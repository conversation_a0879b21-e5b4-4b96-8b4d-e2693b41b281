import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { RouterLink } from '@angular/router';
import { IonContent, IonHeader, IonTitle, IonToolbar, IonCardHeader, IonCardContent, IonItem, IonButton, IonLabel, IonCardTitle, IonCard, IonInput, IonIcon } from '@ionic/angular/standalone';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { addIcons } from 'ionicons';
import { keyOutline, carSportOutline, phonePortraitOutline } from 'ionicons/icons';


@Component({
  selector: 'app-login',
  templateUrl: './login.page.html',
  styleUrls: ['./login.page.scss'],
  imports: [

    CommonModule,
    ReactiveFormsModule,
    RouterLink,
    IonCard,
    IonCardTitle,
    IonLabel,
    IonButton,
    IonItem,
    IonCardContent,
    IonCardHeader,
    IonContent,
    IonHeader,
    IonTitle,
    IonToolbar,
    IonInput,
    IonIcon
  ]
})
export class LoginPage implements OnInit {

  loginForm: FormGroup;
  private isNavigating = false;
  isLoading = false;

  constructor(private http: HttpClient, private router: Router, private fb: FormBuilder) {
    addIcons({ keyOutline, carSportOutline, phonePortraitOutline });

    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required]]
    });
  }


  login() {
    if (this.isNavigating || this.loginForm.invalid || this.isLoading) return;

    this.isLoading = true;
    const formValue = this.loginForm.value;

    console.log('=== FRONTEND LOGIN ATTEMPT ===');
    console.log('Form data:', formValue);

    const loginData = {
      email: formValue.email,
      password: formValue.password
    };

    console.log('Sending login data:', loginData);

    this.http.post('http://localhost:3001/api/auth/login', loginData).subscribe({
      next: (res: any) => {
        if (res.success && res.data.user.role === 'driver') {
          localStorage.setItem('token', res.data.token);
          localStorage.setItem('user', JSON.stringify(res.data.user));
          this.isLoading = false;
          this.navigateToRoute('/driver-dashboard');
        } else if (res.success && res.data.user.role === 'admin') {
          localStorage.setItem('token', res.data.token);
          localStorage.setItem('user', JSON.stringify(res.data.user));
          this.isLoading = false;
          this.navigateToRoute('/admin-dashboard');
        } else {
          this.isLoading = false;
          this.isNavigating = false;
          alert('Invalid login');
        }
      },
      error: (err) => {
        console.error('=== LOGIN ERROR ===');
        console.error('Full error object:', err);
        console.error('Error status:', err.status);
        console.error('Error message:', err.error);
        console.error('Error body:', err.error?.message);

        this.isLoading = false;
        this.isNavigating = false;

        let errorMessage = 'Login failed';
        if (err.error?.message) {
          errorMessage = err.error.message;
        } else if (err.status === 400) {
          errorMessage = 'Invalid email or password';
        } else if (err.status === 0) {
          errorMessage = 'Cannot connect to server. Please check if backend is running.';
        }

        alert(errorMessage);
      }
    });
  }

  private navigateToRoute(route: string) {
    if (this.isNavigating) return;
    this.isNavigating = true;

    setTimeout(() => {
      this.router.navigate([route], { replaceUrl: true }).then(() => {
        this.isNavigating = false;
      }).catch(() => {
        this.isNavigating = false;
      });
    }, 100);
  }
  ngOnInit() {
  }

}
