import{a,b as c,c as o,d as i,e as r,g as l,h as s,n as m,r as p,s as d,t as u,u as f}from"./chunk-EOVFBHGK.js";import"./chunk-26IZYKKN.js";import"./chunk-FYMTVGAO.js";import"./chunk-ASVRUY3W.js";import"./chunk-RC5DY42Y.js";import"./chunk-JGEA7HOG.js";import"./chunk-R5HL6L5F.js";import"./chunk-4WFVMWDK.js";import"./chunk-M2X7KQLB.js";import"./chunk-2YSZFPCQ.js";import"./chunk-57YRIO75.js";import"./chunk-FNBMHXHF.js";import"./chunk-C5RQ2IC2.js";import"./chunk-42C7ZIID.js";import"./chunk-JHI3MBHO.js";var C=(()=>{let e=class e{constructor(){}ngOnInit(){}};e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=c({type:e,selectors:[["app-login"]],decls:9,vars:2,consts:[[3,"translucent"],[3,"fullscreen"],["collapse","condense"],["size","large"]],template:function(t,v){t&1&&(i(0,"ion-header",0)(1,"ion-toolbar")(2,"ion-title"),l(3,"login"),r()()(),i(4,"ion-content",1)(5,"ion-header",2)(6,"ion-toolbar")(7,"ion-title",3),l(8,"login"),r()()()()),t&2&&(o("translucent",!0),a(4),o("fullscreen",!0))},dependencies:[p,d,u,f,s,m],encapsulation:2});let n=e;return n})();export{C as LoginPage};
