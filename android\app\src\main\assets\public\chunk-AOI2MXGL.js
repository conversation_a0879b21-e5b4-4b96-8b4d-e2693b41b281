import{a as l,b as d,c as n,d as r,e as i,g as a,h as s,n as c,r as m,s as p,t as u,u as f}from"./chunk-EOVFBHGK.js";import"./chunk-26IZYKKN.js";import"./chunk-FYMTVGAO.js";import"./chunk-ASVRUY3W.js";import"./chunk-RC5DY42Y.js";import"./chunk-JGEA7HOG.js";import"./chunk-R5HL6L5F.js";import"./chunk-4WFVMWDK.js";import"./chunk-M2X7KQLB.js";import"./chunk-2YSZFPCQ.js";import"./chunk-57YRIO75.js";import"./chunk-FNBMHXHF.js";import"./chunk-C5RQ2IC2.js";import"./chunk-42C7ZIID.js";import"./chunk-JHI3MBHO.js";var x=(()=>{let e=class e{constructor(){}ngOnInit(){}};e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=d({type:e,selectors:[["app-driver-dashboard"]],decls:9,vars:2,consts:[[3,"translucent"],[3,"fullscreen"],["collapse","condense"],["size","large"]],template:function(t,h){t&1&&(r(0,"ion-header",0)(1,"ion-toolbar")(2,"ion-title"),a(3,"driver-dashboard"),i()()(),r(4,"ion-content",1)(5,"ion-header",2)(6,"ion-toolbar")(7,"ion-title",3),a(8,"driver-dashboard"),i()()()()),t&2&&(n("translucent",!0),l(4),n("fullscreen",!0))},dependencies:[m,p,u,f,s,c],encapsulation:2});let o=e;return o})();export{x as DriverDashboardPage};
