// Enhanced Animations
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

// Gradient toolbar
.gradient-toolbar {
  --background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --color: white;
  position: relative;
  overflow: hidden;
}

.gradient-toolbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: shimmer 3s infinite;
}

.animated-title {
  animation: slideInDown 0.6s ease;
  font-weight: 600;
  text-align: center;
}

// Login content
.login-content {
  --background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
}

.login-container {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

// Login card
.login-card {
  border-radius: 24px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  animation: fadeInUp 0.8s ease;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  backdrop-filter: blur(20px);
}

.login-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(102, 126, 234, 0.1),
    transparent
  );
  animation: shimmer 2s infinite;
}

.login-title {
  color: #2c3e50;
  font-size: 28px;
  font-weight: 700;
  text-align: center;
  margin-bottom: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.login-subtitle {
  color: #7f8c8d;
  text-align: center;
  margin: 0;
  font-size: 16px;
  margin-bottom: 20px;
}

// Form items
.form-item {
  margin-bottom: 20px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid rgba(102, 126, 234, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  animation: slideInLeft 0.6s ease-out;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    transform: translateY(-3px);
    box-shadow:
      0 12px 30px rgba(0, 0, 0, 0.1),
      0 0 0 1px rgba(102, 126, 234, 0.2);
    background: rgba(255, 255, 255, 1);
    border-color: rgba(102, 126, 234, 0.3);
  }

  &:focus-within {
    border-color: #667eea;
    background: rgba(255, 255, 255, 1);
    box-shadow:
      0 0 0 4px rgba(102, 126, 234, 0.1),
      0 8px 25px rgba(102, 126, 234, 0.15);
    transform: translateY(-2px);
  }

  &:focus-within::before {
    opacity: 1;
  }

  ion-label {
    color: #2c3e50;
    font-weight: 600;
    font-size: 14px;
  }

  ion-input {
    --color: #2c3e50;
    --placeholder-color: #95a5a6;
    font-weight: 500;
  }
}

// Login button
.login-button {
  margin-top: 30px;
  height: 56px;
  border-radius: 28px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 700;
  font-size: 16px;
  position: relative;
  overflow: hidden;
  animation: fadeInUp 0.6s ease-out 0.4s both;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.3),
      transparent
    );
    transition: left 0.5s ease;
  }

  &:hover:not([disabled]) {
    transform: translateY(-3px);
    box-shadow:
      0 15px 35px rgba(102, 126, 234, 0.4),
      0 0 0 1px rgba(255, 255, 255, 0.3);
    animation: float 2s ease-in-out infinite;
  }

  &:hover:not([disabled])::before {
    left: 100%;
  }

  &:active {
    transform: translateY(-1px);
    animation: pulse 0.3s ease;
  }

  &[disabled] {
    opacity: 0.6;
    background: linear-gradient(135deg, #bdc3c7 0%, #95a5a6 100%);
    animation: none;
  }

  span {
    animation: fadeInLeft 0.6s ease;
    position: relative;
    z-index: 1;
  }
}

// Forgot password link
.forgot-password-link {
  text-align: center;
  margin: 15px 0;
}

.forgot-link-text {
  color: #dc3545;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  display: inline-flex;
  align-items: center;
  gap: 5px;
  transition: all 0.3s ease;

  &:hover {
    color: #c82333;
    text-decoration: underline;
    transform: translateY(-1px);
  }

  ion-icon {
    font-size: 1rem;
  }
}

// Signup link
.signup-link {
  text-align: center;
  margin-top: 30px;
  padding: 20px;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  border-radius: 12px;
  border-left: 4px solid #667eea;

  p {
    margin: 0;
    color: #2c3e50;
    font-size: 14px;
  }

  .signup-link-text {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;

    &:hover {
      color: #764ba2;
      text-decoration: underline;
    }
  }
}

// Alternative Login Section
.alternative-login-section {
  margin-top: 24px;
}

.divider {
  text-align: center;
  margin: 20px 0;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e0e0e0;
  }

  span {
    background: white;
    padding: 0 16px;
    color: #999;
    font-size: 0.9rem;
    font-weight: 500;
  }
}

.otp-login-btn {
  --border-color: #28a745;
  --color: #28a745;
  --border-radius: 12px;
  --padding-top: 14px;
  --padding-bottom: 14px;
  font-weight: 500;
  margin-top: 12px;
  transition: all 0.3s ease;

  &:hover {
    --background: rgba(40, 167, 69, 0.1);
    transform: translateY(-2px);
  }
}

.driver-login-btn {
  --border-color: #667eea;
  --color: #667eea;
  --border-radius: 12px;
  --padding-top: 14px;
  --padding-bottom: 14px;
  font-weight: 500;
  margin-top: 12px;
  transition: all 0.3s ease;

  &:hover {
    --background: rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
  }
}

// Animations
@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// Mobile responsiveness
@media (max-width: 768px) {
  .login-container {
    padding: 10px;
  }

  .login-card {
    margin: 10px;
  }

  .login-title {
    font-size: 24px;
  }
}