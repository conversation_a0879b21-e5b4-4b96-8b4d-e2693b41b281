// Gradient toolbar
.gradient-toolbar {
  --background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --color: white;
}

.animated-title {
  animation: slideInDown 0.6s ease;
  font-weight: 600;
  text-align: center;
}

// Login content
.login-content {
  --background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
}

.login-container {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

// Login card
.login-card {
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  animation: slideInDown 0.8s ease;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-title {
  color: #2c3e50;
  font-size: 28px;
  font-weight: 700;
  text-align: center;
  margin-bottom: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.login-subtitle {
  color: #7f8c8d;
  text-align: center;
  margin: 0;
  font-size: 16px;
  margin-bottom: 20px;
}

// Form items
.form-item {
  margin-bottom: 20px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.8);
  border: 2px solid transparent;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.95);
  }

  &:focus-within {
    border-color: #667eea;
    background: rgba(255, 255, 255, 1);
  }

  ion-label {
    color: #2c3e50;
    font-weight: 500;
  }

  ion-input {
    --color: #2c3e50;
    --placeholder-color: #95a5a6;
  }
}

// Login button
.login-button {
  margin-top: 30px;
  height: 50px;
  border-radius: 25px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 16px;

  &:hover:not([disabled]) {
    transform: translateY(-2px);
    box-shadow: 0 12px 30px rgba(102, 126, 234, 0.4);
  }

  &:active {
    transform: translateY(0);
  }

  &[disabled] {
    opacity: 0.6;
    background: linear-gradient(135deg, #bdc3c7 0%, #95a5a6 100%);
  }

  span {
    animation: fadeInLeft 0.6s ease;
  }
}

// Forgot password link
.forgot-password-link {
  text-align: center;
  margin: 15px 0;
}

.forgot-link-text {
  color: #dc3545;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  display: inline-flex;
  align-items: center;
  gap: 5px;
  transition: all 0.3s ease;

  &:hover {
    color: #c82333;
    text-decoration: underline;
    transform: translateY(-1px);
  }

  ion-icon {
    font-size: 1rem;
  }
}

// Signup link
.signup-link {
  text-align: center;
  margin-top: 30px;
  padding: 20px;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  border-radius: 12px;
  border-left: 4px solid #667eea;

  p {
    margin: 0;
    color: #2c3e50;
    font-size: 14px;
  }

  .signup-link-text {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;

    &:hover {
      color: #764ba2;
      text-decoration: underline;
    }
  }
}

// Alternative Login Section
.alternative-login-section {
  margin-top: 24px;
}

.divider {
  text-align: center;
  margin: 20px 0;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e0e0e0;
  }

  span {
    background: white;
    padding: 0 16px;
    color: #999;
    font-size: 0.9rem;
    font-weight: 500;
  }
}

.otp-login-btn {
  --border-color: #28a745;
  --color: #28a745;
  --border-radius: 12px;
  --padding-top: 14px;
  --padding-bottom: 14px;
  font-weight: 500;
  margin-top: 12px;
  transition: all 0.3s ease;

  &:hover {
    --background: rgba(40, 167, 69, 0.1);
    transform: translateY(-2px);
  }
}

.driver-login-btn {
  --border-color: #667eea;
  --color: #667eea;
  --border-radius: 12px;
  --padding-top: 14px;
  --padding-bottom: 14px;
  font-weight: 500;
  margin-top: 12px;
  transition: all 0.3s ease;

  &:hover {
    --background: rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
  }
}

// Animations
@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// Mobile responsiveness
@media (max-width: 768px) {
  .login-container {
    padding: 10px;
  }

  .login-card {
    margin: 10px;
  }

  .login-title {
    font-size: 24px;
  }
}