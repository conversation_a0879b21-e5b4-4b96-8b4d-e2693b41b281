console.log('Starting backend test...');

// Simple test to check if server is running
const net = require('net');

function checkPort() {
  const client = new net.Socket();

  client.connect(3001, 'localhost', function() {
    console.log('✅ Port 3001 is open - server is running!');
    client.destroy();

    // Now test the actual API
    testAPI();
  });

  client.on('error', function(err) {
    console.log('❌ Port 3001 is not accessible:', err.message);
    console.log('The backend server may not be running.');
  });
}

function testAPI() {
  const http = require('http');

  console.log('Testing API endpoints...');

  // Test health endpoint
  const healthOptions = {
    hostname: 'localhost',
    port: 3001,
    path: '/api/health',
    method: 'GET'
  };

  const healthReq = http.request(healthOptions, (res) => {
    console.log(`Health check status: ${res.statusCode}`);

    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      console.log('Health response:', data);
      testLogin();
    });
  });

  healthReq.on('error', (err) => {
    console.error('❌ Health check failed:', err.message);
  });

  healthReq.end();
}

function testLogin() {
  const http = require('http');
  console.log('\nTesting login endpoint...');

  const postData = JSON.stringify({
    email: '<EMAIL>',
    password: '123456'
  });

  const options = {
    hostname: 'localhost',
    port: 3001,
    path: '/api/auth/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  const req = http.request(options, (res) => {
    console.log(`Login response status: ${res.statusCode}`);

    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      console.log('Login response:', data);
      if (res.statusCode === 200) {
        console.log('✅ Login test successful!');
      } else {
        console.log('❌ Login test failed');
      }
    });
  });

  req.on('error', (err) => {
    console.error('❌ Login test failed:', err.message);
  });

  req.write(postData);
  req.end();
}

checkPort();
