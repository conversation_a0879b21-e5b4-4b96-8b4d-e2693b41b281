// Load environment variables
require('dotenv').config();

const express = require('express');
const cors = require('cors');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Twilio SMS service
const twilio = require('twilio');

const app = express();

// Twilio Configuration
const TWILIO_ACCOUNT_SID = process.env.TWILIO_ACCOUNT_SID || 'your_twilio_account_sid';
const TWILIO_AUTH_TOKEN = process.env.TWILIO_AUTH_TOKEN || 'your_twilio_auth_token';
const TWILIO_PHONE_NUMBER = process.env.TWILIO_PHONE_NUMBER || '+**********';

// Initialize Twilio client
let twilioClient = null;
try {
  if (TWILIO_ACCOUNT_SID !== 'your_twilio_account_sid' && TWILIO_AUTH_TOKEN !== 'your_twilio_auth_token') {
    twilioClient = twilio(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN);
    console.log('✅ Twilio SMS service initialized');
  } else {
    console.log('⚠️ Twilio credentials not configured - using console simulation');
  }
} catch (error) {
  console.log('❌ Twilio initialization failed - using console simulation');
}

// In-memory storage (replace with MongoDB later)
let users = [];
let trips = [];
let companies = [];
let subscriptions = [];
let otpStorage = []; // Store OTPs temporarily

// Initialize test users
async function initializeTestUsers() {
  try {
    // Create test admin user
    const adminPassword = await bcrypt.hash('123456', 10);
    const testAdmin = {
      id: 'admin_' + Date.now(),
      name: 'Test Admin',
      email: '<EMAIL>',
      password: adminPassword,
      phone: '**********',
      role: 'admin',
      companyName: 'Test Company',
      address: 'Test Address',
      isActive: true,
      status: 'active',
      subscriptionStatus: 'trial',
      trialStartDate: new Date(),
      trialEndDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 days trial
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Create test driver user
    const driverPassword = await bcrypt.hash('123456', 10);
    const testDriver = {
      id: 'driver_' + Date.now(),
      name: 'Test Driver',
      email: '<EMAIL>',
      password: driverPassword,
      phone: '**********',
      role: 'driver',
      companyName: 'Test Company',
      aadhaarNumber: '**********12',
      licenseNumber: 'DL**********',
      vehicleDetails: {
        vehicleNumber: 'KA01AB1234',
        vehicleType: 'car',
        vehicleModel: 'Maruti Swift',
        fuelType: 'petrol'
      },
      isActive: true,
      status: 'active',
      subscriptionStatus: 'trial',
      trialStartDate: new Date(),
      trialEndDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 days trial
      createdAt: new Date(),
      updatedAt: new Date(),
      registeredByAdmin: false
    };

    users.push(testAdmin, testDriver);
    console.log('✅ Test users initialized:');
    console.log('   Admin: <EMAIL> / 123456');
    console.log('   Driver: <EMAIL> / 123456');
    console.log('   Driver Phone: ********** / 123456');
  } catch (error) {
    console.error('❌ Error initializing test users:', error);
  }
}

// OTP utility functions
function generateOTP() {
  return Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit OTP
}

function storeOTP(phone, otp, purpose = 'registration') {
  // Remove any existing OTP for this phone and purpose
  otpStorage = otpStorage.filter(item => !(item.phone === phone && item.purpose === purpose));

  // Store new OTP with 5-minute expiry
  otpStorage.push({
    phone,
    otp,
    purpose,
    createdAt: new Date(),
    expiresAt: new Date(Date.now() + 5 * 60 * 1000) // 5 minutes
  });

  console.log(`OTP ${otp} generated for ${phone} (${purpose})`);
}

function verifyOTP(phone, otp, purpose = 'registration') {
  const otpRecord = otpStorage.find(item =>
    item.phone === phone &&
    item.otp === otp &&
    item.purpose === purpose &&
    new Date() < item.expiresAt
  );

  if (otpRecord) {
    // Remove used OTP
    otpStorage = otpStorage.filter(item => item !== otpRecord);
    return true;
  }
  return false;
}

// SMS sending function with Twilio integration
async function sendSMS(phone, message) {
  try {
    if (twilioClient) {
      // Real SMS using Twilio
      const result = await twilioClient.messages.create({
        body: message,
        from: TWILIO_PHONE_NUMBER,
        to: `+91${phone}` // Adding India country code
      });
      console.log(`✅ SMS sent successfully to ${phone} - SID: ${result.sid}`);
      return { success: true, sid: result.sid };
    } else {
      // Fallback to console simulation
      console.log(`📱 SMS SIMULATION to ${phone}: ${message}`);
      console.log(`🔧 To enable real SMS, configure Twilio credentials in environment variables`);
      return { success: true, simulation: true };
    }
  } catch (error) {
    console.error(`❌ SMS sending failed to ${phone}:`, error.message);
    // Fallback to console simulation on error
    console.log(`📱 SMS FALLBACK to ${phone}: ${message}`);
    return { success: false, error: error.message, fallback: true };
  }
}

// Middleware
app.use(cors({
  origin: ['http://localhost:8100', 'http://localhost:8101', 'http://localhost:8102'],
  credentials: true
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Static files
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Ensure uploads directory exists
const uploadsDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// File upload configuration
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    cb(null, Date.now() + '-' + file.originalname);
  }
});
const upload = multer({ storage });

// JWT Secret
const JWT_SECRET = 'driversetu_super_secret_jwt_key_2024_development';

// Auth middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ success: false, message: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ success: false, message: 'Invalid token' });
    }
    req.user = user;
    next();
  });
};

// Health check
app.get('/api/health', (req, res) => {
  console.log('Health check requested');
  res.json({
    status: 'OK',
    message: 'DriverSetu API is running',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Debug endpoint to list all users (for development only)
app.get('/api/debug/users', (req, res) => {
  const usersWithoutPasswords = users.map(user => {
    const { password, ...userWithoutPassword } = user;
    return userWithoutPassword;
  });
  res.json({
    success: true,
    users: usersWithoutPasswords,
    count: users.length
  });
});

// OTP routes
app.post('/api/otp/send', async (req, res) => {
  try {
    const { phone, purpose = 'registration' } = req.body;
    console.log('📤 OTP Send Request:', { phone, purpose });

    if (!phone) {
      return res.status(400).json({ success: false, message: 'Phone number is required' });
    }

    // Check if user already exists for registration
    if (purpose === 'registration') {
      const existingUser = users.find(u => u.phone === phone);
      if (existingUser) {
        return res.status(400).json({ success: false, message: 'User with this phone number already exists' });
      }
    }

    // Check if user exists for forgot password or login
    if (purpose === 'forgot-password' || purpose === 'change-password' || purpose === 'login') {
      const existingUser = users.find(u => u.phone === phone);
      if (!existingUser) {
        return res.status(400).json({ success: false, message: 'User with this phone number not found' });
      }
    }

    const otp = generateOTP();
    storeOTP(phone, otp, purpose);
    console.log('🔐 Generated OTP:', otp, 'for phone:', phone);

    const message = `Your DriverSetu OTP is: ${otp}. Valid for 5 minutes. Do not share with anyone.`;
    const smsResult = await sendSMS(phone, message);

    if (smsResult.success) {
      res.json({
        success: true,
        message: 'OTP sent successfully',
        data: { phone, purpose, smsStatus: smsResult }
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Failed to send SMS, but OTP is generated. Check console for OTP.',
        data: { phone, purpose, error: smsResult.error }
      });
    }
  } catch (error) {
    console.error('Send OTP error:', error);
    res.status(500).json({ success: false, message: 'Failed to send OTP' });
  }
});

app.post('/api/otp/verify', (req, res) => {
  try {
    const { phone, otp, purpose = 'registration' } = req.body;
    console.log('🔍 OTP Verify Request:', { phone, otp, purpose });
    console.log('📱 Current OTP Storage:', otpStorage);

    if (!phone || !otp) {
      console.log('❌ Missing phone or OTP');
      return res.status(400).json({ success: false, message: 'Phone number and OTP are required' });
    }

    const isValid = verifyOTP(phone, otp, purpose);
    console.log('✅ OTP Verification Result:', isValid);

    if (isValid) {
      res.json({
        success: true,
        message: 'OTP verified successfully',
        data: { phone, purpose }
      });
    } else {
      res.status(400).json({
        success: false,
        message: 'Invalid or expired OTP'
      });
    }
  } catch (error) {
    console.error('Verify OTP error:', error);
    res.status(500).json({ success: false, message: 'Failed to verify OTP' });
  }
});

// Auth routes
app.post('/api/auth/register', async (req, res) => {
  try {
    console.log('Registration attempt:', req.body);

    const { name, email, password, phone, role, licenseNumber, vehicleDetails, address, companyName } = req.body;

    // OTP verification temporarily disabled as requested by user
    // TODO: Re-enable OTP verification later
    
    // Check if user exists
    const existingUser = users.find(u => u.email === email);
    if (existingUser) {
      return res.status(400).json({ success: false, message: 'User already exists' });
    }
    
    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);
    
    // Create user
    const user = {
      id: Date.now().toString(),
      name,
      email,
      password: hashedPassword,
      phone,
      role: role || 'driver',
      licenseNumber,
      vehicleDetails,
      address,
      companyName,
      isActive: true,
      createdAt: new Date(),
      trialStartDate: new Date(),
      trialEndDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000) // 3 days trial
    };
    
    users.push(user);
    
    // Generate token
    const token = jwt.sign(
      { id: user.id, email: user.email, role: user.role, companyName: user.companyName },
      JWT_SECRET,
      { expiresIn: '7d' }
    );
    
    // Remove password from response
    const { password: _, ...userResponse } = user;
    
    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: {
        token,
        user: userResponse
      }
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ success: false, message: 'Registration failed' });
  }
});

app.post('/api/auth/login', async (req, res) => {
  try {
    console.log('Login attempt:', req.body);

    const { email, password, phone, otpVerified } = req.body;

    // Handle OTP-based login
    if (otpVerified && phone) {
      console.log('OTP-based login for phone:', phone);
      const user = users.find(u => u.phone === phone);

      if (!user) {
        console.log('User not found for phone:', phone);
        return res.status(400).json({ success: false, message: 'User not found' });
      }

      // Generate JWT token for OTP-verified user
      const token = jwt.sign(
        {
          id: user.id,
          email: user.email,
          role: user.role,
          companyName: user.companyName || user.name
        },
        JWT_SECRET,
        { expiresIn: '7d' }
      );

      console.log('OTP login successful for:', user.name);
      return res.json({
        success: true,
        message: 'Login successful',
        data: {
          token,
          user: {
            id: user.id,
            name: user.name,
            email: user.email,
            phone: user.phone,
            role: user.role,
            companyName: user.companyName,
            address: user.address,
            isActive: user.isActive,
            status: user.status,
            subscriptionStatus: user.subscriptionStatus,
            trialStartDate: user.trialStartDate,
            trialEndDate: user.trialEndDate,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt
          },
          trialDaysLeft: user.trialEndDate ? Math.max(0, Math.ceil((new Date(user.trialEndDate) - new Date()) / (1000 * 60 * 60 * 24))) : 0,
          hasActiveSubscription: user.subscriptionStatus === 'active'
        }
      });
    }

    // Handle traditional email/password login
    const identifier = email || phone;
    if (!identifier || !password) {
      return res.status(400).json({ success: false, message: 'Email/phone and password are required' });
    }

    // Find user by email or phone (for drivers)
    const user = users.find(u => u.email === identifier || u.phone === identifier);
    console.log ('User found:', user ? `${user.name} (${user.role})` : 'None');

    if (!user) {
      console.log('User not found for:', identifier);
      return res.status(400).json({ success: false, message: 'Invalid credentials' });
    }

    // Check password
    console.log('Checking password for user:', user.name);
    const isValidPassword = await bcrypt.compare(password, user.password);
    console.log('Password valid:', isValidPassword);

    if (!isValidPassword) {
      console.log('Invalid password for user:', user.name);
      return res.status(400).json({ success: false, message: 'Invalid credentials' });
    }
    
    // Initialize variables for response
    const now = new Date();
    let trialExpired = false;
    let hasActiveSubscription = false;

    // Check subscription status (skip for admin-registered drivers)
    if (user.role === 'driver' && user.registeredByAdmin) {
      // Admin-registered drivers don't need individual subscriptions
      // Their access is controlled by admin's subscription
      const adminUser = users.find(u => u.companyName === user.companyName && u.role === 'admin');
      if (adminUser) {
        hasActiveSubscription = subscriptions.some(s =>
          s.userId === adminUser.id && s.status === 'active' && new Date(s.endDate) > new Date()
        );
        if (!hasActiveSubscription) {
          return res.status(402).json({
            success: false,
            message: 'Company subscription expired. Contact your admin.',
            requiresSubscription: true
          });
        }
      }
      // For admin-registered drivers, set hasActiveSubscription to true if admin has subscription
      hasActiveSubscription = true;
    } else {
      // Check if trial expired and no subscription for regular users
      trialExpired = user.trialEndDate ? now > new Date(user.trialEndDate) : false;
      hasActiveSubscription = subscriptions.some(s =>
        s.userId === user.id && s.status === 'active' && new Date(s.endDate) > now
      );

      if (trialExpired && !hasActiveSubscription) {
        return res.status(402).json({
          success: false,
          message: 'Trial expired. Please subscribe to continue.',
          requiresSubscription: true
        });
      }
    }
    
    // Generate token
    const token = jwt.sign(
      { id: user.id, email: user.email, role: user.role, companyName: user.companyName },
      JWT_SECRET,
      { expiresIn: '7d' }
    );
    
    // Remove password from response
    const { password: _, ...userResponse } = user;
    
    res.json({
      success: true,
      message: 'Login successful',
      data: {
        token,
        user: userResponse,
        trialDaysLeft: (trialExpired || !user.trialEndDate) ? 0 : Math.ceil((new Date(user.trialEndDate) - now) / (1000 * 60 * 60 * 24)),
        hasActiveSubscription
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ success: false, message: 'Login failed' });
  }
});

// Forgot Password route
app.post('/api/auth/forgot-password', async (req, res) => {
  try {
    const { phone, otp, newPassword } = req.body;

    if (!phone || !otp || !newPassword) {
      return res.status(400).json({
        success: false,
        message: 'Phone number, OTP, and new password are required'
      });
    }

    // Verify OTP
    const isOTPValid = verifyOTP(phone, otp, 'forgot-password');
    if (!isOTPValid) {
      return res.status(400).json({ success: false, message: 'Invalid or expired OTP' });
    }

    // Find user by phone
    const user = users.find(u => u.phone === phone);
    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    user.password = hashedPassword;

    res.json({
      success: true,
      message: 'Password reset successfully'
    });
  } catch (error) {
    console.error('Forgot password error:', error);
    res.status(500).json({ success: false, message: 'Failed to reset password' });
  }
});

// Change Password route
app.post('/api/auth/change-password', authenticateToken, async (req, res) => {
  try {
    const { phone, otp, newPassword } = req.body;

    if (!phone || !otp || !newPassword) {
      return res.status(400).json({
        success: false,
        message: 'Phone number, OTP, and new password are required'
      });
    }

    // Verify OTP
    const isOTPValid = verifyOTP(phone, otp, 'change-password');
    if (!isOTPValid) {
      return res.status(400).json({ success: false, message: 'Invalid or expired OTP' });
    }

    // Find user by ID and phone
    const user = users.find(u => u.id === req.user.id && u.phone === phone);
    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found or phone mismatch' });
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    user.password = hashedPassword;

    res.json({
      success: true,
      message: 'Password changed successfully'
    });
  } catch (error) {
    console.error('Change password error:', error);
    res.status(500).json({ success: false, message: 'Failed to change password' });
  }
});

// Trip routes
app.post('/api/trips/submit', authenticateToken, upload.array('billImages', 5), (req, res) => {
  try {
    console.log('Trip submission:', req.user.email);
    
    const { date, vehicle, fromLocation, toLocation, km, amount, expenses, remarks } = req.body;
    
    const trip = {
      id: Date.now().toString(),
      driverId: req.user.id,
      driver: users.find(u => u.id === req.user.id),
      date: new Date(date),
      vehicle,
      fromLocation,
      toLocation,
      km: parseFloat(km),
      amount: parseFloat(amount),
      expenses: typeof expenses === 'string' ? JSON.parse(expenses) : expenses,
      remarks,
      status: 'pending',
      billImages: req.files ? req.files.map(f => ({ filename: f.filename, path: f.path })) : [],
      createdAt: new Date()
    };
    
    trips.push(trip);
    
    res.json({
      success: true,
      message: 'Trip submitted successfully',
      data: { trip }
    });
  } catch (error) {
    console.error('Trip submission error:', error);
    res.status(500).json({ success: false, message: 'Trip submission failed' });
  }
});

app.get('/api/trips', authenticateToken, (req, res) => {
  try {
    let userTrips = trips;
    
    if (req.user.role === 'driver') {
      userTrips = trips.filter(t => t.driverId === req.user.id);
    }
    
    res.json({
      success: true,
      data: { trips: userTrips }
    });
  } catch (error) {
    console.error('Get trips error:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch trips' });
  }
});

app.patch('/api/trips/:id/status', authenticateToken, (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    
    const trip = trips.find(t => t.id === id);
    if (!trip) {
      return res.status(404).json({ success: false, message: 'Trip not found' });
    }
    
    trip.status = status;
    trip.updatedAt = new Date();
    
    res.json({
      success: true,
      message: `Trip ${status} successfully`,
      data: { trip }
    });
  } catch (error) {
    console.error('Update trip status error:', error);
    res.status(500).json({ success: false, message: 'Failed to update trip status' });
  }
});

// Stats route
app.get('/api/trips/stats', authenticateToken, (req, res) => {
  try {
    const totalTrips = trips.length;
    const pendingTrips = trips.filter(t => t.status === 'pending').length;
    const totalRevenue = trips.filter(t => t.status === 'approved').reduce((sum, t) => sum + t.amount, 0);
    const totalDrivers = users.filter(u => u.role === 'driver').length;
    
    res.json({
      success: true,
      data: {
        stats: {
          totalTrips,
          pendingTrips,
          totalRevenue,
          totalDrivers
        }
      }
    });
  } catch (error) {
    console.error('Get stats error:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch stats' });
  }
});

// User routes
app.get('/api/users/drivers', authenticateToken, (req, res) => {
  try {
    const drivers = users.filter(u => u.role === 'driver').map(u => {
      const { password, ...driver } = u;
      return driver;
    });

    res.json({
      success: true,
      data: { drivers }
    });
  } catch (error) {
    console.error('Get drivers error:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch drivers' });
  }
});

// Subscription routes
app.post('/api/subscriptions/create', authenticateToken, (req, res) => {
  try {
    const { planType, duration, companyName } = req.body;

    // Subscription plans
    const plans = {
      basic: { price: 999, features: ['Up to 10 drivers', 'Basic reporting', 'Email support'] },
      premium: { price: 1999, features: ['Up to 50 drivers', 'Advanced analytics', 'Priority support', 'Custom reports'] },
      enterprise: { price: 4999, features: ['Unlimited drivers', 'Full analytics suite', '24/7 support', 'API access', 'Custom integrations'] }
    };

    const plan = plans[planType];
    if (!plan) {
      return res.status(400).json({ success: false, message: 'Invalid plan type' });
    }

    const subscription = {
      id: Date.now().toString(),
      userId: req.user.id,
      companyName: companyName || users.find(u => u.id === req.user.id)?.companyName,
      planType,
      price: plan.price,
      duration: duration || 30, // days
      features: plan.features,
      status: 'pending_payment',
      startDate: new Date(),
      endDate: new Date(Date.now() + (duration || 30) * 24 * 60 * 60 * 1000),
      createdAt: new Date(),
      paymentId: null
    };

    subscriptions.push(subscription);

    res.json({
      success: true,
      message: 'Subscription created successfully',
      data: {
        subscription,
        paymentRequired: true,
        amount: plan.price
      }
    });
  } catch (error) {
    console.error('Create subscription error:', error);
    res.status(500).json({ success: false, message: 'Failed to create subscription' });
  }
});

app.get('/api/subscriptions/plans', (req, res) => {
  try {
    const plans = {
      basic: {
        name: 'Basic Plan',
        price: 999,
        duration: 30,
        features: ['Up to 10 drivers', 'Basic trip tracking', 'Email support', 'Basic reports'],
        recommended: false
      },
      premium: {
        name: 'Premium Plan',
        price: 1999,
        duration: 30,
        features: ['Up to 50 drivers', 'Advanced analytics', 'Priority support', 'Custom reports', 'Real-time tracking'],
        recommended: true
      },
      enterprise: {
        name: 'Enterprise Plan',
        price: 4999,
        duration: 30,
        features: ['Unlimited drivers', 'Full analytics suite', '24/7 support', 'API access', 'Custom integrations', 'White-label solution'],
        recommended: false
      }
    };

    res.json({
      success: true,
      data: { plans }
    });
  } catch (error) {
    console.error('Get plans error:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch plans' });
  }
});

app.get('/api/subscriptions/my', authenticateToken, (req, res) => {
  try {
    const userSubscriptions = subscriptions.filter(s => s.userId === req.user.id);
    const user = users.find(u => u.id === req.user.id);

    const now = new Date();
    const trialDaysLeft = Math.max(0, Math.ceil((new Date(user.trialEndDate) - now) / (1000 * 60 * 60 * 24)));
    const activeSubscription = userSubscriptions.find(s =>
      s.status === 'active' && new Date(s.endDate) > now
    );

    res.json({
      success: true,
      data: {
        subscriptions: userSubscriptions,
        activeSubscription,
        trialDaysLeft,
        isTrialActive: trialDaysLeft > 0,
        hasActiveSubscription: !!activeSubscription
      }
    });
  } catch (error) {
    console.error('Get my subscriptions error:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch subscriptions' });
  }
});

// Payment simulation route (replace with actual payment gateway)
app.post('/api/payments/process', authenticateToken, (req, res) => {
  try {
    const { subscriptionId, paymentMethod, amount } = req.body;

    const subscription = subscriptions.find(s => s.id === subscriptionId && s.userId === req.user.id);
    if (!subscription) {
      return res.status(404).json({ success: false, message: 'Subscription not found' });
    }

    // Simulate payment processing
    const paymentId = 'pay_' + Date.now();

    // Update subscription status
    subscription.status = 'active';
    subscription.paymentId = paymentId;
    subscription.paidAt = new Date();

    res.json({
      success: true,
      message: 'Payment processed successfully',
      data: {
        paymentId,
        subscription,
        message: 'Your subscription is now active!'
      }
    });
  } catch (error) {
    console.error('Process payment error:', error);
    res.status(500).json({ success: false, message: 'Payment processing failed' });
  }
});

// Company management routes
app.post('/api/companies/register', (req, res) => {
  try {
    const { companyName, adminName, adminEmail, adminPassword, phone, address, gstNumber } = req.body;

    // Check if company already exists
    const existingCompany = companies.find(c => c.companyName === companyName);
    if (existingCompany) {
      return res.status(400).json({ success: false, message: 'Company already registered' });
    }

    const company = {
      id: Date.now().toString(),
      companyName,
      adminName,
      adminEmail,
      phone,
      address,
      gstNumber,
      status: 'active',
      trialStartDate: new Date(),
      trialEndDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 days trial
      createdAt: new Date()
    };

    companies.push(company);

    res.status(201).json({
      success: true,
      message: 'Company registered successfully',
      data: { company }
    });
  } catch (error) {
    console.error('Company registration error:', error);
    res.status(500).json({ success: false, message: 'Company registration failed' });
  }
});

app.get('/api/companies', authenticateToken, (req, res) => {
  try {
    // Only admin can view all companies
    if (req.user.role !== 'admin') {
      return res.status(403).json({ success: false, message: 'Access denied' });
    }

    res.json({
      success: true,
      data: { companies }
    });
  } catch (error) {
    console.error('Get companies error:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch companies' });
  }
});

// Admin routes for driver management
app.get('/api/admin/stats', authenticateToken, (req, res) => {
  if (req.user.role !== 'admin') {
    return res.status(403).json({ success: false, message: 'Admin access required' });
  }

  const adminCompany = req.user.companyName;
  const companyDrivers = users.filter(u => u.role === 'driver' && u.companyName === adminCompany);
  const companyTrips = trips.filter(t => {
    const driver = users.find(u => u.id === t.userId);
    return driver && driver.companyName === adminCompany;
  });

  const totalTrips = companyTrips.length;
  const totalDrivers = companyDrivers.length;
  const totalRevenue = companyTrips.reduce((sum, trip) => sum + (trip.amount || 0), 0);
  const totalExpenses = companyTrips.reduce((sum, trip) => {
    const expenses = trip.expenses || {};
    return sum + (expenses.fuel || 0) + (expenses.food || 0) + (expenses.other || 0);
  }, 0);

  res.json({
    success: true,
    data: {
      totalTrips,
      totalDrivers,
      totalRevenue,
      totalExpenses,
      netProfit: totalRevenue - totalExpenses,
      maxDrivers: 20,
      remainingSlots: Math.max(0, 20 - totalDrivers)
    }
  });
});

// Admin - Register driver
app.post('/api/admin/register-driver', authenticateToken, upload.fields([
  { name: 'aadhaarPhoto', maxCount: 1 },
  { name: 'driverPhoto', maxCount: 1 }
]), (req, res) => {
  if (req.user.role !== 'admin') {
    return res.status(403).json({ success: false, message: 'Admin access required' });
  }

  const { name, mobile, aadhaarNumber, vehicleNumber, vehicleType } = req.body;

  // Check if admin has active subscription
  const adminSubscription = subscriptions.find(s =>
    s.userId === req.user.id && s.status === 'active'
  );

  // Also check if admin has trial period (for new admins)
  const isTrialActive = req.user.trialEndDate && new Date(req.user.trialEndDate) > new Date();

  if (!adminSubscription && !isTrialActive) {
    return res.status(403).json({
      success: false,
      message: 'Active subscription or trial required to register drivers'
    });
  }

  // Check driver limit (20 drivers per admin)
  const adminCompany = req.user.companyName;
  const existingDrivers = users.filter(u =>
    u.role === 'driver' && u.companyName === adminCompany
  );

  if (existingDrivers.length >= 20) {
    return res.status(400).json({
      success: false,
      message: 'Maximum 20 drivers allowed per company'
    });
  }

  // Check if mobile number already exists
  if (users.find(u => u.phone === mobile)) {
    return res.status(400).json({
      success: false,
      message: 'Driver with this mobile number already exists'
    });
  }

  // Generate driver ID and password
  const driverId = 'DRV' + Date.now();
  const defaultPassword = mobile.slice(-4) + name.slice(0, 2).toUpperCase();

  const newDriver = {
    id: driverId,
    name,
    email: `${driverId}@${adminCompany.toLowerCase().replace(/\s+/g, '')}.com`,
    phone: mobile,
    password: bcrypt.hashSync(defaultPassword, 10),
    role: 'driver',
    companyName: adminCompany,
    adminId: req.user.id,
    aadhaarNumber,
    vehicleDetails: {
      vehicleNumber,
      vehicleType,
      vehicleModel: req.body.vehicleModel || '',
      fuelType: req.body.fuelType || 'petrol'
    },
    documents: {
      aadhaarPhoto: req.files?.aadhaarPhoto?.[0]?.filename || null,
      driverPhoto: req.files?.driverPhoto?.[0]?.filename || null
    },
    status: 'active',
    registeredBy: req.user.id,
    registeredByAdmin: true,
    registeredAt: new Date().toISOString(),
    // No trial for admin-registered drivers
    trialStartDate: null,
    trialEndDate: null
  };

  users.push(newDriver);

  res.json({
    success: true,
    message: 'Driver registered successfully',
    data: {
      driver: {
        id: newDriver.id,
        name: newDriver.name,
        phone: newDriver.phone,
        vehicleNumber: newDriver.vehicleDetails.vehicleNumber,
        defaultPassword: defaultPassword
      }
    }
  });
});

// Admin - Get all drivers
app.get('/api/admin/drivers', authenticateToken, (req, res) => {
  if (req.user.role !== 'admin') {
    return res.status(403).json({ success: false, message: 'Admin access required' });
  }

  const adminCompany = req.user.companyName;
  const companyDrivers = users.filter(u =>
    u.role === 'driver' && u.companyName === adminCompany
  ).map(driver => ({
    id: driver.id,
    name: driver.name,
    phone: driver.phone,
    aadhaarNumber: driver.aadhaarNumber,
    vehicleDetails: driver.vehicleDetails,
    status: driver.status,
    registeredAt: driver.registeredAt,
    documents: driver.documents
  }));

  res.json({
    success: true,
    data: {
      drivers: companyDrivers,
      totalDrivers: companyDrivers.length,
      maxDrivers: 20,
      remainingSlots: Math.max(0, 20 - companyDrivers.length)
    }
  });
});

// Admin - Update driver status
app.put('/api/admin/drivers/:driverId/status', authenticateToken, (req, res) => {
  if (req.user.role !== 'admin') {
    return res.status(403).json({ success: false, message: 'Admin access required' });
  }

  const { driverId } = req.params;
  const { status } = req.body;

  const driver = users.find(u =>
    u.id === driverId &&
    u.role === 'driver' &&
    u.companyName === req.user.companyName
  );

  if (!driver) {
    return res.status(404).json({ success: false, message: 'Driver not found' });
  }

  driver.status = status;
  driver.updatedAt = new Date().toISOString();

  res.json({
    success: true,
    message: `Driver status updated to ${status}`,
    data: { driver: { id: driver.id, name: driver.name, status: driver.status } }
  });
});

const PORT = process.env.PORT || 3001;
app.listen(PORT, async () => {
  console.log(`🚀 DriverSetu API Server running on port ${PORT}`);
  console.log(`📱 Environment: development`);
  console.log(`🔗 Health check: http://localhost:${PORT}/api/health`);
  console.log(`📊 API Base URL: http://localhost:${PORT}/api`);
  console.log(`👥 Admin can register up to 20 drivers per company`);
  console.log(`💳 Admin pays for all drivers under subscription`);
  console.log(`📲 OTP System: Ready (Console SMS simulation)`);
  console.log(`🗄️ OTP Storage: ${otpStorage.length} active OTPs`);

  // Initialize test users
  await initializeTestUsers();
});

module.exports = app;
