import{d as a,e as c}from"./chunk-26IZYKKN.js";import{c as r,d as s}from"./chunk-JGEA7HOG.js";import{a as i}from"./chunk-FNBMHXHF.js";import"./chunk-C5RQ2IC2.js";import{e as n}from"./chunk-JHI3MBHO.js";var h=()=>{let e=window;e.addEventListener("statusTap",()=>{r(()=>{let m=e.innerWidth,d=e.innerHeight,o=document.elementFromPoint(m/2,d/2);if(!o)return;let t=a(o);t&&new Promise(l=>i(t,l)).then(()=>{s(()=>n(null,null,function*(){t.style.setProperty("--overflow","hidden"),yield c(t,300),t.style.removeProperty("--overflow")}))})})})};export{h as startStatusTap};
