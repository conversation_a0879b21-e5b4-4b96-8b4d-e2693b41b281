import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import {
  IonContent,
  IonHeader,
  IonTitle,
  IonToolbar,
  IonButton,
  IonButtons,
  IonIcon,
  IonCard,
  IonCardContent,
  IonItem,
  IonLabel,
  IonInput,
  IonSpinner
} from '@ionic/angular/standalone';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { addIcons } from 'ionicons';
import { phonePortraitOutline, carSport, arrowBackOutline, logInOutline, eyeOutline, eyeOffOutline, alertCircleOutline, businessOutline } from 'ionicons/icons';

@Component({
  selector: 'app-driver-login',
  templateUrl: './driver-login.page.html',
  styleUrls: ['./driver-login.page.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    IonContent,
    IonHeader,
    IonTitle,
    IonToolbar,
    IonButton,
    IonButtons,
    IonIcon,
    IonCard,
    IonCardContent,
    IonItem,
    IonLabel,
    IonInput,
    IonSpinner
  ]
})
export class DriverLoginPage implements OnInit {
  driverLoginForm: FormGroup;
  showPassword = false;
  isLoading = false;
  errorMessage = '';

  constructor(
    private formBuilder: FormBuilder,
    private http: HttpClient,
    private router: Router
  ) {
    addIcons({
      phonePortraitOutline,
      carSport,
      arrowBackOutline,
      logInOutline,
      eyeOutline,
      eyeOffOutline,
      alertCircleOutline,
      businessOutline
    });

    this.driverLoginForm = this.formBuilder.group({
      phone: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    });
  }

  ngOnInit() {}

  getFieldError(fieldName: string): string {
    const field = this.driverLoginForm.get(fieldName);
    if (field && field.invalid && field.touched) {
      if (field.errors?.['required']) {
        return `${this.getFieldDisplayName(fieldName)} is required`;
      }
      if (field.errors?.['pattern']) {
        if (fieldName === 'phone') {
          return 'Please enter a valid 10-digit phone number';
        }
      }
      if (field.errors?.['minlength']) {
        return `${this.getFieldDisplayName(fieldName)} must be at least ${field.errors?.['minlength'].requiredLength} characters`;
      }
    }
    return '';
  }

  private getFieldDisplayName(fieldName: string): string {
    const displayNames: { [key: string]: string } = {
      phone: 'Phone number',
      password: 'Password'
    };
    return displayNames[fieldName] || fieldName;
  }

  togglePassword() {
    this.showPassword = !this.showPassword;
  }

  login() {
    if (this.driverLoginForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';

    const loginData = {
      phone: this.driverLoginForm.value.phone,
      password: this.driverLoginForm.value.password,
      role: 'driver'
    };

    console.log('Driver login attempt:', loginData);

    this.http.post('http://localhost:3001/api/auth/login', loginData).subscribe({
      next: (response: any) => {
        console.log('Driver login response:', response);
        if (response.success) {
          // Store token and user data
          localStorage.setItem('token', response.data.token);
          localStorage.setItem('user', JSON.stringify(response.data.user));

          // Navigate to driver dashboard
          this.router.navigate(['/driver-dashboard']);
        } else {
          this.errorMessage = response.message || 'Login failed';
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Driver login error:', error);
        this.errorMessage = error.error?.message || 'Login failed. Please try again.';
        this.isLoading = false;
      }
    });
  }

  private markFormGroupTouched() {
    Object.keys(this.driverLoginForm.controls).forEach(key => {
      const control = this.driverLoginForm.get(key);
      control?.markAsTouched();
    });
  }

  goBack() {
    this.router.navigate(['/login']);
  }

  goToForgotPassword() {
    this.router.navigate(['/forgot-password']);
  }

  goToAdminLogin() {
    this.router.navigate(['/login']);
  }

  goToOtpLogin() {
    this.router.navigate(['/otp-login']);
  }
}
