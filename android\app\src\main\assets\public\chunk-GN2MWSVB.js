import{a as c,b as l,c as a,d as t,e as o,g as n,r as s,s as p,t as m,u as d}from"./chunk-EOVFBHGK.js";import"./chunk-26IZYKKN.js";import"./chunk-FYMTVGAO.js";import"./chunk-ASVRUY3W.js";import"./chunk-RC5DY42Y.js";import"./chunk-JGEA7HOG.js";import"./chunk-R5HL6L5F.js";import"./chunk-4WFVMWDK.js";import"./chunk-M2X7KQLB.js";import"./chunk-2YSZFPCQ.js";import"./chunk-57YRIO75.js";import"./chunk-FNBMHXHF.js";import"./chunk-C5RQ2IC2.js";import"./chunk-42C7ZIID.js";import"./chunk-JHI3MBHO.js";var x=(()=>{let e=class e{constructor(){}};e.\u0275fac=function(i){return new(i||e)},e.\u0275cmp=l({type:e,selectors:[["app-home"]],decls:16,vars:2,consts:[[3,"translucent"],[3,"fullscreen"],["collapse","condense"],["size","large"],["id","container"],["target","_blank","rel","noopener noreferrer","href","https://ionicframework.com/docs/components"]],template:function(i,g){i&1&&(t(0,"ion-header",0)(1,"ion-toolbar")(2,"ion-title"),n(3," Blank "),o()()(),t(4,"ion-content",1)(5,"ion-header",2)(6,"ion-toolbar")(7,"ion-title",3),n(8,"Blank"),o()()(),t(9,"div",4)(10,"strong"),n(11,"Ready to create an app?"),o(),t(12,"p"),n(13,"Start with Ionic "),t(14,"a",5),n(15,"UI Components"),o()()()()),i&2&&(a("translucent",!0),c(4),a("fullscreen",!0))},dependencies:[p,d,m,s],styles:["#container[_ngcontent-%COMP%]{text-align:center;position:absolute;left:0;right:0;top:50%;transform:translateY(-50%)}#container[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{font-size:20px;line-height:26px}#container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:16px;line-height:22px;color:#8c8c8c;margin:0}#container[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{text-decoration:none}"]});let r=e;return r})();export{x as HomePage};
