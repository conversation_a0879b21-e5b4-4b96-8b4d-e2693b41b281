.login-container {
  max-width: 400px;
  margin: 0 auto;
  padding: 20px;
}

.header-section {
  text-align: center;
  margin-bottom: 30px;
  
  .logo-container {
    margin-bottom: 20px;
    
    .logo-icon {
      font-size: 64px;
      color: var(--ion-color-primary);
    }
  }
  
  h1 {
    font-size: 28px;
    font-weight: 600;
    color: var(--ion-color-dark);
    margin-bottom: 8px;
  }
  
  .subtitle {
    font-size: 16px;
    color: var(--ion-color-medium);
    margin: 0;
    line-height: 1.4;
  }
}

.form-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  
  ion-card-content {
    padding: 24px;
  }
}

.form-item {
  margin-bottom: 16px;
  --border-radius: 8px;
  --background: var(--ion-color-light);
  
  ion-label {
    font-weight: 500;
    color: var(--ion-color-dark);
  }
  
  ion-input {
    --padding-start: 12px;
    --padding-end: 12px;
    font-size: 16px;
  }
  
  &.ion-invalid {
    --border-color: var(--ion-color-danger);
  }
}

.error-message {
  color: var(--ion-color-danger);
  font-size: 14px;
  margin-top: 8px;
  margin-bottom: 16px;
  padding-left: 4px;
}

.success-message {
  color: var(--ion-color-success);
  font-size: 14px;
  margin-top: 8px;
  margin-bottom: 16px;
  padding-left: 4px;
}

.login-button, .verify-button {
  margin-top: 20px;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  --border-radius: 8px;
  
  ion-spinner {
    margin-right: 8px;
  }
}

.otp-timer {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 16px 0;
  color: var(--ion-color-medium);
  font-size: 14px;
  
  ion-icon {
    margin-right: 8px;
    font-size: 16px;
  }
}

.resend-section {
  text-align: center;
  margin: 16px 0;
  
  ion-text {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
  }
  
  .resend-button {
    --color: var(--ion-color-primary);
    font-weight: 500;
    
    ion-icon {
      margin-right: 4px;
    }
  }
}

.alternative-login {
  text-align: center;
  margin-top: 30px;
  
  ion-text p {
    margin-bottom: 16px;
    font-size: 14px;
  }
  
  .alt-login-button {
    height: 44px;
    --border-radius: 8px;
    --border-width: 2px;
    font-weight: 500;
  }
}

// Responsive design
@media (max-width: 480px) {
  .login-container {
    padding: 16px;
  }
  
  .header-section {
    margin-bottom: 24px;
    
    .logo-container .logo-icon {
      font-size: 56px;
    }
    
    h1 {
      font-size: 24px;
    }
    
    .subtitle {
      font-size: 14px;
    }
  }
  
  .form-card ion-card-content {
    padding: 20px;
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .form-card {
    --background: var(--ion-color-dark);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }
  
  .form-item {
    --background: var(--ion-color-dark-shade);
  }
}
