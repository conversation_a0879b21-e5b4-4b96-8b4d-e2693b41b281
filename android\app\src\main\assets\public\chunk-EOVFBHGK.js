import{a as Zn,b as gc,c as oo,d as xi,g as io,h as vc,i as uf}from"./chunk-26IZYKKN.js";import{a as R,b as uc,c as dc,d as fc,e as pc,f as hc,g as af,h as Si,i as Ti}from"./chunk-RC5DY42Y.js";import{a as Ii,b as De,c as qn,d as xt,e as T,f as xe,g as Xd,h as Jd,i as H,j as to,k as Ce}from"./chunk-JGEA7HOG.js";import{a as ro,d as cf}from"./chunk-R5HL6L5F.js";import{a as lf}from"./chunk-4WFVMWDK.js";import{a as mc,c as Mi}from"./chunk-M2X7KQLB.js";import{a as yc}from"./chunk-2YSZFPCQ.js";import{a as df}from"./chunk-57YRIO75.js";import{a as rt,b as At,c as rf,d as wi,e as _i,f as of,g as de,h as Rt,i as lc,j as Jt,l as sf}from"./chunk-FNBMHXHF.js";import{a as X,b as ef,c as tf,d as nf,e as Qe,f as Ei}from"./chunk-C5RQ2IC2.js";import{a as no,b as Xt}from"./chunk-42C7ZIID.js";import{a as y,b as U,d as Kd,e as b}from"./chunk-JHI3MBHO.js";var Dc;function Cc(){return Dc}function Nt(e){let t=Dc;return Dc=e,t}var my=Symbol("NotFound"),Ai=class extends Error{name="\u0275NotFound";constructor(t){super(t)}};function Yn(e){return e===my||e?.name==="\u0275NotFound"}function ff(e,t){return Object.is(e,t)}var ve=null,Ri=!1,Ic=1;var Ni=Symbol("SIGNAL");function $(e){let t=ve;return ve=e,t}function Ec(){return ve}var ki={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function wc(e){if(Ri)throw new Error("");if(ve===null)return;ve.consumerOnSignalRead(e);let t=ve.nextProducerIndex++;if(Li(ve),t<ve.producerNode.length&&ve.producerNode[t]!==e&&so(ve)){let n=ve.producerNode[t];Fi(n,ve.producerIndexOfThis[t])}ve.producerNode[t]!==e&&(ve.producerNode[t]=e,ve.producerIndexOfThis[t]=so(ve)?gf(e,ve,t):0),ve.producerLastReadVersion[t]=e.version}function pf(){Ic++}function hf(e){if(!(so(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===Ic)){if(!e.producerMustRecompute(e)&&!Pi(e)){bc(e);return}e.producerRecomputeValue(e),bc(e)}}function _c(e){if(e.liveConsumerNode===void 0)return;let t=Ri;Ri=!0;try{for(let n of e.liveConsumerNode)n.dirty||gy(n)}finally{Ri=t}}function mf(){return ve?.consumerAllowSignalWrites!==!1}function gy(e){e.dirty=!0,_c(e),e.consumerMarkedDirty?.(e)}function bc(e){e.dirty=!1,e.lastCleanEpoch=Ic}function Oi(e){return e&&(e.nextProducerIndex=0),$(e)}function Sc(e,t){if($(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(so(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)Fi(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function Pi(e){Li(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(hf(n),r!==n.version))return!0}return!1}function Tc(e){if(Li(e),so(e))for(let t=0;t<e.producerNode.length;t++)Fi(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function gf(e,t,n){if(vf(e),e.liveConsumerNode.length===0&&yf(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=gf(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function Fi(e,t){if(vf(e),e.liveConsumerNode.length===1&&yf(e))for(let r=0;r<e.producerNode.length;r++)Fi(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];Li(o),o.producerIndexOfThis[r]=t}}function so(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function Li(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function vf(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function yf(e){return e.producerNode!==void 0}function vy(){throw new Error}var Df=vy;function yy(e){Df(e)}function Mc(e){Df=e}var Dy=null;function xc(e,t){mf()||yy(e),e.equal(e.value,t)||(e.value=t,Cy(e))}var Ac=U(y({},ki),{equal:ff,value:void 0,kind:"signal"});function Cy(e){e.version++,pf(),_c(e),Dy?.(e)}function _(e){return typeof e=="function"}function Qn(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var ji=Qn(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function ao(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var fe=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(_(r))try{r()}catch(i){t=i instanceof ji?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{Cf(i)}catch(s){t=t??[],s instanceof ji?t=[...t,...s.errors]:t.push(s)}}if(t)throw new ji(t)}}add(t){var n;if(t&&t!==this)if(this.closed)Cf(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&ao(n,t)}remove(t){let{_finalizers:n}=this;n&&ao(n,t),t instanceof e&&t._removeParent(this)}};fe.EMPTY=(()=>{let e=new fe;return e.closed=!0,e})();var Rc=fe.EMPTY;function Vi(e){return e instanceof fe||e&&"closed"in e&&_(e.remove)&&_(e.add)&&_(e.unsubscribe)}function Cf(e){_(e)?e():e.unsubscribe()}var ot={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var Kn={setTimeout(e,t,...n){let{delegate:r}=Kn;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=Kn;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function Bi(e){Kn.setTimeout(()=>{let{onUnhandledError:t}=ot;if(t)t(e);else throw e})}function co(){}var bf=Nc("C",void 0,void 0);function If(e){return Nc("E",void 0,e)}function Ef(e){return Nc("N",e,void 0)}function Nc(e,t,n){return{kind:e,value:t,error:n}}var Cn=null;function Xn(e){if(ot.useDeprecatedSynchronousErrorHandling){let t=!Cn;if(t&&(Cn={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=Cn;if(Cn=null,n)throw r}}else e()}function wf(e){ot.useDeprecatedSynchronousErrorHandling&&Cn&&(Cn.errorThrown=!0,Cn.error=e)}var bn=class extends fe{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,Vi(t)&&t.add(this)):this.destination=Ey}static create(t,n,r){return new Jn(t,n,r)}next(t){this.isStopped?Oc(Ef(t),this):this._next(t)}error(t){this.isStopped?Oc(If(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?Oc(bf,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},by=Function.prototype.bind;function kc(e,t){return by.call(e,t)}var Pc=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){Hi(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){Hi(r)}else Hi(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){Hi(n)}}},Jn=class extends bn{constructor(t,n,r){super();let o;if(_(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&ot.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&kc(t.next,i),error:t.error&&kc(t.error,i),complete:t.complete&&kc(t.complete,i)}):o=t}this.destination=new Pc(o)}};function Hi(e){ot.useDeprecatedSynchronousErrorHandling?wf(e):Bi(e)}function Iy(e){throw e}function Oc(e,t){let{onStoppedNotification:n}=ot;n&&Kn.setTimeout(()=>n(e,t))}var Ey={closed:!0,next:co,error:Iy,complete:co};var er=typeof Symbol=="function"&&Symbol.observable||"@@observable";function ke(e){return e}function Fc(...e){return Lc(e)}function Lc(e){return e.length===0?ke:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var F=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=_y(n)?n:new Jn(n,r,o);return Xn(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=_f(r),new r((o,i)=>{let s=new Jn({next:a=>{try{n(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[er](){return this}pipe(...n){return Lc(n)(this)}toPromise(n){return n=_f(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function _f(e){var t;return(t=e??ot.Promise)!==null&&t!==void 0?t:Promise}function wy(e){return e&&_(e.next)&&_(e.error)&&_(e.complete)}function _y(e){return e&&e instanceof bn||wy(e)&&Vi(e)}function jc(e){return _(e?.lift)}function O(e){return t=>{if(jc(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function P(e,t,n,r,o){return new Vc(e,t,n,r,o)}var Vc=class extends bn{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(c){t.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){t.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function tr(){return O((e,t)=>{let n=null;e._refCount++;let r=P(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var nr=class extends F{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,jc(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new fe;let n=this.getSubject();t.add(this.source.subscribe(P(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=fe.EMPTY)}return t}refCount(){return tr()(this)}};var Sf=Qn(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var te=(()=>{class e extends F{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new $i(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new Sf}next(n){Xn(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){Xn(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){Xn(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?Rc:(this.currentObservers=null,i.push(n),new fe(()=>{this.currentObservers=null,ao(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new F;return n.source=this,n}}return e.create=(t,n)=>new $i(t,n),e})(),$i=class extends te{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:Rc}};var pe=class extends te{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var je=new F(e=>e.complete());function Tf(e){return e&&_(e.schedule)}function Mf(e){return e[e.length-1]}function xf(e){return _(Mf(e))?e.pop():void 0}function en(e){return Tf(Mf(e))?e.pop():void 0}function En(e,t,n,r){var o=arguments.length,i=o<3?t:r===null?r=Object.getOwnPropertyDescriptor(t,n):r,s;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")i=Reflect.decorate(e,t,n,r);else for(var a=e.length-1;a>=0;a--)(s=e[a])&&(i=(o<3?s(i):o>3?s(t,n,i):s(t,n))||i);return o>3&&i&&Object.defineProperty(t,n,i),i}function Rf(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(u){try{l(r.next(u))}catch(f){s(f)}}function c(u){try{l(r.throw(u))}catch(f){s(f)}}function l(u){u.done?i(u.value):o(u.value).then(a,c)}l((r=r.apply(e,t||[])).next())})}function Af(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function In(e){return this instanceof In?(this.v=e,this):new In(e)}function Nf(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(d){return function(m){return Promise.resolve(m).then(d,f)}}function a(d,m){r[d]&&(o[d]=function(g){return new Promise(function(v,D){i.push([d,g,v,D])>1||c(d,g)})},m&&(o[d]=m(o[d])))}function c(d,m){try{l(r[d](m))}catch(g){p(i[0][3],g)}}function l(d){d.value instanceof In?Promise.resolve(d.value.v).then(u,f):p(i[0][2],d)}function u(d){c("next",d)}function f(d){c("throw",d)}function p(d,m){d(m),i.shift(),i.length&&c(i[0][0],i[0][1])}}function kf(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof Af=="function"?Af(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(l){i({value:l,done:a})},s)}}var rr=e=>e&&typeof e.length=="number"&&typeof e!="function";function Ui(e){return _(e?.then)}function zi(e){return _(e[er])}function Gi(e){return Symbol.asyncIterator&&_(e?.[Symbol.asyncIterator])}function Wi(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function Sy(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var qi=Sy();function Zi(e){return _(e?.[qi])}function Yi(e){return Nf(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield In(n.read());if(o)return yield In(void 0);yield yield In(r)}}finally{n.releaseLock()}})}function Qi(e){return _(e?.getReader)}function he(e){if(e instanceof F)return e;if(e!=null){if(zi(e))return Ty(e);if(rr(e))return My(e);if(Ui(e))return xy(e);if(Gi(e))return Of(e);if(Zi(e))return Ay(e);if(Qi(e))return Ry(e)}throw Wi(e)}function Ty(e){return new F(t=>{let n=e[er]();if(_(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function My(e){return new F(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function xy(e){return new F(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,Bi)})}function Ay(e){return new F(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function Of(e){return new F(t=>{Ny(e,t).catch(n=>t.error(n))})}function Ry(e){return Of(Yi(e))}function Ny(e,t){var n,r,o,i;return Rf(this,void 0,void 0,function*(){try{for(n=kf(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function Ve(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function Ki(e,t=0){return O((n,r)=>{n.subscribe(P(r,o=>Ve(r,e,()=>r.next(o),t),()=>Ve(r,e,()=>r.complete(),t),o=>Ve(r,e,()=>r.error(o),t)))})}function Xi(e,t=0){return O((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function Pf(e,t){return he(e).pipe(Xi(t),Ki(t))}function Ff(e,t){return he(e).pipe(Xi(t),Ki(t))}function Lf(e,t){return new F(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function jf(e,t){return new F(n=>{let r;return Ve(n,t,()=>{r=e[qi](),Ve(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>_(r?.return)&&r.return()})}function Ji(e,t){if(!e)throw new Error("Iterable cannot be null");return new F(n=>{Ve(n,t,()=>{let r=e[Symbol.asyncIterator]();Ve(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function Vf(e,t){return Ji(Yi(e),t)}function Bf(e,t){if(e!=null){if(zi(e))return Pf(e,t);if(rr(e))return Lf(e,t);if(Ui(e))return Ff(e,t);if(Gi(e))return Ji(e,t);if(Zi(e))return jf(e,t);if(Qi(e))return Vf(e,t)}throw Wi(e)}function ie(e,t){return t?Bf(e,t):he(e)}function S(...e){let t=en(e);return ie(e,t)}function or(e,t){let n=_(e)?e:()=>e,r=o=>o.error(n());return new F(t?o=>t.schedule(r,0,o):r)}function Bc(e){return!!e&&(e instanceof F||_(e.lift)&&_(e.subscribe))}var kt=Qn(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function L(e,t){return O((n,r)=>{let o=0;n.subscribe(P(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:ky}=Array;function Oy(e,t){return ky(t)?e(...t):e(t)}function es(e){return L(t=>Oy(e,t))}var{isArray:Py}=Array,{getPrototypeOf:Fy,prototype:Ly,keys:jy}=Object;function Hf(e){if(e.length===1){let t=e[0];if(Py(t))return{args:t,keys:null};if(Vy(t)){let n=jy(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function Vy(e){return e&&typeof e=="object"&&Fy(e)===Ly}function $f(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function ir(...e){let t=en(e),n=xf(e),{args:r,keys:o}=Hf(e);if(r.length===0)return ie([],t);let i=new F(By(r,t,o?s=>$f(o,s):ke));return n?i.pipe(es(n)):i}function By(e,t,n=ke){return r=>{Uf(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)Uf(t,()=>{let l=ie(e[c],t),u=!1;l.subscribe(P(r,f=>{i[c]=f,u||(u=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function Uf(e,t,n){e?Ve(n,e,t):t()}function zf(e,t,n,r,o,i,s,a){let c=[],l=0,u=0,f=!1,p=()=>{f&&!c.length&&!l&&t.complete()},d=g=>l<r?m(g):c.push(g),m=g=>{i&&t.next(g),l++;let v=!1;he(n(g,u++)).subscribe(P(t,D=>{o?.(D),i?d(D):t.next(D)},()=>{v=!0},void 0,()=>{if(v)try{for(l--;c.length&&l<r;){let D=c.shift();s?Ve(t,s,()=>m(D)):m(D)}p()}catch(D){t.error(D)}}))};return e.subscribe(P(t,d,()=>{f=!0,p()})),()=>{a?.()}}function oe(e,t,n=1/0){return _(t)?oe((r,o)=>L((i,s)=>t(r,i,o,s))(he(e(r,o))),n):(typeof t=="number"&&(n=t),O((r,o)=>zf(r,o,e,n)))}function sr(e=1/0){return oe(ke,e)}function Gf(){return sr(1)}function ar(...e){return Gf()(ie(e,en(e)))}function lo(e){return new F(t=>{he(e()).subscribe(t)})}var Hy=["addListener","removeListener"],$y=["addEventListener","removeEventListener"],Uy=["on","off"];function cr(e,t,n,r){if(_(n)&&(r=n,n=void 0),r)return cr(e,t,n).pipe(es(r));let[o,i]=Wy(e)?$y.map(s=>a=>e[s](t,a,n)):zy(e)?Hy.map(Wf(e,t)):Gy(e)?Uy.map(Wf(e,t)):[];if(!o&&rr(e))return oe(s=>cr(s,t,n))(he(e));if(!o)throw new TypeError("Invalid event target");return new F(s=>{let a=(...c)=>s.next(1<c.length?c:c[0]);return o(a),()=>i(a)})}function Wf(e,t){return n=>r=>e[n](t,r)}function zy(e){return _(e.addListener)&&_(e.removeListener)}function Gy(e){return _(e.on)&&_(e.off)}function Wy(e){return _(e.addEventListener)&&_(e.removeEventListener)}function Se(e,t){return O((n,r)=>{let o=0;n.subscribe(P(r,i=>e.call(t,i,o++)&&r.next(i)))})}function pt(e){return O((t,n)=>{let r=null,o=!1,i;r=t.subscribe(P(n,void 0,void 0,s=>{i=he(e(s,pt(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function qf(e,t,n,r,o){return(i,s)=>{let a=n,c=t,l=0;i.subscribe(P(s,u=>{let f=l++;c=a?e(c,u,f):(a=!0,u),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function tn(e,t){return _(t)?oe(e,t,1):oe(e,1)}function nn(e){return O((t,n)=>{let r=!1;t.subscribe(P(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function Ot(e){return e<=0?()=>je:O((t,n)=>{let r=0;t.subscribe(P(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function Hc(e,t=ke){return e=e??qy,O((n,r)=>{let o,i=!0;n.subscribe(P(r,s=>{let a=t(s);(i||!e(o,a))&&(i=!1,o=a,r.next(s))}))})}function qy(e,t){return e===t}function ts(e=Zy){return O((t,n)=>{let r=!1;t.subscribe(P(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function Zy(){return new kt}function uo(e){return O((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function Pt(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Se((o,i)=>e(o,i,r)):ke,Ot(1),n?nn(t):ts(()=>new kt))}function lr(e){return e<=0?()=>je:O((t,n)=>{let r=[];t.subscribe(P(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function $c(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Se((o,i)=>e(o,i,r)):ke,lr(1),n?nn(t):ts(()=>new kt))}function Uc(e,t){return O(qf(e,t,arguments.length>=2,!0))}function zc(...e){let t=en(e);return O((n,r)=>{(t?ar(e,n,t):ar(e,n)).subscribe(r)})}function Ae(e,t){return O((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(P(r,c=>{o?.unsubscribe();let l=0,u=i++;he(e(c,u)).subscribe(o=P(r,f=>r.next(t?t(c,f,u,l++):f),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function ns(e){return O((t,n)=>{he(e).subscribe(P(n,()=>n.complete(),co)),!n.closed&&t.subscribe(n)})}function be(e,t,n){let r=_(e)||t||n?{next:e,error:t,complete:n}:e;return r?O((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(P(i,c=>{var l;(l=r.next)===null||l===void 0||l.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var l;a=!1,(l=r.error)===null||l===void 0||l.call(r,c),i.error(c)},()=>{var c,l;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(l=r.finalize)===null||l===void 0||l.call(r)}))}):ke}var E=class extends Error{code;constructor(t,n){super(nl(t,n)),this.code=t}};function Qy(e){return`NG0${Math.abs(e)}`}function nl(e,t){return`${Qy(e)}${t?": "+t:""}`}function re(e){for(let t in e)if(e[t]===re)return t;throw Error("")}function Kf(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function ze(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(ze).join(", ")}]`;if(e==null)return""+e;let t=e.overriddenName||e.name;if(t)return`${t}`;let n=e.toString();if(n==null)return""+n;let r=n.indexOf(`
`);return r>=0?n.slice(0,r):n}function rl(e,t){return e?t?`${e} ${t}`:e:t||""}var Ky=re({__forward_ref__:re});function dr(e){return e.__forward_ref__=dr,e.toString=function(){return ze(this())},e}function Be(e){return ol(e)?e():e}function ol(e){return typeof e=="function"&&e.hasOwnProperty(Ky)&&e.__forward_ref__===dr}function Xf(e,t,n,r){throw new Error(`ASSERTION ERROR: ${e}`+(r==null?"":` [Expected=> ${n} ${r} ${t} <=Actual]`))}function C(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function mt(e){return{providers:e.providers||[],imports:e.imports||[]}}function mo(e){return Xy(e,cs)}function il(e){return mo(e)!==null}function Xy(e,t){return e.hasOwnProperty(t)&&e[t]||null}function Jy(e){let t=e?.[cs]??null;return t||null}function Wc(e){return e&&e.hasOwnProperty(os)?e[os]:null}var cs=re({\u0275prov:re}),os=re({\u0275inj:re}),w=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(t,n){this._desc=t,this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=C({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function sl(e){return e&&!!e.\u0275providers}var al=re({\u0275cmp:re}),cl=re({\u0275dir:re}),ll=re({\u0275pipe:re}),ul=re({\u0275mod:re}),po=re({\u0275fac:re}),xn=re({__NG_ELEMENT_ID__:re}),Zf=re({__NG_ENV_ID__:re});function Jf(e){return typeof e=="string"?e:e==null?"":String(e)}function ep(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():Jf(e)}function dl(e,t){throw new E(-200,e)}function ls(e,t){throw new E(-201,!1)}var qc;function tp(){return qc}function Ue(e){let t=qc;return qc=e,t}function fl(e,t,n){let r=mo(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&8)return null;if(t!==void 0)return t;ls(e,"Injector")}var eD={},wn=eD,tD="__NG_DI_FLAG__",Zc=class{injector;constructor(t){this.injector=t}retrieve(t,n){let r=_n(n)||0;try{return this.injector.get(t,r&8?null:wn,r)}catch(o){if(Yn(o))return o;throw o}}},is="ngTempTokenPath",nD="ngTokenPath",rD=/\n/gm,oD="\u0275",Yf="__source";function iD(e,t=0){let n=Cc();if(n===void 0)throw new E(-203,!1);if(n===null)return fl(e,void 0,t);{let r=sD(t),o=n.retrieve(e,r);if(Yn(o)){if(r.optional)return null;throw o}return o}}function I(e,t=0){return(tp()||iD)(Be(e),t)}function h(e,t){return I(e,_n(t))}function _n(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function sD(e){return{optional:!!(e&8),host:!!(e&1),self:!!(e&2),skipSelf:!!(e&4)}}function Yc(e){let t=[];for(let n=0;n<e.length;n++){let r=Be(e[n]);if(Array.isArray(r)){if(r.length===0)throw new E(900,!1);let o,i=0;for(let s=0;s<r.length;s++){let a=r[s],c=aD(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}t.push(I(o,i))}else t.push(I(r))}return t}function aD(e){return e[tD]}function cD(e,t,n,r){let o=e[is];throw t[Yf]&&o.unshift(t[Yf]),e.message=lD(`
`+e.message,o,n,r),e[nD]=o,e[is]=null,e}function lD(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==oD?e.slice(2):e;let o=ze(t);if(Array.isArray(t))o=t.map(ze).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):ze(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(rD,`
  `)}`}function Sn(e,t){let n=e.hasOwnProperty(po);return n?e[po]:null}function np(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=t[r];if(n&&(o=n(o),i=n(i)),i!==o)return!1}return!0}function rp(e){return e.flat(Number.POSITIVE_INFINITY)}function us(e,t){e.forEach(n=>Array.isArray(n)?us(n,t):t(n))}function pl(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function go(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function op(e,t){let n=[];for(let r=0;r<e;r++)n.push(t);return n}var on={},Ke=[],sn=new w(""),hl=new w("",-1),ml=new w(""),ho=class{get(t,n=wn){if(n===wn)throw new Ai(`NullInjectorError: No provider for ${ze(t)}!`);return n}};function gl(e){return e[ul]||null}function gt(e){return e[al]||null}function vl(e){return e[cl]||null}function ip(e){return e[ll]||null}function fr(e){return{\u0275providers:e}}function sp(...e){return{\u0275providers:yl(!0,e),\u0275fromNgModule:!0}}function yl(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return us(t,s=>{let a=s;ss(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&ap(o,i),n}function ap(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];Dl(o,i=>{t(i,r)})}}function ss(e,t,n,r){if(e=Be(e),!e)return!1;let o=null,i=Wc(e),s=!i&&gt(e);if(!i&&!s){let c=e.ngModule;if(i=Wc(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let l of c)ss(l,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let l;try{us(i.imports,u=>{ss(u,t,n,r)&&(l||=[],l.push(u))})}finally{}l!==void 0&&ap(l,t)}if(!a){let l=Sn(o)||(()=>new o);t({provide:o,useFactory:l,deps:Ke},o),t({provide:ml,useValue:o,multi:!0},o),t({provide:sn,useValue:()=>I(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let l=e;Dl(c,u=>{t(u,l)})}}else return!1;return o!==e&&e.providers!==void 0}function Dl(e,t){for(let n of e)sl(n)&&(n=n.\u0275providers),Array.isArray(n)?Dl(n,t):t(n)}var uD=re({provide:String,useValue:re});function cp(e){return e!==null&&typeof e=="object"&&uD in e}function dD(e){return!!(e&&e.useExisting)}function fD(e){return!!(e&&e.useFactory)}function as(e){return typeof e=="function"}var vo=new w(""),rs={},Qf={},Gc;function pr(){return Gc===void 0&&(Gc=new ho),Gc}var ee=class{},Tn=class extends ee{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,Kc(t,s=>this.processProvider(s)),this.records.set(hl,ur(void 0,this)),o.has("environment")&&this.records.set(ee,ur(void 0,this));let i=this.records.get(vo);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(ml,Ke,{self:!0}))}retrieve(t,n){let r=_n(n)||0;try{return this.get(t,wn,r)}catch(o){if(Yn(o))return o;throw o}}destroy(){fo(this),this._destroyed=!0;let t=$(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),$(t)}}onDestroy(t){return fo(this),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){fo(this);let n=Nt(this),r=Ue(void 0),o;try{return t()}finally{Nt(n),Ue(r)}}get(t,n=wn,r){if(fo(this),t.hasOwnProperty(Zf))return t[Zf](this);let o=_n(r),i,s=Nt(this),a=Ue(void 0);try{if(!(o&4)){let l=this.records.get(t);if(l===void 0){let u=vD(t)&&mo(t);u&&this.injectableDefInScope(u)?l=ur(Qc(t),rs):l=null,this.records.set(t,l)}if(l!=null)return this.hydrate(t,l)}let c=o&2?pr():this.parent;return n=o&8&&n===wn?null:n,c.get(t,n)}catch(c){if(Yn(c)){if((c[is]=c[is]||[]).unshift(ze(t)),s)throw c;return cD(c,t,"R3InjectorError",this.source)}else throw c}finally{Ue(a),Nt(s)}}resolveInjectorInitializers(){let t=$(null),n=Nt(this),r=Ue(void 0),o;try{let i=this.get(sn,Ke,{self:!0});for(let s of i)s()}finally{Nt(n),Ue(r),$(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(ze(r));return`R3Injector[${t.join(", ")}]`}processProvider(t){t=Be(t);let n=as(t)?t:Be(t&&t.provide),r=hD(t);if(!as(t)&&t.multi===!0){let o=this.records.get(n);o||(o=ur(void 0,rs,!0),o.factory=()=>Yc(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n){let r=$(null);try{return n.value===Qf?dl(ze(t)):n.value===rs&&(n.value=Qf,n.value=n.factory()),typeof n.value=="object"&&n.value&&gD(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{$(r)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=Be(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function Qc(e){let t=mo(e),n=t!==null?t.factory:Sn(e);if(n!==null)return n;if(e instanceof w)throw new E(204,!1);if(e instanceof Function)return pD(e);throw new E(204,!1)}function pD(e){if(e.length>0)throw new E(204,!1);let n=Jy(e);return n!==null?()=>n.factory(e):()=>new e}function hD(e){if(cp(e))return ur(void 0,e.useValue);{let t=lp(e);return ur(t,rs)}}function lp(e,t,n){let r;if(as(e)){let o=Be(e);return Sn(o)||Qc(o)}else if(cp(e))r=()=>Be(e.useValue);else if(fD(e))r=()=>e.useFactory(...Yc(e.deps||[]));else if(dD(e))r=()=>I(Be(e.useExisting));else{let o=Be(e&&(e.useClass||e.provide));if(mD(e))r=()=>new o(...Yc(e.deps));else return Sn(o)||Qc(o)}return r}function fo(e){if(e.destroyed)throw new E(205,!1)}function ur(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function mD(e){return!!e.deps}function gD(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function vD(e){return typeof e=="function"||typeof e=="object"&&e.ngMetadataName==="InjectionToken"}function Kc(e,t){for(let n of e)Array.isArray(n)?Kc(n,t):n&&sl(n)?Kc(n.\u0275providers,t):t(n)}function Ge(e,t){let n;e instanceof Tn?(fo(e),n=e):n=new Zc(e);let r,o=Nt(n),i=Ue(void 0);try{return t()}finally{Nt(o),Ue(i)}}function up(){return tp()!==void 0||Cc()!=null}var it=0,A=1,x=2,Ie=3,Xe=4,Oe=5,hr=6,yo=7,We=8,mr=9,Lt=10,Re=11,gr=12,Cl=13,vr=14,He=15,an=16,An=17,vt=18,Do=19,bl=20,Ft=21,ds=22,Co=23,qe=24,fs=25,$e=26,dp=1;var cn=7,bo=8,Rn=9,Pe=10;function jt(e){return Array.isArray(e)&&typeof e[dp]=="object"}function st(e){return Array.isArray(e)&&e[dp]===!0}function ps(e){return(e.flags&4)!==0}function Nn(e){return e.componentOffset>-1}function hs(e){return(e.flags&1)===1}function Vt(e){return!!e.template}function yr(e){return(e[x]&512)!==0}function kn(e){return(e[x]&256)===256}var fp="svg",pp="math";function yt(e){for(;Array.isArray(e);)e=e[it];return e}function Bt(e,t){return yt(t[e.index])}function ms(e,t){return e.data[t]}function Dt(e,t){let n=t[e];return jt(n)?n:n[it]}function hp(e){return(e[x]&4)===4}function gs(e){return(e[x]&128)===128}function mp(e){return st(e[Ie])}function Dr(e,t){return t==null?null:e[t]}function Il(e){e[An]=0}function El(e){e[x]&1024||(e[x]|=1024,gs(e)&&Eo(e))}function Io(e){return!!(e[x]&9216||e[qe]?.dirty)}function vs(e){e[Lt].changeDetectionScheduler?.notify(8),e[x]&64&&(e[x]|=1024),Io(e)&&Eo(e)}function Eo(e){e[Lt].changeDetectionScheduler?.notify(0);let t=rn(e);for(;t!==null&&!(t[x]&8192||(t[x]|=8192,!gs(t)));)t=rn(t)}function wl(e,t){if(kn(e))throw new E(911,!1);e[Ft]===null&&(e[Ft]=[]),e[Ft].push(t)}function gp(e,t){if(e[Ft]===null)return;let n=e[Ft].indexOf(t);n!==-1&&e[Ft].splice(n,1)}function rn(e){let t=e[Ie];return st(t)?t[Ie]:t}function vp(e){return e[yo]??=[]}function yp(e){return e.cleanup??=[]}function Dp(e,t,n,r){let o=vp(t);o.push(n),e.firstCreatePass&&yp(e).push(r,o.length-1)}var z={lFrame:Np(null),bindingsEnabled:!0,skipHydrationRootTNode:null},wo=function(e){return e[e.Off=0]="Off",e[e.Exhaustive=1]="Exhaustive",e[e.OnlyDirtyViews=2]="OnlyDirtyViews",e}(wo||{}),yD=0,Xc=!1;function Cp(){return z.lFrame.elementDepthCount}function bp(){z.lFrame.elementDepthCount++}function Ip(){z.lFrame.elementDepthCount--}function ys(){return z.bindingsEnabled}function _l(){return z.skipHydrationRootTNode!==null}function Ep(e){return z.skipHydrationRootTNode===e}function wp(){z.skipHydrationRootTNode=null}function se(){return z.lFrame.lView}function at(){return z.lFrame.tView}function Je(){let e=Sl();for(;e!==null&&e.type===64;)e=e.parent;return e}function Sl(){return z.lFrame.currentTNode}function _p(){let e=z.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function ln(e,t){let n=z.lFrame;n.currentTNode=e,n.isParent=t}function Ds(){return z.lFrame.isParent}function Cs(){z.lFrame.isParent=!1}function Tl(e){Xf("Must never be called in production mode"),yD=e}function Ml(){return Xc}function xl(e){let t=Xc;return Xc=e,t}function Sp(e){return z.lFrame.bindingIndex=e}function Tp(){return z.lFrame.bindingIndex++}function Mp(){return z.lFrame.inI18n}function xp(e,t){let n=z.lFrame;n.bindingIndex=n.bindingRootIndex=e,bs(t)}function Ap(){return z.lFrame.currentDirectiveIndex}function bs(e){z.lFrame.currentDirectiveIndex=e}function Al(){return z.lFrame.currentQueryIndex}function Is(e){z.lFrame.currentQueryIndex=e}function DD(e){let t=e[A];return t.type===2?t.declTNode:t.type===1?e[Oe]:null}function Rl(e,t,n){if(n&4){let o=t,i=e;for(;o=o.parent,o===null&&!(n&1);)if(o=DD(i),o===null||(i=i[vr],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=z.lFrame=Rp();return r.currentTNode=t,r.lView=e,!0}function Es(e){let t=Rp(),n=e[A];z.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function Rp(){let e=z.lFrame,t=e===null?null:e.child;return t===null?Np(e):t}function Np(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function kp(){let e=z.lFrame;return z.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var Nl=kp;function ws(){let e=kp();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function kl(){return z.lFrame.selectedIndex}function un(e){z.lFrame.selectedIndex=e}function Op(){let e=z.lFrame;return ms(e.tView,e.selectedIndex)}function Pp(){return z.lFrame.currentNamespace}var Fp=!0;function _o(){return Fp}function So(e){Fp=e}function Jc(e,t=null,n=null,r){let o=Ol(e,t,n,r);return o.resolveInjectorInitializers(),o}function Ol(e,t=null,n=null,r,o=new Set){let i=[n||Ke,sp(e)];return r=r||(typeof e=="object"?void 0:ze(e)),new Tn(i,t||pr(),r||null,o)}var Ee=class e{static THROW_IF_NOT_FOUND=wn;static NULL=new ho;static create(t,n){if(Array.isArray(t))return Jc({name:""},n,t,"");{let r=t.name??"";return Jc({name:r},t.parent,t.providers,r)}}static \u0275prov=C({token:e,providedIn:"any",factory:()=>I(hl)});static __NG_ELEMENT_ID__=-1},ae=new w(""),Cr=(()=>{class e{static __NG_ELEMENT_ID__=CD;static __NG_ENV_ID__=n=>n}return e})(),el=class extends Cr{_lView;constructor(t){super(),this._lView=t}get destroyed(){return kn(this._lView)}onDestroy(t){let n=this._lView;return wl(n,t),()=>gp(n,t)}};function CD(){return new el(se())}var ht=class{_console=console;handleError(t){this._console.error("ERROR",t)}},Ct=new w("",{providedIn:"root",factory:()=>{let e=h(ee),t;return n=>{e.destroyed&&!t?setTimeout(()=>{throw n}):(t??=e.get(ht),t.handleError(n))}}}),Lp={provide:sn,useValue:()=>void h(ht),multi:!0};var Mn=class{},_s=new w("",{providedIn:"root",factory:()=>!1});var Pl=new w(""),Fl=new w("");var dn=(()=>{class e{taskId=0;pendingTasks=new Set;destroyed=!1;pendingTask=new pe(!1);get hasPendingTasks(){return this.destroyed?!1:this.pendingTask.value}get hasPendingTasksObservable(){return this.destroyed?new F(n=>{n.next(!1),n.complete()}):this.pendingTask}add(){!this.hasPendingTasks&&!this.destroyed&&this.pendingTask.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}has(n){return this.pendingTasks.has(n)}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this.hasPendingTasks&&this.pendingTask.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this.hasPendingTasks&&this.pendingTask.next(!1),this.destroyed=!0,this.pendingTask.unsubscribe()}static \u0275prov=C({token:e,providedIn:"root",factory:()=>new e})}return e})();function To(...e){}var Ll=(()=>{class e{static \u0275prov=C({token:e,providedIn:"root",factory:()=>new tl})}return e})(),tl=class{dirtyEffectCount=0;queues=new Map;add(t){this.enqueue(t),this.schedule(t)}schedule(t){t.dirty&&this.dirtyEffectCount++}remove(t){let n=t.zone,r=this.queues.get(n);r.has(t)&&(r.delete(t),t.dirty&&this.dirtyEffectCount--)}enqueue(t){let n=t.zone;this.queues.has(n)||this.queues.set(n,new Set);let r=this.queues.get(n);r.has(t)||r.add(t)}flush(){for(;this.dirtyEffectCount>0;){let t=!1;for(let[n,r]of this.queues)n===null?t||=this.flushQueue(r):t||=n.run(()=>this.flushQueue(r));t||(this.dirtyEffectCount=0)}}flushQueue(t){let n=!1;for(let r of t)r.dirty&&(this.dirtyEffectCount--,n=!0,r.run());return n}};function ko(e){return{toString:e}.toString()}function kD(e){return typeof e=="function"}var As=class{previousValue;currentValue;firstChange;constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function uh(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}var Ln=(()=>{let e=()=>dh;return e.ngInherit=!0,e})();function dh(e){return e.type.prototype.ngOnChanges&&(e.setInput=PD),OD}function OD(){let e=ph(this),t=e?.current;if(t){let n=e.previous;if(n===on)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function PD(e,t,n,r,o){let i=this.declaredInputs[r],s=ph(e)||FD(e,{previous:on,current:null}),a=s.current||(s.current={}),c=s.previous,l=c[i];a[i]=new As(l&&l.currentValue,n,c===on),uh(e,t,o,n)}var fh="__ngSimpleChanges__";function ph(e){return e[fh]||null}function FD(e,t){return e[fh]=t}var jp=[];var ne=function(e,t=null,n){for(let r=0;r<jp.length;r++){let o=jp[r];o(e,t,n)}};function LD(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=dh(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function pu(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:l,ngOnDestroy:u}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),c&&(e.viewHooks??=[]).push(-n,c),l&&((e.viewHooks??=[]).push(n,l),(e.viewCheckHooks??=[]).push(n,l)),u!=null&&(e.destroyHooks??=[]).push(n,u)}}function Ss(e,t,n){hh(e,t,3,n)}function Ts(e,t,n,r){(e[x]&3)===n&&hh(e,t,n,r)}function jl(e,t){let n=e[x];(n&3)===t&&(n&=16383,n+=1,e[x]=n)}function hh(e,t,n,r){let o=r!==void 0?e[An]&65535:0,i=r??-1,s=t.length-1,a=0;for(let c=o;c<s;c++)if(typeof t[c+1]=="number"){if(a=t[c],r!=null&&a>=r)break}else t[c]<0&&(e[An]+=65536),(a<i||i==-1)&&(jD(e,n,t,c),e[An]=(e[An]&**********)+c+2),c++}function Vp(e,t){ne(4,e,t);let n=$(null);try{t.call(e)}finally{$(n),ne(5,e,t)}}function jD(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[x]>>14<e[An]>>16&&(e[x]&3)===t&&(e[x]+=16384,Vp(a,i)):Vp(a,i)}var Ir=-1,Ao=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(t,n,r){this.factory=t,this.canSeeViewProviders=n,this.injectImpl=r}};function VD(e){return(e.flags&8)!==0}function BD(e){return(e.flags&16)!==0}function HD(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];$D(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function mh(e){return e===3||e===4||e===6}function $D(e){return e.charCodeAt(0)===64}function Er(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?Bp(e,n,o,null,t[++r]):Bp(e,n,o,null,null))}}return e}function Bp(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),o!==null&&e.splice(i++,0,o)}function gh(e){return e!==Ir}function Rs(e){return e&32767}function UD(e){return e>>16}function Ns(e,t){let n=UD(e),r=t;for(;n>0;)r=r[vr],n--;return r}var Ul=!0;function Hp(e){let t=Ul;return Ul=e,t}var zD=256,vh=zD-1,yh=5,GD=0,bt={};function WD(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(xn)&&(r=n[xn]),r==null&&(r=n[xn]=GD++);let o=r&vh,i=1<<o;t.data[e+(o>>yh)]|=i}function Dh(e,t){let n=Ch(e,t);if(n!==-1)return n;let r=t[A];r.firstCreatePass&&(e.injectorIndex=t.length,Vl(r.data,e),Vl(t,null),Vl(r.blueprint,null));let o=hu(e,t),i=e.injectorIndex;if(gh(o)){let s=Rs(o),a=Ns(o,t),c=a[A].data;for(let l=0;l<8;l++)t[i+l]=a[s+l]|c[s+l]}return t[i+8]=o,i}function Vl(e,t){e.push(0,0,0,0,0,0,0,0,t)}function Ch(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function hu(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=_h(o),r===null)return Ir;if(n++,o=o[vr],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return Ir}function qD(e,t,n){WD(e,t,n)}function ZD(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let n=e.attrs;if(n){let r=n.length,o=0;for(;o<r;){let i=n[o];if(mh(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof n[o]=="string";)o++;else{if(i===t)return n[o+1];o=o+2}}}return null}function bh(e,t,n){if(n&8||e!==void 0)return e;ls(t,"NodeInjector")}function Ih(e,t,n,r){if(n&8&&r===void 0&&(r=null),(n&3)===0){let o=e[mr],i=Ue(void 0);try{return o?o.get(t,r,n&8):fl(t,r,n&8)}finally{Ue(i)}}return bh(r,t,n)}function Eh(e,t,n,r=0,o){if(e!==null){if(t[x]&2048&&!(r&2)){let s=XD(e,t,n,r,bt);if(s!==bt)return s}let i=wh(e,t,n,r,bt);if(i!==bt)return i}return Ih(t,n,r,o)}function wh(e,t,n,r,o){let i=QD(n);if(typeof i=="function"){if(!Rl(t,e,r))return r&1?bh(o,n,r):Ih(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&8))ls(n);else return s}finally{Nl()}}else if(typeof i=="number"){let s=null,a=Ch(e,t),c=Ir,l=r&1?t[He][Oe]:null;for((a===-1||r&4)&&(c=a===-1?hu(e,t):t[a+8],c===Ir||!Up(r,!1)?a=-1:(s=t[A],a=Rs(c),t=Ns(c,t)));a!==-1;){let u=t[A];if($p(i,a,u.data)){let f=YD(a,t,n,s,r,l);if(f!==bt)return f}c=t[a+8],c!==Ir&&Up(r,t[A].data[a+8]===l)&&$p(i,a,t)?(s=u,a=Rs(c),t=Ns(c,t)):a=-1}}return o}function YD(e,t,n,r,o,i){let s=t[A],a=s.data[e+8],c=r==null?Nn(a)&&Ul:r!=s&&(a.type&3)!==0,l=o&1&&i===a,u=Ms(a,s,n,c,l);return u!==null?ks(t,s,u,a):bt}function Ms(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,c=e.directiveStart,l=e.directiveEnd,u=i>>20,f=r?a:a+u,p=o?a+u:l;for(let d=f;d<p;d++){let m=s[d];if(d<c&&n===m||d>=c&&m.type===n)return d}if(o){let d=s[c];if(d&&Vt(d)&&d.type===n)return c}return null}function ks(e,t,n,r){let o=e[n],i=t.data;if(o instanceof Ao){let s=o;s.resolving&&dl(ep(i[n]));let a=Hp(s.canSeeViewProviders);s.resolving=!0;let c=i[n].type||i[n],l,u=s.injectImpl?Ue(s.injectImpl):null,f=Rl(e,r,0);try{o=e[n]=s.factory(void 0,i,e,r),t.firstCreatePass&&n>=r.directiveStart&&LD(n,i[n],t)}finally{u!==null&&Ue(u),Hp(a),s.resolving=!1,Nl()}}return o}function QD(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(xn)?e[xn]:void 0;return typeof t=="number"?t>=0?t&vh:KD:t}function $p(e,t,n){let r=1<<e;return!!(n[t+(e>>yh)]&r)}function Up(e,t){return!(e&2)&&!(e&1&&t)}var On=class{_tNode;_lView;constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return Eh(this._tNode,this._lView,t,_n(r),n)}};function KD(){return new On(Je(),se())}function _r(e){return ko(()=>{let t=e.prototype.constructor,n=t[po]||zl(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[po]||zl(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function zl(e){return ol(e)?()=>{let t=zl(Be(e));return t&&t()}:Sn(e)}function XD(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[x]&2048&&!yr(s);){let a=wh(i,s,n,r|2,bt);if(a!==bt)return a;let c=i.parent;if(!c){let l=s[bl];if(l){let u=l.get(n,bt,r);if(u!==bt)return u}c=_h(s),s=s[vr]}i=c}return o}function _h(e){let t=e[A],n=t.type;return n===2?t.declTNode:n===1?e[Oe]:null}function $t(e){return ZD(Je(),e)}function JD(){return Sr(Je(),se())}function Sr(e,t){return new we(Bt(e,t))}var we=(()=>{class e{nativeElement;constructor(n){this.nativeElement=n}static __NG_ELEMENT_ID__=JD}return e})();function e0(e){return e instanceof we?e.nativeElement:e}function t0(){return this._results[Symbol.iterator]()}var Os=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new te}constructor(t=!1){this._emitDistinctChangesOnly=t}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){this.dirty=!1;let r=rp(t);(this._changesDetected=!np(this._results,r,n))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(t){this._onDirty=t}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=t0};function Sh(e){return(e.flags&128)===128}var mu=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(mu||{}),Th=new Map,n0=0;function r0(){return n0++}function o0(e){Th.set(e[Do],e)}function Gl(e){Th.delete(e[Do])}var zp="__ngContext__";function Tr(e,t){jt(t)?(e[zp]=t[Do],o0(t)):e[zp]=t}function Mh(e){return Ah(e[gr])}function xh(e){return Ah(e[Xe])}function Ah(e){for(;e!==null&&!st(e);)e=e[Xe];return e}var Wl;function gu(e){Wl=e}function Rh(){if(Wl!==void 0)return Wl;if(typeof document<"u")return document;throw new E(210,!1)}var Us=new w("",{providedIn:"root",factory:()=>i0}),i0="ng",zs=new w(""),Mr=new w("",{providedIn:"platform",factory:()=>"unknown"});var Gs=new w("",{providedIn:"root",factory:()=>Rh().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var s0="h",a0="b";var Nh=!1,kh=new w("",{providedIn:"root",factory:()=>Nh});var c0=()=>null;function Oh(e,t,n=!1){return c0(e,t,n)}function Ph(e,t){let n=e.contentQueries;if(n!==null){let r=$(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];Is(i),a.contentQueries(2,t[s],s)}}}finally{$(r)}}}function ql(e,t,n){Is(0);let r=$(null);try{t(e,n)}finally{$(r)}}function vu(e,t,n){if(ps(t)){let r=$(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=n[s];a.contentQueries(1,c,s)}}}finally{$(r)}}}var Ht=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(Ht||{});var l0=/^>|^->|<!--|-->|--!>|<!-$/g,u0=/(<|>)/g,d0="\u200B$1\u200B";function f0(e){return e.replace(l0,t=>t.replace(u0,d0))}function Fh(e){return e instanceof Function?e():e}function p0(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}var Lh="ng-template";function h0(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&p0(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(yu(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function yu(e){return e.type===4&&e.value!==Lh}function m0(e,t,n){let r=e.type===4&&!n?Lh:e.value;return t===r}function g0(e,t,n){let r=4,o=e.attrs,i=o!==null?D0(o):0,s=!1;for(let a=0;a<t.length;a++){let c=t[a];if(typeof c=="number"){if(!s&&!ct(r)&&!ct(c))return!1;if(s&&ct(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!m0(e,c,n)||c===""&&t.length===1){if(ct(r))return!1;s=!0}}else if(r&8){if(o===null||!h0(e,o,c,n)){if(ct(r))return!1;s=!0}}else{let l=t[++a],u=v0(c,o,yu(e),n);if(u===-1){if(ct(r))return!1;s=!0;continue}if(l!==""){let f;if(u>i?f="":f=o[u+1].toLowerCase(),r&2&&l!==f){if(ct(r))return!1;s=!0}}}}return ct(r)||s}function ct(e){return(e&1)===0}function v0(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return C0(t,e)}function jh(e,t,n=!1){for(let r=0;r<t.length;r++)if(g0(e,t[r],n))return!0;return!1}function y0(e){let t=e.attrs;if(t!=null){let n=t.indexOf(5);if((n&1)===0)return t[n+1]}return null}function D0(e){for(let t=0;t<e.length;t++){let n=e[t];if(mh(n))return t}return e.length}function C0(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function b0(e,t){e:for(let n=0;n<t.length;n++){let r=t[n];if(e.length===r.length){for(let o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function Gp(e,t){return e?":not("+t.trim()+")":t}function I0(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!ct(s)&&(t+=Gp(i,o),o=""),r=s,i=i||!ct(r);n++}return o!==""&&(t+=Gp(i,o)),t}function E0(e){return e.map(I0).join(",")}function w0(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!ct(o))break;o=i}r++}return n.length&&t.push(1,...n),t}var Ws={};function _0(e,t){return e.createText(t)}function S0(e,t){return e.createComment(f0(t))}function Vh(e,t,n){return e.createElement(t,n)}function Ps(e,t,n,r,o){e.insertBefore(t,n,r,o)}function Bh(e,t,n){e.appendChild(t,n)}function Wp(e,t,n,r,o){r!==null?Ps(e,t,n,r,o):Bh(e,t,n)}function T0(e,t,n){e.removeChild(null,t,n)}function M0(e,t,n){e.setAttribute(t,"style",n)}function x0(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function Hh(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&HD(e,t,r),o!==null&&x0(e,t,o),i!==null&&M0(e,t,i)}function Du(e,t,n,r,o,i,s,a,c,l,u){let f=$e+r,p=f+o,d=A0(f,p),m=typeof l=="function"?l():l;return d[A]={type:e,blueprint:d,template:n,queries:null,viewQuery:a,declTNode:t,data:d.slice().fill(null,f),bindingStartIndex:f,expandoStartIndex:p,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:m,incompleteFirstPass:!1,ssrId:u}}function A0(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:Ws);return n}function R0(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=Du(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function Cu(e,t,n,r,o,i,s,a,c,l,u){let f=t.blueprint.slice();return f[it]=o,f[x]=r|4|128|8|64|1024,(l!==null||e&&e[x]&2048)&&(f[x]|=2048),Il(f),f[Ie]=f[vr]=e,f[We]=n,f[Lt]=s||e&&e[Lt],f[Re]=a||e&&e[Re],f[mr]=c||e&&e[mr]||null,f[Oe]=i,f[Do]=r0(),f[hr]=u,f[bl]=l,f[He]=t.type==2?e[He]:f,f}function N0(e,t,n){let r=Bt(t,e),o=R0(n),i=e[Lt].rendererFactory,s=bu(e,Cu(e,o,null,$h(n),r,t,null,i.createRenderer(r,n),null,null,null));return e[t.index]=s}function $h(e){let t=16;return e.signals?t=4096:e.onPush&&(t=64),t}function Uh(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function bu(e,t){return e[gr]?e[Cl][Xe]=t:e[gr]=t,e[Cl]=t,t}function zh(e=1){Gh(at(),se(),kl()+e,!1)}function Gh(e,t,n,r){if(!r)if((t[x]&3)===3){let i=e.preOrderCheckHooks;i!==null&&Ss(t,i,n)}else{let i=e.preOrderHooks;i!==null&&Ts(t,i,0,n)}un(n)}var qs=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(qs||{});function Zl(e,t,n,r){let o=$(null);try{let[i,s,a]=e.inputs[n],c=null;(s&qs.SignalBased)!==0&&(c=t[i][Ni]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(t,r)),e.setInput!==null?e.setInput(t,c,r,n,i):uh(t,c,i,r)}finally{$(o)}}function Wh(e,t,n,r,o){let i=kl(),s=r&2;try{un(-1),s&&t.length>$e&&Gh(e,t,$e,!1),ne(s?2:0,o,n),n(r,o)}finally{un(i),ne(s?3:1,o,n)}}function Zs(e,t,n){B0(e,t,n),(n.flags&64)===64&&H0(e,t,n)}function Iu(e,t,n=Bt){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function k0(e,t,n,r){let i=r.get(kh,Nh)||n===Ht.ShadowDom,s=e.selectRootElement(t,i);return O0(s),s}function O0(e){P0(e)}var P0=()=>null;function F0(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function L0(e,t,n,r,o,i){let s=t[A];if(wu(e,s,t,n,r)){Nn(e)&&V0(t,e.index);return}j0(e,t,n,r,o,i)}function j0(e,t,n,r,o,i){if(e.type&3){let s=Bt(e,t);n=F0(n),r=i!=null?i(r,e.value||"",n):r,o.setProperty(s,n,r)}else e.type&12}function V0(e,t){let n=Dt(t,e);n[x]&16||(n[x]|=64)}function B0(e,t,n){let r=n.directiveStart,o=n.directiveEnd;Nn(n)&&N0(t,n,e.data[r+n.componentOffset]),e.firstCreatePass||Dh(n,t);let i=n.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=ks(t,e,s,n);if(Tr(c,t),i!==null&&U0(t,s-r,c,a,n,i),Vt(a)){let l=Dt(n.index,t);l[We]=ks(t,e,s,n)}}}function H0(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=Ap();try{un(i);for(let a=r;a<o;a++){let c=e.data[a],l=t[a];bs(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&$0(c,l)}}finally{un(-1),bs(s)}}function $0(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function Eu(e,t){let n=e.directiveRegistry,r=null;if(n)for(let o=0;o<n.length;o++){let i=n[o];jh(t,i.selectors,!1)&&(r??=[],Vt(i)?r.unshift(i):r.push(i))}return r}function U0(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],l=s[a+1];Zl(r,n,c,l)}}function wu(e,t,n,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let l=s[c],u=s[c+1],f=t.data[l];Zl(f,n[l],u,o),a=!0}if(i)for(let c of i){let l=n[c],u=t.data[c];Zl(u,l,r,o),a=!0}return a}function z0(e,t){let n=Dt(t,e),r=n[A];G0(r,n);let o=n[it];o!==null&&n[hr]===null&&(n[hr]=Oh(o,n[mr])),ne(18),_u(r,n,n[We]),ne(19,n[We])}function G0(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function _u(e,t,n){Es(t);try{let r=e.viewQuery;r!==null&&ql(1,r,n);let o=e.template;o!==null&&Wh(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[vt]?.finishViewCreation(e),e.staticContentQueries&&Ph(e,t),e.staticViewQueries&&ql(2,e.viewQuery,n);let i=e.components;i!==null&&W0(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[x]&=-5,ws()}}function W0(e,t){for(let n=0;n<t.length;n++)z0(e,t[n])}function qh(e,t,n,r){let o=$(null);try{let i=t.tView,a=e[x]&4096?4096:16,c=Cu(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),l=e[t.index];c[an]=l;let u=e[vt];return u!==null&&(c[vt]=u.createEmbeddedView(i)),_u(i,c,n),c}finally{$(o)}}function Yl(e,t){return!t||t.firstChild===null||Sh(e)}var qp=!1,q0=new w(""),Z0;function Su(e,t){return Z0(e,t)}var jn=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(jn||{});function Ys(e){return(e.flags&32)===32}function br(e,t,n,r,o){if(r!=null){let i,s=!1;st(r)?i=r:jt(r)&&(s=!0,r=r[it]);let a=yt(r);e===0&&n!==null?o==null?Bh(t,n,a):Ps(t,n,a,o||null,!0):e===1&&n!==null?Ps(t,n,a,o||null,!0):e===2?T0(t,a,s):e===3&&t.destroyNode(a),i!=null&&oC(t,e,i,n,o)}}function Y0(e,t){Zh(e,t),t[it]=null,t[Oe]=null}function Q0(e,t,n,r,o,i){r[it]=o,r[Oe]=t,Ks(e,r,n,1,o,i)}function Zh(e,t){t[Lt].changeDetectionScheduler?.notify(9),Ks(e,t,t[Re],2,null,null)}function K0(e){let t=e[gr];if(!t)return Bl(e[A],e);for(;t;){let n=null;if(jt(t))n=t[gr];else{let r=t[Pe];r&&(n=r)}if(!n){for(;t&&!t[Xe]&&t!==e;)jt(t)&&Bl(t[A],t),t=t[Ie];t===null&&(t=e),jt(t)&&Bl(t[A],t),n=t&&t[Xe]}t=n}}function Tu(e,t){let n=e[Rn],r=n.indexOf(t);n.splice(r,1)}function Yh(e,t){if(kn(t))return;let n=t[Re];n.destroyNode&&Ks(e,t,n,3,null,null),K0(t)}function Bl(e,t){if(kn(t))return;let n=$(null);try{t[x]&=-129,t[x]|=256,t[qe]&&Tc(t[qe]),J0(e,t),X0(e,t),t[A].type===1&&t[Re].destroy();let r=t[an];if(r!==null&&st(t[Ie])){r!==t[Ie]&&Tu(r,t);let o=t[vt];o!==null&&o.detachView(e)}Gl(t)}finally{$(n)}}function X0(e,t){let n=e.cleanup,r=t[yo];if(n!==null)for(let s=0;s<n.length-1;s+=2)if(typeof n[s]=="string"){let a=n[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[n[s+1]];n[s].call(a)}r!==null&&(t[yo]=null);let o=t[Ft];if(o!==null){t[Ft]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=t[Co];if(i!==null){t[Co]=null;for(let s of i)s.destroy()}}function J0(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof Ao)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];ne(4,a,c);try{c.call(a)}finally{ne(5,a,c)}}else{ne(4,o,i);try{i.call(o)}finally{ne(5,o,i)}}}}}function Qh(e,t,n){return eC(e,t.parent,n)}function eC(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[it];if(Nn(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===Ht.None||o===Ht.Emulated)return null}return Bt(r,n)}function Kh(e,t,n){return nC(e,t,n)}function tC(e,t,n){return e.type&40?Bt(e,n):null}var nC=tC,Zp;function Qs(e,t,n,r){let o=Qh(e,r,t),i=t[Re],s=r.parent||t[Oe],a=Kh(s,r,t);if(o!=null)if(Array.isArray(n))for(let c=0;c<n.length;c++)Wp(i,o,n[c],a,!1);else Wp(i,o,n,a,!1);Zp!==void 0&&Zp(i,r,t,n,o)}function Mo(e,t){if(t!==null){let n=t.type;if(n&3)return Bt(t,e);if(n&4)return Ql(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return Mo(e,r);{let o=e[t.index];return st(o)?Ql(-1,o):yt(o)}}else{if(n&128)return Mo(e,t.next);if(n&32)return Su(t,e)()||yt(e[t.index]);{let r=Xh(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=rn(e[He]);return Mo(o,r)}else return Mo(e,t.next)}}}return null}function Xh(e,t){if(t!==null){let r=e[He][Oe],o=t.projection;return r.projection[o]}return null}function Ql(e,t){let n=Pe+e+1;if(n<t.length){let r=t[n],o=r[A].firstChild;if(o!==null)return Mo(r,o)}return t[cn]}function Mu(e,t,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],c=n.type;if(s&&t===0&&(a&&Tr(yt(a),r),n.flags|=2),!Ys(n))if(c&8)Mu(e,t,n.child,r,o,i,!1),br(t,e,o,a,i);else if(c&32){let l=Su(n,r),u;for(;u=l();)br(t,e,o,u,i);br(t,e,o,a,i)}else c&16?Jh(e,t,r,n,o,i):br(t,e,o,a,i);n=s?n.projectionNext:n.next}}function Ks(e,t,n,r,o,i){Mu(n,r,e.firstChild,t,o,i,!1)}function rC(e,t,n){let r=t[Re],o=Qh(e,n,t),i=n.parent||t[Oe],s=Kh(i,n,t);Jh(r,0,t,n,o,s)}function Jh(e,t,n,r,o,i){let s=n[He],c=s[Oe].projection[r.projection];if(Array.isArray(c))for(let l=0;l<c.length;l++){let u=c[l];br(t,e,o,u,i)}else{let l=c,u=s[Ie];Sh(r)&&(l.flags|=128),Mu(e,t,l,u,o,i,!0)}}function oC(e,t,n,r,o){let i=n[cn],s=yt(n);i!==s&&br(t,e,r,i,o);for(let a=Pe;a<n.length;a++){let c=n[a];Ks(c[A],c,e,t,r,i)}}function Ro(e,t,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=t[n.index];i!==null&&r.push(yt(i)),st(i)&&em(i,r);let s=n.type;if(s&8)Ro(e,t,n.child,r);else if(s&32){let a=Su(n,t),c;for(;c=a();)r.push(c)}else if(s&16){let a=Xh(t,n);if(Array.isArray(a))r.push(...a);else{let c=rn(t[He]);Ro(c[A],c,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function em(e,t){for(let n=Pe;n<e.length;n++){let r=e[n],o=r[A].firstChild;o!==null&&Ro(r[A],r,o,t)}e[cn]!==e[it]&&t.push(e[cn])}function tm(e){if(e[fs]!==null){for(let t of e[fs])t.impl.addSequence(t);e[fs].length=0}}var nm=[];function iC(e){return e[qe]??sC(e)}function sC(e){let t=nm.pop()??Object.create(cC);return t.lView=e,t}function aC(e){e.lView[qe]!==e&&(e.lView=null,nm.push(e))}var cC=U(y({},ki),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{Eo(e.lView)},consumerOnSignalRead(){this.lView[qe]=this}});function lC(e){let t=e[qe]??Object.create(uC);return t.lView=e,t}var uC=U(y({},ki),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let t=rn(e.lView);for(;t&&!rm(t[A]);)t=rn(t);t&&El(t)},consumerOnSignalRead(){this.lView[qe]=this}});function rm(e){return e.type!==2}function om(e){if(e[Co]===null)return;let t=!0;for(;t;){let n=!1;for(let r of e[Co])r.dirty&&(n=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));t=n&&!!(e[x]&8192)}}var dC=100;function xu(e,t=0){let r=e[Lt].rendererFactory,o=!1;o||r.begin?.();try{fC(e,t)}finally{o||r.end?.()}}function fC(e,t){let n=Ml();try{xl(!0),Kl(e,t);let r=0;for(;Io(e);){if(r===dC)throw new E(103,!1);r++,Kl(e,1)}}finally{xl(n)}}function im(e,t){Tl(t?wo.Exhaustive:wo.OnlyDirtyViews);try{xu(e)}finally{Tl(wo.Off)}}function pC(e,t,n,r){if(kn(t))return;let o=t[x],i=!1,s=!1;Es(t);let a=!0,c=null,l=null;i||(rm(e)?(l=iC(t),c=Oi(l)):Ec()===null?(a=!1,l=lC(t),c=Oi(l)):t[qe]&&(Tc(t[qe]),t[qe]=null));try{Il(t),Sp(e.bindingStartIndex),n!==null&&Wh(e,t,n,2,r);let u=(o&3)===3;if(!i)if(u){let d=e.preOrderCheckHooks;d!==null&&Ss(t,d,null)}else{let d=e.preOrderHooks;d!==null&&Ts(t,d,0,null),jl(t,0)}if(s||hC(t),om(t),sm(t,0),e.contentQueries!==null&&Ph(e,t),!i)if(u){let d=e.contentCheckHooks;d!==null&&Ss(t,d)}else{let d=e.contentHooks;d!==null&&Ts(t,d,1),jl(t,1)}gC(e,t);let f=e.components;f!==null&&cm(t,f,0);let p=e.viewQuery;if(p!==null&&ql(2,p,r),!i)if(u){let d=e.viewCheckHooks;d!==null&&Ss(t,d)}else{let d=e.viewHooks;d!==null&&Ts(t,d,2),jl(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[ds]){for(let d of t[ds])d();t[ds]=null}i||(tm(t),t[x]&=-73)}catch(u){throw i||Eo(t),u}finally{l!==null&&(Sc(l,c),a&&aC(l)),ws()}}function sm(e,t){for(let n=Mh(e);n!==null;n=xh(n))for(let r=Pe;r<n.length;r++){let o=n[r];am(o,t)}}function hC(e){for(let t=Mh(e);t!==null;t=xh(t)){if(!(t[x]&2))continue;let n=t[Rn];for(let r=0;r<n.length;r++){let o=n[r];El(o)}}}function mC(e,t,n){ne(18);let r=Dt(t,e);am(r,n),ne(19,r[We])}function am(e,t){gs(e)&&Kl(e,t)}function Kl(e,t){let r=e[A],o=e[x],i=e[qe],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&Pi(i)),s||=!1,i&&(i.dirty=!1),e[x]&=-9217,s)pC(r,e,r.template,e[We]);else if(o&8192){let a=$(null);try{om(e),sm(e,1);let c=r.components;c!==null&&cm(e,c,1),tm(e)}finally{$(a)}}}function cm(e,t,n){for(let r=0;r<t.length;r++)mC(e,t[r],n)}function gC(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)un(~o);else{let i=o,s=n[++r],a=n[++r];xp(s,i);let c=t[i];ne(24,c),a(2,c),ne(25,c)}}}finally{un(-1)}}function lm(e,t){let n=Ml()?64:1088;for(e[Lt].changeDetectionScheduler?.notify(t);e;){e[x]|=n;let r=rn(e);if(yr(e)&&!r)return e;e=r}return null}function um(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function dm(e,t,n,r=!0){let o=t[A];if(vC(o,t,e,n),r){let s=Ql(n,e),a=t[Re],c=a.parentNode(e[cn]);c!==null&&Q0(o,e[Oe],a,t,c,s)}let i=t[hr];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function Xl(e,t){if(e.length<=Pe)return;let n=Pe+t,r=e[n];if(r){let o=r[an];o!==null&&o!==e&&Tu(o,r),t>0&&(e[n-1][Xe]=r[Xe]);let i=go(e,Pe+t);Y0(r[A],r);let s=i[vt];s!==null&&s.detachView(i[A]),r[Ie]=null,r[Xe]=null,r[x]&=-129}return r}function vC(e,t,n,r){let o=Pe+r,i=n.length;r>0&&(n[o-1][Xe]=t),r<i-Pe?(t[Xe]=n[o],pl(n,Pe+r,t)):(n.push(t),t[Xe]=null),t[Ie]=n;let s=t[an];s!==null&&n!==s&&fm(s,t);let a=t[vt];a!==null&&a.insertView(e),vs(t),t[x]|=128}function fm(e,t){let n=e[Rn],r=t[Ie];if(jt(r))e[x]|=2;else{let o=r[Ie][He];t[He]!==o&&(e[x]|=2)}n===null?e[Rn]=[t]:n.push(t)}var fn=class{_lView;_cdRefInjectingView;_appRef=null;_attachedToViewContainer=!1;exhaustive;get rootNodes(){let t=this._lView,n=t[A];return Ro(n,t,n.firstChild,[])}constructor(t,n){this._lView=t,this._cdRefInjectingView=n}get context(){return this._lView[We]}set context(t){this._lView[We]=t}get destroyed(){return kn(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[Ie];if(st(t)){let n=t[bo],r=n?n.indexOf(this):-1;r>-1&&(Xl(t,r),go(n,r))}this._attachedToViewContainer=!1}Yh(this._lView[A],this._lView)}onDestroy(t){wl(this._lView,t)}markForCheck(){lm(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[x]&=-129}reattach(){vs(this._lView),this._lView[x]|=128}detectChanges(){this._lView[x]|=1024,xu(this._lView)}checkNoChanges(){return;try{this.exhaustive??=this._lView[mr].get(q0,qp)}catch{this.exhaustive=qp}}attachToViewContainerRef(){if(this._appRef)throw new E(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=yr(this._lView),n=this._lView[an];n!==null&&!t&&Tu(n,this._lView),Zh(this._lView[A],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new E(902,!1);this._appRef=t;let n=yr(this._lView),r=this._lView[an];r!==null&&!n&&fm(r,this._lView),vs(this._lView)}};var Pn=(()=>{class e{_declarationLView;_declarationTContainer;elementRef;static __NG_ELEMENT_ID__=yC;constructor(n,r,o){this._declarationLView=n,this._declarationTContainer=r,this.elementRef=o}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(n,r){return this.createEmbeddedViewImpl(n,r)}createEmbeddedViewImpl(n,r,o){let i=qh(this._declarationLView,this._declarationTContainer,n,{embeddedViewInjector:r,dehydratedView:o});return new fn(i)}}return e})();function yC(){return Au(Je(),se())}function Au(e,t){return e.type&4?new Pn(t,e,Sr(e,t)):null}function Oo(e,t,n,r,o){let i=e.data[t];if(i===null)i=DC(e,t,n,r,o),Mp()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=_p();i.injectorIndex=s===null?-1:s.injectorIndex}return ln(i,!0),i}function DC(e,t,n,r,o){let i=Sl(),s=Ds(),a=s?i:i&&i.parent,c=e.data[t]=bC(e,a,n,t,r,o);return CC(e,c,i,s),c}function CC(e,t,n,r){e.firstChild===null&&(e.firstChild=t),n!==null&&(r?n.child==null&&t.parent!==null&&(n.child=t):n.next===null&&(n.next=t,t.prev=n))}function bC(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return _l()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var tO=new RegExp(`^(\\d+)*(${a0}|${s0})*(.*)`);var IC=()=>null;function Jl(e,t){return IC(e,t)}var pm=class{},Xs=class{},eu=class{resolveComponentFactory(t){throw new E(917,!1)}},Po=class{static NULL=new eu},Fn=class{};var hm=(()=>{class e{static \u0275prov=C({token:e,providedIn:"root",factory:()=>null})}return e})();var xs={},tu=class{injector;parentInjector;constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){let o=this.injector.get(t,xs,r);return o!==xs||n===xs?o:this.parentInjector.get(t,n,r)}};function nu(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=rl(o,a);else if(i==2){let c=a,l=t[++s];r=rl(r,c+": "+l+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}function j(e,t=0){let n=se();if(n===null)return I(e,t);let r=Je();return Eh(r,n,Be(e),t)}function Ru(e,t,n,r,o){let i=r===null?null:{"":-1},s=o(e,n);if(s!==null){let a=s,c=null,l=null;for(let u of s)if(u.resolveHostDirectives!==null){[a,c,l]=u.resolveHostDirectives(s);break}_C(e,t,n,a,i,c,l)}i!==null&&r!==null&&EC(n,r,i)}function EC(e,t,n){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new E(-301,!1);r.push(t[o],i)}}function wC(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function _C(e,t,n,r,o,i,s){let a=r.length,c=!1;for(let p=0;p<a;p++){let d=r[p];!c&&Vt(d)&&(c=!0,wC(e,n,p)),qD(Dh(n,t),e,d.type)}RC(n,e.data.length,a);for(let p=0;p<a;p++){let d=r[p];d.providersResolver&&d.providersResolver(d)}let l=!1,u=!1,f=Uh(e,t,a,null);a>0&&(n.directiveToIndex=new Map);for(let p=0;p<a;p++){let d=r[p];if(n.mergedAttrs=Er(n.mergedAttrs,d.hostAttrs),TC(e,n,t,f,d),AC(f,d,o),s!==null&&s.has(d)){let[g,v]=s.get(d);n.directiveToIndex.set(d.type,[f,g+n.directiveStart,v+n.directiveStart])}else(i===null||!i.has(d))&&n.directiveToIndex.set(d.type,f);d.contentQueries!==null&&(n.flags|=4),(d.hostBindings!==null||d.hostAttrs!==null||d.hostVars!==0)&&(n.flags|=64);let m=d.type.prototype;!l&&(m.ngOnChanges||m.ngOnInit||m.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),l=!0),!u&&(m.ngOnChanges||m.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),u=!0),f++}SC(e,n,i)}function SC(e,t,n){for(let r=t.directiveStart;r<t.directiveEnd;r++){let o=e.data[r];if(n===null||!n.has(o))Yp(0,t,o,r),Yp(1,t,o,r),Kp(t,r,!1);else{let i=n.get(o);Qp(0,t,i,r),Qp(1,t,i,r),Kp(t,r,!0)}}}function Yp(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=t.inputs??={}:s=t.outputs??={},s[i]??=[],s[i].push(r),mm(t,i)}}function Qp(e,t,n,r){let o=e===0?n.inputs:n.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=t.hostDirectiveInputs??={}:a=t.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),mm(t,s)}}function mm(e,t){t==="class"?e.flags|=8:t==="style"&&(e.flags|=16)}function Kp(e,t,n){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!n&&o===null||n&&i===null||yu(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!n&&o.hasOwnProperty(c)){let l=o[c];for(let u of l)if(u===t){s??=[],s.push(c,r[a+1]);break}}else if(n&&i.hasOwnProperty(c)){let l=i[c];for(let u=0;u<l.length;u+=2)if(l[u]===t){s??=[],s.push(l[u+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function TC(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=Sn(o.type,!0)),s=new Ao(i,Vt(o),j);e.blueprint[r]=s,n[r]=s,MC(e,t,r,Uh(e,n,o.hostVars,Ws),o)}function MC(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;xC(s)!=a&&s.push(a),s.push(n,r,i)}}function xC(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function AC(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;Vt(t)&&(n[""]=e)}}function RC(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function gm(e,t,n,r,o,i,s,a){let c=t.consts,l=Dr(c,s),u=Oo(t,e,2,r,l);return i&&Ru(t,n,u,Dr(c,a),o),u.mergedAttrs=Er(u.mergedAttrs,u.attrs),u.attrs!==null&&nu(u,u.attrs,!1),u.mergedAttrs!==null&&nu(u,u.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,u),u}function vm(e,t){pu(e,t),ps(t)&&e.queries.elementEnd(t)}function NC(e,t,n){if(n===Ws)return!1;let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}var ru=Symbol("BINDING");var Fs=class extends Po{ngModule;constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=gt(t);return new pn(n,this.ngModule)}};function kC(e){return Object.keys(e).map(t=>{let[n,r,o]=e[t],i={propName:n,templateName:t,isSignal:(r&qs.SignalBased)!==0};return o&&(i.transform=o),i})}function OC(e){return Object.keys(e).map(t=>({propName:e[t],templateName:t}))}function PC(e,t,n){let r=t instanceof ee?t:t?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new tu(n,r):n}function FC(e){let t=e.get(Fn,null);if(t===null)throw new E(407,!1);let n=e.get(hm,null),r=e.get(Mn,null);return{rendererFactory:t,sanitizer:n,changeDetectionScheduler:r,ngReflect:!1}}function LC(e,t){let n=(e.selectors[0][0]||"div").toLowerCase();return Vh(t,n,n==="svg"?fp:n==="math"?pp:null)}var pn=class extends Xs{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=kC(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=OC(this.componentDef.outputs),this.cachedOutputs}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=E0(t.selectors),this.ngContentSelectors=t.ngContentSelectors??[],this.isBoundToModule=!!n}create(t,n,r,o,i,s){ne(22);let a=$(null);try{let c=this.componentDef,l=jC(r,c,s,i),u=PC(c,o||this.ngModule,t),f=FC(u),p=f.rendererFactory.createRenderer(null,c),d=r?k0(p,r,c.encapsulation,u):LC(c,p),m=s?.some(Xp)||i?.some(D=>typeof D!="function"&&D.bindings.some(Xp)),g=Cu(null,l,null,512|$h(c),null,null,f,p,u,null,Oh(d,u,!0));g[$e]=d,Es(g);let v=null;try{let D=gm($e,l,g,"#host",()=>l.directiveRegistry,!0,0);d&&(Hh(p,d,D),Tr(d,g)),Zs(l,g,D),vu(l,D,g),vm(l,D),n!==void 0&&BC(D,this.ngContentSelectors,n),v=Dt(D.index,g),g[We]=v[We],_u(l,g,null)}catch(D){throw v!==null&&Gl(v),Gl(g),D}finally{ne(23),ws()}return new Ls(this.componentType,g,!!m)}finally{$(a)}}};function jC(e,t,n,r){let o=e?["ng-version","20.0.5"]:w0(t.selectors[0]),i=null,s=null,a=0;if(n)for(let u of n)a+=u[ru].requiredVars,u.create&&(u.targetIdx=0,(i??=[]).push(u)),u.update&&(u.targetIdx=0,(s??=[]).push(u));if(r)for(let u=0;u<r.length;u++){let f=r[u];if(typeof f!="function")for(let p of f.bindings){a+=p[ru].requiredVars;let d=u+1;p.create&&(p.targetIdx=d,(i??=[]).push(p)),p.update&&(p.targetIdx=d,(s??=[]).push(p))}}let c=[t];if(r)for(let u of r){let f=typeof u=="function"?u:u.type,p=vl(f);c.push(p)}return Du(0,null,VC(i,s),1,a,c,null,null,null,[o],null)}function VC(e,t){return!e&&!t?null:n=>{if(n&1&&e)for(let r of e)r.create();if(n&2&&t)for(let r of t)r.update()}}function Xp(e){let t=e[ru].kind;return t==="input"||t==="twoWay"}var Ls=class extends pm{_rootLView;_hasInputBindings;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(t,n,r){super(),this._rootLView=n,this._hasInputBindings=r,this._tNode=ms(n[A],$e),this.location=Sr(this._tNode,n),this.instance=Dt(this._tNode.index,n)[We],this.hostView=this.changeDetectorRef=new fn(n,void 0),this.componentType=t}setInput(t,n){this._hasInputBindings;let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let o=this._rootLView,i=wu(r,o[A],o,t,n);this.previousInputValues.set(t,n);let s=Dt(r.index,o);lm(s,1)}get injector(){return new On(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function BC(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null&&i.length?Array.from(i):null)}}var lt=(()=>{class e{static __NG_ELEMENT_ID__=HC}return e})();function HC(){let e=Je();return Dm(e,se())}var $C=lt,ym=class extends $C{_lContainer;_hostTNode;_hostLView;constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return Sr(this._hostTNode,this._hostLView)}get injector(){return new On(this._hostTNode,this._hostLView)}get parentInjector(){let t=hu(this._hostTNode,this._hostLView);if(gh(t)){let n=Ns(t,this._hostLView),r=Rs(t),o=n[A].data[r+8];return new On(o,n)}else return new On(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=Jp(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-Pe}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=Jl(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,Yl(this._hostTNode,s)),a}createComponent(t,n,r,o,i,s,a){let c=t&&!kD(t),l;if(c)l=n;else{let v=n||{};l=v.index,r=v.injector,o=v.projectableNodes,i=v.environmentInjector||v.ngModuleRef,s=v.directives,a=v.bindings}let u=c?t:new pn(gt(t)),f=r||this.parentInjector;if(!i&&u.ngModule==null){let D=(c?f:this.parentInjector).get(ee,null);D&&(i=D)}let p=gt(u.componentType??{}),d=Jl(this._lContainer,p?.id??null),m=d?.firstChild??null,g=u.create(f,o,m,i,s,a);return this.insertImpl(g.hostView,l,Yl(this._hostTNode,d)),g}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(mp(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let c=o[Ie],l=new ym(c,c[Oe],c[Ie]);l.detach(l.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return dm(s,o,i,r),t.attachToViewContainerRef(),pl(Hl(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=Jp(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=Xl(this._lContainer,n);r&&(go(Hl(this._lContainer),n),Yh(r[A],r))}detach(t){let n=this._adjustIndex(t,-1),r=Xl(this._lContainer,n);return r&&go(Hl(this._lContainer),n)!=null?new fn(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function Jp(e){return e[bo]}function Hl(e){return e[bo]||(e[bo]=[])}function Dm(e,t){let n,r=t[e.index];return st(r)?n=r:(n=um(r,t,null,e),t[e.index]=n,bu(t,n)),zC(n,t,e,r),new ym(n,e,t)}function UC(e,t){let n=e[Re],r=n.createComment(""),o=Bt(t,e),i=n.parentNode(o);return Ps(n,i,r,n.nextSibling(o),!1),r}var zC=qC,GC=()=>!1;function WC(e,t,n){return GC(e,t,n)}function qC(e,t,n,r){if(e[cn])return;let o;n.type&8?o=yt(r):o=UC(t,n),e[cn]=o}var ou=class e{queryList;matches=null;constructor(t){this.queryList=t}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},iu=class e{queries;constructor(t=[]){this.queries=t}createEmbeddedView(t){let n=t.queries;if(n!==null){let r=t.contentQueries!==null?t.contentQueries[0]:n.length,o=[];for(let i=0;i<r;i++){let s=n.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}finishViewCreation(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)Nu(t,n).matches!==null&&this.queries[n].setDirty()}},su=class{flags;read;predicate;constructor(t,n,r=null){this.flags=n,this.read=r,typeof t=="string"?this.predicate=tb(t):this.predicate=t}},au=class e{queries;constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){let o=n!==null?n.length:0,i=this.getByIndex(r).embeddedTView(t,o);i&&(i.indexInDeclarationView=r,n!==null?n.push(i):n=[i])}return n!==null?new e(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}},cu=class e{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(t,n=-1){this.metadata=t,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new e(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,r=t.parent;for(;r!==null&&r.type&8&&r.index!==n;)r=r.parent;return n===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(t,n,ZC(n,i)),this.matchTNodeWithReadOption(t,n,Ms(n,t,i,!1,!1))}else r===Pn?n.type&4&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,Ms(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===we||o===lt||o===Pn&&n.type&4)this.addMatch(n.index,-2);else{let i=Ms(n,t,o,!1,!1);i!==null&&this.addMatch(n.index,i)}else this.addMatch(n.index,r)}}addMatch(t,n){this.matches===null?this.matches=[t,n]:this.matches.push(t,n)}};function ZC(e,t){let n=e.localNames;if(n!==null){for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1]}return null}function YC(e,t){return e.type&11?Sr(e,t):e.type&4?Au(e,t):null}function QC(e,t,n,r){return n===-1?YC(t,e):n===-2?KC(e,t,r):ks(e,e[A],n,t)}function KC(e,t,n){if(n===we)return Sr(t,e);if(n===Pn)return Au(t,e);if(n===lt)return Dm(t,e)}function Cm(e,t,n,r){let o=t[vt].queries[r];if(o.matches===null){let i=e.data,s=n.matches,a=[];for(let c=0;s!==null&&c<s.length;c+=2){let l=s[c];if(l<0)a.push(null);else{let u=i[l];a.push(QC(t,u,s[c+1],n.metadata.read))}}o.matches=a}return o.matches}function lu(e,t,n,r){let o=e.queries.getByIndex(n),i=o.matches;if(i!==null){let s=Cm(e,t,o,n);for(let a=0;a<i.length;a+=2){let c=i[a];if(c>0)r.push(s[a/2]);else{let l=i[a+1],u=t[-c];for(let f=Pe;f<u.length;f++){let p=u[f];p[an]===p[Ie]&&lu(p[A],p,l,r)}if(u[Rn]!==null){let f=u[Rn];for(let p=0;p<f.length;p++){let d=f[p];lu(d[A],d,l,r)}}}}}return r}function XC(e,t){return e[vt].queries[t].queryList}function JC(e,t,n){let r=new Os((n&4)===4);return Dp(e,t,r,r.destroy),(t[vt]??=new iu).queries.push(new ou(r))-1}function eb(e,t,n){let r=at();return r.firstCreatePass&&(nb(r,new su(e,t,n),-1),(t&2)===2&&(r.staticViewQueries=!0)),JC(r,se(),t)}function tb(e){return e.split(",").map(t=>t.trim())}function nb(e,t,n){e.queries===null&&(e.queries=new au),e.queries.track(new cu(t,n))}function Nu(e,t){return e.queries.getByIndex(t)}function rb(e,t){let n=e[A],r=Nu(n,t);return r.crossesNgTemplate?lu(n,e,t,[]):Cm(n,e,r,t)}var eh=new Set;function ku(e){eh.has(e)||(eh.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var wr=class{},Js=class{};var js=class extends wr{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new Fs(this);constructor(t,n,r,o=!0){super(),this.ngModuleType=t,this._parent=n;let i=gl(t);this._bootstrapComponents=Fh(i.bootstrap),this._r3Injector=Ol(t,n,[{provide:wr,useValue:this},{provide:Po,useValue:this.componentFactoryResolver},...r],ze(t),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},Vs=class extends Js{moduleType;constructor(t){super(),this.moduleType=t}create(t){return new js(this.moduleType,t,[])}};var No=class extends wr{injector;componentFactoryResolver=new Fs(this);instance=null;constructor(t){super();let n=new Tn([...t.providers,{provide:wr,useValue:this},{provide:Po,useValue:this.componentFactoryResolver}],t.parent||pr(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function xr(e,t,n=null){return new No({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var ob=(()=>{class e{_injector;cachedInjectors=new Map;constructor(n){this._injector=n}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=yl(!1,n.type),o=r.length>0?xr([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=C({token:e,providedIn:"environment",factory:()=>new e(I(ee))})}return e})();function It(e){return ko(()=>{let t=bm(e),n=U(y({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===mu.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:t.standalone?o=>o.get(ob).getOrCreateStandaloneInjector(n):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||Ht.Emulated,styles:e.styles||Ke,_:null,schemas:e.schemas||null,tView:null,id:""});t.standalone&&ku("NgStandalone"),Im(n);let r=e.dependencies;return n.directiveDefs=th(r,!1),n.pipeDefs=th(r,!0),n.id=lb(n),n})}function ib(e){return gt(e)||vl(e)}function sb(e){return e!==null}function Ut(e){return ko(()=>({type:e.type,bootstrap:e.bootstrap||Ke,declarations:e.declarations||Ke,imports:e.imports||Ke,exports:e.exports||Ke,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function ab(e,t){if(e==null)return on;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=qs.None,c=null),n[i]=[r,a,c],t[i]=s}return n}function cb(e){if(e==null)return on;let t={};for(let n in e)e.hasOwnProperty(n)&&(t[e[n]]=n);return t}function zt(e){return ko(()=>{let t=bm(e);return Im(t),t})}function bm(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputConfig:e.inputs||on,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||Ke,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,resolveHostDirectives:null,hostDirectives:null,inputs:ab(e.inputs,t),outputs:cb(e.outputs),debugInfo:null}}function Im(e){e.features?.forEach(t=>t(e))}function th(e,t){if(!e)return null;let n=t?ip:ib;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(sb)}function lb(e){let t=0,n=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,n,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=2147483648,"c"+t}function ub(e){return Object.getPrototypeOf(e.prototype).constructor}function Fo(e){let t=ub(e.type),n=!0,r=[e];for(;t;){let o;if(Vt(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new E(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);let s=e;s.inputs=$l(e.inputs),s.declaredInputs=$l(e.declaredInputs),s.outputs=$l(e.outputs);let a=o.hostBindings;a&&mb(e,a);let c=o.viewQuery,l=o.contentQueries;if(c&&pb(e,c),l&&hb(e,l),db(e,o),Kf(e.outputs,o.outputs),Vt(o)&&o.data.animation){let u=e.data;u.animation=(u.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===Fo&&(n=!1)}}t=Object.getPrototypeOf(t)}fb(r)}function db(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n])}}function fb(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=Er(o.hostAttrs,n=Er(n,o.hostAttrs))}}function $l(e){return e===on?{}:e===Ke?[]:e}function pb(e,t){let n=e.viewQuery;n?e.viewQuery=(r,o)=>{t(r,o),n(r,o)}:e.viewQuery=t}function hb(e,t){let n=e.contentQueries;n?e.contentQueries=(r,o,i)=>{t(r,o,i),n(r,o,i)}:e.contentQueries=t}function mb(e,t){let n=e.hostBindings;n?e.hostBindings=(r,o)=>{t(r,o),n(r,o)}:e.hostBindings=t}function gb(e,t,n,r,o,i,s,a,c){let l=t.consts,u=Oo(t,e,4,s||null,a||null);ys()&&Ru(t,n,u,Dr(l,c),Eu),u.mergedAttrs=Er(u.mergedAttrs,u.attrs),pu(t,u);let f=u.tView=Du(2,u,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,l,null);return t.queries!==null&&(t.queries.template(t,u),f.queries=t.queries.embeddedTView(u)),u}function vb(e,t,n,r,o,i,s,a,c,l,u){let f=n+$e,p=t.firstCreatePass?gb(f,t,e,r,o,i,s,a,l):t.data[f];c&&(p.flags|=c),ln(p,!1);let d=yb(t,e,p,n);_o()&&Qs(t,e,d,p),Tr(d,e);let m=um(d,e,d,p);return e[f]=m,bu(e,m),WC(m,p,e),hs(p)&&Zs(t,e,p),l!=null&&Iu(e,p,u),p}var yb=Db;function Db(e,t,n,r){return So(!0),t[Re].createComment("")}var Ou=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(Ou||{}),Lo=new w(""),Em=!1,uu=class extends te{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(t=!1){super(),this.__isAsync=t,up()&&(this.destroyRef=h(Cr,{optional:!0})??void 0,this.pendingTasks=h(dn,{optional:!0})??void 0)}emit(t){let n=$(null);try{super.next(t)}finally{$(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let c=t;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof fe&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{try{t(n)}finally{r!==void 0&&this.pendingTasks?.remove(r)}})}}},ce=uu;function wm(e){let t,n;function r(){e=To;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function nh(e){return queueMicrotask(()=>e()),()=>{e=To}}var Pu="isAngularZone",Bs=Pu+"_ID",Cb=0,q=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new ce(!1);onMicrotaskEmpty=new ce(!1);onStable=new ce(!1);onError=new ce(!1);constructor(t){let{enableLongStackTrace:n=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=Em}=t;if(typeof Zone>"u")throw new E(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),n&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,Eb(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(Pu)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new E(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new E(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,bb,To,To);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},bb={};function Fu(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function Ib(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function t(){wm(()=>{e.callbackScheduled=!1,du(e),e.isCheckStableRunning=!0,Fu(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{t()}):e._outer.run(()=>{t()}),du(e)}function Eb(e){let t=()=>{Ib(e)},n=Cb++;e._inner=e._inner.fork({name:"angular",properties:{[Pu]:!0,[Bs]:n,[Bs+n]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if(wb(c))return r.invokeTask(i,s,a,c);try{return rh(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),oh(e)}},onInvoke:(r,o,i,s,a,c,l)=>{try{return rh(e),r.invoke(i,s,a,c,l)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!_b(c)&&t(),oh(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,du(e),Fu(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function du(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function rh(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function oh(e){e._nesting--,Fu(e)}var Hs=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new ce;onMicrotaskEmpty=new ce;onStable=new ce;onError=new ce;run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function wb(e){return _m(e,"__ignore_ng_zone__")}function _b(e){return _m(e,"__scheduler_tick__")}function _m(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}var Sm=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=C({token:e,providedIn:"root",factory:()=>new e})}return e})();var Lu=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var ju=new w("");function Ar(e){return!!e&&typeof e.then=="function"}function Tm(e){return!!e&&typeof e.subscribe=="function"}var ea=new w("");var Vu=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r});appInits=h(ea,{optional:!0})??[];injector=h(Ee);constructor(){}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=Ge(this.injector,o);if(Ar(i))n.push(i);else if(Tm(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),ta=new w("");function Mm(){Mc(()=>{let e="";throw new E(600,e)})}function xm(e){return e.isBoundToModule}var Sb=10;var Gt=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=h(Ct);afterRenderManager=h(Sm);zonelessEnabled=h(_s);rootEffectScheduler=h(Ll);dirtyFlags=0;tracingSnapshot=null;allTestViews=new Set;autoDetectTestViews=new Set;includeAllTestViews=!1;afterTick=new te;get allViews(){return[...(this.includeAllTestViews?this.allTestViews:this.autoDetectTestViews).keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];internalPendingTask=h(dn);get isStable(){return this.internalPendingTask.hasPendingTasksObservable.pipe(L(n=>!n))}constructor(){h(Lo,{optional:!0})}whenStable(){let n;return new Promise(r=>{n=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{n.unsubscribe()})}_injector=h(ee);_rendererFactory=null;get injector(){return this._injector}bootstrap(n,r){return this.bootstrapImpl(n,r)}bootstrapImpl(n,r,o=Ee.NULL){return this._injector.get(q).run(()=>{ne(10);let s=n instanceof Xs;if(!this._injector.get(Vu).done){let m="";throw new E(405,m)}let c;s?c=n:c=this._injector.get(Po).resolveComponentFactory(n),this.componentTypes.push(c.componentType);let l=xm(c)?void 0:this._injector.get(wr),u=r||c.selector,f=c.create(o,[],u,l),p=f.location.nativeElement,d=f.injector.get(ju,null);return d?.registerApplication(p),f.onDestroy(()=>{this.detachView(f.hostView),xo(this.components,f),d?.unregisterApplication(p)}),this._loadComponent(f),ne(11,f),f})}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){ne(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(Ou.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new E(101,!1);let n=$(null);try{this._runningTick=!0,this.synchronize()}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,$(n),this.afterTick.next(),ne(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(Fn,null,{optional:!0}));let n=0;for(;this.dirtyFlags!==0&&n++<Sb;)ne(14),this.synchronizeOnce(),ne(15)}synchronizeOnce(){this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush());let n=!1;if(this.dirtyFlags&7){let r=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:o}of this.allViews){if(!r&&!Io(o))continue;let i=r&&!this.zonelessEnabled?0:1;xu(o,i),n=!0}if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}n||(this._rendererFactory?.begin?.(),this._rendererFactory?.end?.()),this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:n})=>Io(n))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;xo(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView);try{this.tick()}catch(o){this.internalErrorHandler(o)}this.components.push(n),this._injector.get(ta,[]).forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>xo(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new E(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function xo(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function Bu(e,t,n){let r=se(),o=Tp();if(NC(r,o,t)){let i=at(),s=Op();L0(s,r,e,t,r[Re],n)}return Bu}function ih(e,t,n,r,o){wu(t,e,n,o?"class":"style",r)}function na(e,t,n,r){let o=se(),i=at(),s=$e+e,a=o[Re],c=i.firstCreatePass?gm(s,i,o,t,Eu,ys(),n,r):i.data[s],l=Tb(i,o,c,a,t,e);o[s]=l;let u=hs(c);return ln(c,!0),Hh(a,l,c),!Ys(c)&&_o()&&Qs(i,o,l,c),(Cp()===0||u)&&Tr(l,o),bp(),u&&(Zs(i,o,c),vu(i,c,o)),r!==null&&Iu(o,c),na}function ra(){let e=Je();Ds()?Cs():(e=e.parent,ln(e,!1));let t=e;Ep(t)&&wp(),Ip();let n=at();return n.firstCreatePass&&vm(n,t),t.classesWithoutHost!=null&&VD(t)&&ih(n,t,se(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&BD(t)&&ih(n,t,se(),t.stylesWithoutHost,!1),ra}function oa(e,t,n,r){return na(e,t,n,r),ra(),oa}var Tb=(e,t,n,r,o,i)=>(So(!0),Vh(r,o,Pp()));function Mb(e,t,n,r,o){let i=t.consts,s=Dr(i,r),a=Oo(t,e,8,"ng-container",s);s!==null&&nu(a,s,!0);let c=Dr(i,o);return ys()&&Ru(t,n,a,c,Eu),a.mergedAttrs=Er(a.mergedAttrs,a.attrs),t.queries!==null&&t.queries.elementStart(t,a),a}function ia(e,t,n){let r=se(),o=at(),i=e+$e,s=o.firstCreatePass?Mb(i,o,r,t,n):o.data[i];ln(s,!0);let a=xb(o,r,s,e);return r[i]=a,_o()&&Qs(o,r,a,s),Tr(a,r),hs(s)&&(Zs(o,r,s),vu(o,s,r)),n!=null&&Iu(r,s),ia}function sa(){let e=Je(),t=at();return Ds()?Cs():(e=e.parent,ln(e,!1)),t.firstCreatePass&&(pu(t,e),ps(e)&&t.queries.elementEnd(e)),sa}var xb=(e,t,n,r)=>(So(!0),S0(t[Re],""));var jo="en-US";var Ab=jo;function Am(e){typeof e=="string"&&(Ab=e.toLowerCase().replace(/_/g,"-"))}function Rb(e,t){let n=null,r=y0(e);for(let o=0;o<t.length;o++){let i=t[o];if(i==="*"){n=o;continue}if(r===null?jh(e,i,!0):b0(r,i))return o}return n}function hn(e){let t=se()[He][Oe];if(!t.projection){let n=e?e.length:1,r=t.projection=op(n,null),o=r.slice(),i=t.child;for(;i!==null;){if(i.type!==128){let s=e?Rb(i,e):0;s!==null&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i)}i=i.next}}}function mn(e,t=0,n,r,o,i){let s=se(),a=at(),c=r?e+1:null;c!==null&&vb(s,a,c,r,o,i,null,n);let l=Oo(a,$e+e,16,null,n||null);l.projection===null&&(l.projection=t),Cs();let f=!s[hr]||_l();s[He][Oe].projection[l.projection]===null&&c!==null?Nb(s,a,c):f&&!Ys(l)&&rC(a,s,l)}function Nb(e,t,n){let r=$e+n,o=t.data[r],i=e[r],s=Jl(i,o.tView.ssrId),a=qh(e,o,void 0,{dehydratedView:s});dm(i,a,0,Yl(o,s))}function aa(e,t,n){eb(e,t,n)}function Vo(e){let t=se(),n=at(),r=Al();Is(r+1);let o=Nu(n,r);if(e.dirty&&hp(t)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=rb(t,r);e.reset(i,e0),e.notifyOnChanges()}return!0}return!1}function Bo(){return XC(se(),Al())}function kb(e,t=""){let n=se(),r=at(),o=e+$e,i=r.firstCreatePass?Oo(r,o,1,t,null):r.data[o],s=Ob(r,n,i,t,e);n[o]=s,_o()&&Qs(r,n,s,i),ln(i,!1)}var Ob=(e,t,n,r,o)=>(So(!0),_0(t[Re],r));var $s=class{ngModuleFactory;componentFactories;constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},Hu=(()=>{class e{compileModuleSync(n){return new Vs(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=gl(n),i=Fh(o.declarations).reduce((s,a)=>{let c=gt(a);return c&&s.push(new pn(c)),s},[]);return new $s(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Pb=(()=>{class e{zone=h(q);changeDetectionScheduler=h(Mn);applicationRef=h(Gt);applicationErrorHandler=h(Ct);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{try{this.applicationRef.dirtyFlags|=1,this.applicationRef._tick()}catch(n){this.applicationErrorHandler(n)}})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Rm({ngZoneFactory:e,ignoreChangesOutsideZone:t,scheduleInRootZone:n}){return e??=()=>new q(U(y({},Nm()),{scheduleInRootZone:n})),[{provide:q,useFactory:e},{provide:sn,multi:!0,useFactory:()=>{let r=h(Pb,{optional:!0});return()=>r.initialize()}},{provide:sn,multi:!0,useFactory:()=>{let r=h(Fb);return()=>{r.initialize()}}},t===!0?{provide:Pl,useValue:!0}:[],{provide:Fl,useValue:n??Em},{provide:Ct,useFactory:()=>{let r=h(q),o=h(ee),i;return s=>{r.runOutsideAngular(()=>{o.destroyed&&!i?setTimeout(()=>{throw s}):(i??=o.get(ht),i.handleError(s))})}}}]}function Nm(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var Fb=(()=>{class e{subscription=new fe;initialized=!1;zone=h(q);pendingTasks=h(dn);initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{q.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{q.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var km=(()=>{class e{applicationErrorHandler=h(Ct);appRef=h(Gt);taskService=h(dn);ngZone=h(q);zonelessEnabled=h(_s);tracing=h(Lo,{optional:!0});disableScheduling=h(Pl,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new fe;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(Bs):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(h(Fl,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof Hs||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;let r=!1;switch(n){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?nh:wm;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(n){return!(this.disableScheduling&&!n||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(Bs+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let n=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){this.taskService.remove(n),this.applicationErrorHandler(r)}finally{this.cleanup()}this.useMicrotaskScheduler=!0,nh(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(n)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Lb(){return typeof $localize<"u"&&$localize.locale||jo}var $u=new w("",{providedIn:"root",factory:()=>h($u,{optional:!0,skipSelf:!0})||Lb()});var Lm=Symbol("InputSignalNode#UNSET"),eI=U(y({},Ac),{transformFn:void 0,applyValueToInputSignal(e,t){xc(e,t)}});function jm(e,t){let n=Object.create(eI);n.value=e,n.transformFn=t?.transform;function r(){if(wc(n),n.value===Lm){let o=null;throw new E(-950,o)}return n.value}return r[Ni]=n,r}var tI=new w("");tI.__NG_ELEMENT_ID__=e=>{let t=Je();if(t===null)throw new E(204,!1);if(t.type&2)return t.value;if(e&8)return null;throw new E(204,!1)};function Om(e,t){return jm(e,t)}function nI(e){return jm(Lm,e)}var Vm=(Om.required=nI,Om);var Uu=new w(""),rI=new w("");function Ho(e){return!e.moduleRef}function oI(e){let t=Ho(e)?e.r3Injector:e.moduleRef.injector,n=t.get(q);return n.run(()=>{Ho(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=t.get(Ct),o;if(n.runOutsideAngular(()=>{o=n.onError.subscribe({next:r})}),Ho(e)){let i=()=>t.destroy(),s=e.platformInjector.get(Uu);s.add(i),t.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(Uu);s.add(i),e.moduleRef.onDestroy(()=>{xo(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return sI(r,n,()=>{let i=t.get(Vu);return i.runInitializers(),i.donePromise.then(()=>{let s=t.get($u,jo);if(Am(s||jo),!t.get(rI,!0))return Ho(e)?t.get(Gt):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(Ho(e)){let c=t.get(Gt);return e.rootComponent!==void 0&&c.bootstrap(e.rootComponent),c}else return iI?.(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}var iI;function sI(e,t,n){try{let r=n();return Ar(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e(r)),r}}var ca=null;function aI(e=[],t){return Ee.create({name:t,providers:[{provide:vo,useValue:"platform"},{provide:Uu,useValue:new Set([()=>ca=null])},...e]})}function cI(e=[]){if(ca)return ca;let t=aI(e);return ca=t,Mm(),lI(t),t}function lI(e){let t=e.get(zs,null);Ge(e,()=>{t?.forEach(n=>n())})}var et=(()=>{class e{static __NG_ELEMENT_ID__=uI}return e})();function uI(e){return dI(Je(),se(),(e&16)===16)}function dI(e,t,n){if(Nn(e)&&!n){let r=Dt(e.index,t);return new fn(r,r)}else if(e.type&175){let r=t[He];return new fn(r,t)}return null}function Bm(e){ne(8);try{let{rootComponent:t,appProviders:n,platformProviders:r}=e,o=cI(r),i=[Rm({}),{provide:Mn,useExisting:km},Lp,...n||[]],s=new No({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return oI({r3Injector:s.injector,platformInjector:o,rootComponent:t})}catch(t){return Promise.reject(t)}finally{ne(9)}}function Hm(e,t){let n=gt(e),r=t.elementInjector||pr();return new pn(n).create(r,t.projectableNodes,t.hostElement,t.environmentInjector,t.directives,t.bindings)}function Wu(e){let t=gt(e);if(!t)return null;let n=new pn(t);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return t.standalone},get isSignal(){return t.signals}}}var zm=null;function Wt(){return zm}function qu(e){zm??=e}var $o=class{},Zu=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:()=>h(Gm),providedIn:"platform"})}return e})();var Gm=(()=>{class e extends Zu{_location;_history;_doc=h(ae);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return Wt().getBaseHref(this._doc)}onPopState(n){let r=Wt().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){let r=Wt().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,o){this._history.pushState(n,r,o)}replaceState(n,r,o){this._history.replaceState(n,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:()=>new e,providedIn:"platform"})}return e})();function Wm(e,t){return e?t?e.endsWith("/")?t.startsWith("/")?e+t.slice(1):e+t:t.startsWith("/")?e+t:`${e}/${t}`:e:t}function $m(e){let t=e.search(/#|\?|$/);return e[t-1]==="/"?e.slice(0,t-1)+e.slice(t):e}function gn(e){return e&&e[0]!=="?"?`?${e}`:e}var Uo=(()=>{class e{historyGo(n){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:()=>h(Zm),providedIn:"root"})}return e})(),qm=new w(""),Zm=(()=>{class e extends Uo{_platformLocation;_baseHref;_removeListenerFns=[];constructor(n,r){super(),this._platformLocation=n,this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??h(ae).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return Wm(this._baseHref,n)}path(n=!1){let r=this._platformLocation.pathname+gn(this._platformLocation.search),o=this._platformLocation.hash;return o&&n?`${r}${o}`:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+gn(i));this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+gn(i));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static \u0275fac=function(r){return new(r||e)(I(Zu),I(qm,8))};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),ut=(()=>{class e{_subject=new te;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(n){this._locationStrategy=n;let r=this._locationStrategy.getBaseHref();this._basePath=hI($m(Um(r))),this._locationStrategy.onPopState(o=>{this._subject.next({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+gn(r))}normalize(n){return e.stripTrailingSlash(pI(this._basePath,Um(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",o=null){this._locationStrategy.pushState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+gn(r)),o)}replaceState(n,r="",o=null){this._locationStrategy.replaceState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+gn(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(o=>o(n,r))}subscribe(n,r,o){return this._subject.subscribe({next:n,error:r??void 0,complete:o??void 0})}static normalizeQueryParams=gn;static joinWithSlash=Wm;static stripTrailingSlash=$m;static \u0275fac=function(r){return new(r||e)(I(Uo))};static \u0275prov=C({token:e,factory:()=>fI(),providedIn:"root"})}return e})();function fI(){return new ut(I(Uo))}function pI(e,t){if(!e||!t.startsWith(e))return t;let n=t.substring(e.length);return n===""||["/",";","?","#"].includes(n[0])?n:t}function Um(e){return e.replace(/\/index.html$/,"")}function hI(e){if(new RegExp("^(https?:)?//").test(e)){let[,n]=e.split(/\/\/[^\/]+/);return n}return e}var Yu=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=Ut({type:e});static \u0275inj=mt({})}return e})();function Qu(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[o,i]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}var zo=class{};var Qm="browser",vI="server";function Km(e){return e===vI}var fa=new w(""),ed=(()=>{class e{_zone;_plugins;_eventNameToPlugin=new Map;constructor(n,r){this._zone=r,n.forEach(o=>{o.manager=this}),this._plugins=n.slice().reverse()}addEventListener(n,r,o,i){return this._findPluginFor(r).addEventListener(n,r,o,i)}getZone(){return this._zone}_findPluginFor(n){let r=this._eventNameToPlugin.get(n);if(r)return r;if(r=this._plugins.find(i=>i.supports(n)),!r)throw new E(5101,!1);return this._eventNameToPlugin.set(n,r),r}static \u0275fac=function(r){return new(r||e)(I(fa),I(q))};static \u0275prov=C({token:e,factory:e.\u0275fac})}return e})(),Go=class{_doc;constructor(t){this._doc=t}manager},ua="ng-app-id";function Jm(e){for(let t of e)t.remove()}function eg(e,t){let n=t.createElement("style");return n.textContent=e,n}function yI(e,t,n,r){let o=e.head?.querySelectorAll(`style[${ua}="${t}"],link[${ua}="${t}"]`);if(o)for(let i of o)i.removeAttribute(ua),i instanceof HTMLLinkElement?r.set(i.href.slice(i.href.lastIndexOf("/")+1),{usage:0,elements:[i]}):i.textContent&&n.set(i.textContent,{usage:0,elements:[i]})}function Xu(e,t){let n=t.createElement("link");return n.setAttribute("rel","stylesheet"),n.setAttribute("href",e),n}var td=(()=>{class e{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;isServer;constructor(n,r,o,i={}){this.doc=n,this.appId=r,this.nonce=o,this.isServer=Km(i),yI(n,r,this.inline,this.external),this.hosts.add(n.head)}addStyles(n,r){for(let o of n)this.addUsage(o,this.inline,eg);r?.forEach(o=>this.addUsage(o,this.external,Xu))}removeStyles(n,r){for(let o of n)this.removeUsage(o,this.inline);r?.forEach(o=>this.removeUsage(o,this.external))}addUsage(n,r,o){let i=r.get(n);i?i.usage++:r.set(n,{usage:1,elements:[...this.hosts].map(s=>this.addElement(s,o(n,this.doc)))})}removeUsage(n,r){let o=r.get(n);o&&(o.usage--,o.usage<=0&&(Jm(o.elements),r.delete(n)))}ngOnDestroy(){for(let[,{elements:n}]of[...this.inline,...this.external])Jm(n);this.hosts.clear()}addHost(n){this.hosts.add(n);for(let[r,{elements:o}]of this.inline)o.push(this.addElement(n,eg(r,this.doc)));for(let[r,{elements:o}]of this.external)o.push(this.addElement(n,Xu(r,this.doc)))}removeHost(n){this.hosts.delete(n)}addElement(n,r){return this.nonce&&r.setAttribute("nonce",this.nonce),this.isServer&&r.setAttribute(ua,this.appId),n.appendChild(r)}static \u0275fac=function(r){return new(r||e)(I(ae),I(Us),I(Gs,8),I(Mr))};static \u0275prov=C({token:e,factory:e.\u0275fac})}return e})(),Ku={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},nd=/%COMP%/g;var ng="%COMP%",DI=`_nghost-${ng}`,CI=`_ngcontent-${ng}`,bI=!0,II=new w("",{providedIn:"root",factory:()=>bI});function EI(e){return CI.replace(nd,e)}function wI(e){return DI.replace(nd,e)}function rg(e,t){return t.map(n=>n.replace(nd,e))}var rd=(()=>{class e{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(n,r,o,i,s,a,c,l=null,u=null){this.eventManager=n,this.sharedStylesHost=r,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=s,this.platformId=a,this.ngZone=c,this.nonce=l,this.tracingService=u,this.platformIsServer=!1,this.defaultRenderer=new Wo(n,s,c,this.platformIsServer,this.tracingService)}createRenderer(n,r){if(!n||!r)return this.defaultRenderer;let o=this.getOrCreateRenderer(n,r);return o instanceof da?o.applyToHost(n):o instanceof qo&&o.applyStyles(),o}getOrCreateRenderer(n,r){let o=this.rendererByCompId,i=o.get(r.id);if(!i){let s=this.doc,a=this.ngZone,c=this.eventManager,l=this.sharedStylesHost,u=this.removeStylesOnCompDestroy,f=this.platformIsServer,p=this.tracingService;switch(r.encapsulation){case Ht.Emulated:i=new da(c,l,r,this.appId,u,s,a,f,p);break;case Ht.ShadowDom:return new Ju(c,l,n,r,s,a,this.nonce,f,p);default:i=new qo(c,l,r,u,s,a,f,p);break}o.set(r.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(n){this.rendererByCompId.delete(n)}static \u0275fac=function(r){return new(r||e)(I(ed),I(td),I(Us),I(II),I(ae),I(Mr),I(q),I(Gs),I(Lo,8))};static \u0275prov=C({token:e,factory:e.\u0275fac})}return e})(),Wo=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(t,n,r,o,i){this.eventManager=t,this.doc=n,this.ngZone=r,this.platformIsServer=o,this.tracingService=i}destroy(){}destroyNode=null;createElement(t,n){return n?this.doc.createElementNS(Ku[n]||n,t):this.doc.createElement(t)}createComment(t){return this.doc.createComment(t)}createText(t){return this.doc.createTextNode(t)}appendChild(t,n){(tg(t)?t.content:t).appendChild(n)}insertBefore(t,n,r){t&&(tg(t)?t.content:t).insertBefore(n,r)}removeChild(t,n){n.remove()}selectRootElement(t,n){let r=typeof t=="string"?this.doc.querySelector(t):t;if(!r)throw new E(-5104,!1);return n||(r.textContent=""),r}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,n,r,o){if(o){n=o+":"+n;let i=Ku[o];i?t.setAttributeNS(i,n,r):t.setAttribute(n,r)}else t.setAttribute(n,r)}removeAttribute(t,n,r){if(r){let o=Ku[r];o?t.removeAttributeNS(o,n):t.removeAttribute(`${r}:${n}`)}else t.removeAttribute(n)}addClass(t,n){t.classList.add(n)}removeClass(t,n){t.classList.remove(n)}setStyle(t,n,r,o){o&(jn.DashCase|jn.Important)?t.style.setProperty(n,r,o&jn.Important?"important":""):t.style[n]=r}removeStyle(t,n,r){r&jn.DashCase?t.style.removeProperty(n):t.style[n]=""}setProperty(t,n,r){t!=null&&(t[n]=r)}setValue(t,n){t.nodeValue=n}listen(t,n,r,o){if(typeof t=="string"&&(t=Wt().getGlobalEventTarget(this.doc,t),!t))throw new E(5102,!1);let i=this.decoratePreventDefault(r);return this.tracingService?.wrapEventListener&&(i=this.tracingService.wrapEventListener(t,n,i)),this.eventManager.addEventListener(t,n,i,o)}decoratePreventDefault(t){return n=>{if(n==="__ngUnwrap__")return t;t(n)===!1&&n.preventDefault()}}};function tg(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var Ju=class extends Wo{sharedStylesHost;hostEl;shadowRoot;constructor(t,n,r,o,i,s,a,c,l){super(t,i,s,c,l),this.sharedStylesHost=n,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let u=o.styles;u=rg(o.id,u);for(let p of u){let d=document.createElement("style");a&&d.setAttribute("nonce",a),d.textContent=p,this.shadowRoot.appendChild(d)}let f=o.getExternalStyles?.();if(f)for(let p of f){let d=Xu(p,i);a&&d.setAttribute("nonce",a),this.shadowRoot.appendChild(d)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}appendChild(t,n){return super.appendChild(this.nodeOrShadowRoot(t),n)}insertBefore(t,n,r){return super.insertBefore(this.nodeOrShadowRoot(t),n,r)}removeChild(t,n){return super.removeChild(null,n)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},qo=class extends Wo{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(t,n,r,o,i,s,a,c,l){super(t,i,s,a,c),this.sharedStylesHost=n,this.removeStylesOnCompDestroy=o;let u=r.styles;this.styles=l?rg(l,u):u,this.styleUrls=r.getExternalStyles?.(l)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},da=class extends qo{contentAttr;hostAttr;constructor(t,n,r,o,i,s,a,c,l){let u=o+"-"+r.id;super(t,n,r,i,s,a,c,l,u),this.contentAttr=EI(u),this.hostAttr=wI(u)}applyToHost(t){this.applyStyles(),this.setAttribute(t,this.hostAttr,"")}createElement(t,n){let r=super.createElement(t,n);return super.setAttribute(r,this.contentAttr,""),r}};var pa=class e extends $o{supportsDOMEvents=!0;static makeCurrent(){qu(new e)}onAndCancel(t,n,r,o){return t.addEventListener(n,r,o),()=>{t.removeEventListener(n,r,o)}}dispatchEvent(t,n){t.dispatchEvent(n)}remove(t){t.remove()}createElement(t,n){return n=n||this.getDefaultDocument(),n.createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,n){return n==="window"?window:n==="document"?t:n==="body"?t.body:null}getBaseHref(t){let n=_I();return n==null?null:SI(n)}resetBaseElement(){Zo=null}getUserAgent(){return window.navigator.userAgent}getCookie(t){return Qu(document.cookie,t)}},Zo=null;function _I(){return Zo=Zo||document.head.querySelector("base"),Zo?Zo.getAttribute("href"):null}function SI(e){return new URL(e,document.baseURI).pathname}var TI=(()=>{class e{build(){return new XMLHttpRequest}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:e.\u0275fac})}return e})(),ig=(()=>{class e extends Go{constructor(n){super(n)}supports(n){return!0}addEventListener(n,r,o,i){return n.addEventListener(r,o,i),()=>this.removeEventListener(n,r,o,i)}removeEventListener(n,r,o,i){return n.removeEventListener(r,o,i)}static \u0275fac=function(r){return new(r||e)(I(ae))};static \u0275prov=C({token:e,factory:e.\u0275fac})}return e})(),og=["alt","control","meta","shift"],MI={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},xI={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},sg=(()=>{class e extends Go{constructor(n){super(n)}supports(n){return e.parseEventName(n)!=null}addEventListener(n,r,o,i){let s=e.parseEventName(r),a=e.eventCallback(s.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>Wt().onAndCancel(n,s.domEventName,a,i))}static parseEventName(n){let r=n.toLowerCase().split("."),o=r.shift();if(r.length===0||!(o==="keydown"||o==="keyup"))return null;let i=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),og.forEach(l=>{let u=r.indexOf(l);u>-1&&(r.splice(u,1),s+=l+".")}),s+=i,r.length!=0||i.length===0)return null;let c={};return c.domEventName=o,c.fullKey=s,c}static matchEventFullKeyCode(n,r){let o=MI[n.key]||n.key,i="";return r.indexOf("code.")>-1&&(o=n.code,i="code."),o==null||!o?!1:(o=o.toLowerCase(),o===" "?o="space":o==="."&&(o="dot"),og.forEach(s=>{if(s!==o){let a=xI[s];a(n)&&(i+=s+".")}}),i+=o,i===r)}static eventCallback(n,r,o){return i=>{e.matchEventFullKeyCode(i,n)&&o.runGuarded(()=>r(i))}}static _normalizeKey(n){return n==="esc"?"escape":n}static \u0275fac=function(r){return new(r||e)(I(ae))};static \u0275prov=C({token:e,factory:e.\u0275fac})}return e})();function AI(e,t){return Bm(y({rootComponent:e},RI(t)))}function RI(e){return{appProviders:[...FI,...e?.providers??[]],platformProviders:PI}}function NI(){pa.makeCurrent()}function kI(){return new ht}function OI(){return gu(document),document}var PI=[{provide:Mr,useValue:Qm},{provide:zs,useValue:NI,multi:!0},{provide:ae,useFactory:OI}];var FI=[{provide:vo,useValue:"root"},{provide:ht,useFactory:kI},{provide:fa,useClass:ig,multi:!0,deps:[ae]},{provide:fa,useClass:sg,multi:!0,deps:[ae]},rd,td,ed,{provide:Fn,useExisting:rd},{provide:zo,useClass:TI},[]];var ag=(()=>{class e{_doc;constructor(n){this._doc=n}getTitle(){return this._doc.title}setTitle(n){this._doc.title=n||""}static \u0275fac=function(r){return new(r||e)(I(ae))};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var M="primary",li=Symbol("RouteTitle"),cd=class{params;constructor(t){this.params=t||{}}has(t){return Object.prototype.hasOwnProperty.call(this.params,t)}get(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n[0]:n}return null}getAll(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n:[n]}return[]}get keys(){return Object.keys(this.params)}};function Hn(e){return new cd(e)}function mg(e,t,n){let r=n.path.split("/");if(r.length>e.length||n.pathMatch==="full"&&(t.hasChildren()||r.length<e.length))return null;let o={};for(let i=0;i<r.length;i++){let s=r[i],a=e[i];if(s[0]===":")o[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:o}}function jI(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;++n)if(!wt(e[n],t[n]))return!1;return!0}function wt(e,t){let n=e?ld(e):void 0,r=t?ld(t):void 0;if(!n||!r||n.length!=r.length)return!1;let o;for(let i=0;i<n.length;i++)if(o=n[i],!gg(e[o],t[o]))return!1;return!0}function ld(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function gg(e,t){if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;let n=[...e].sort(),r=[...t].sort();return n.every((o,i)=>r[i]===o)}else return e===t}function vg(e){return e.length>0?e[e.length-1]:null}function Yt(e){return Bc(e)?e:Ar(e)?ie(Promise.resolve(e)):S(e)}var VI={exact:Dg,subset:Cg},yg={exact:BI,subset:HI,ignored:()=>!0};function cg(e,t,n){return VI[n.paths](e.root,t.root,n.matrixParams)&&yg[n.queryParams](e.queryParams,t.queryParams)&&!(n.fragment==="exact"&&e.fragment!==t.fragment)}function BI(e,t){return wt(e,t)}function Dg(e,t,n){if(!Vn(e.segments,t.segments)||!ga(e.segments,t.segments,n)||e.numberOfChildren!==t.numberOfChildren)return!1;for(let r in t.children)if(!e.children[r]||!Dg(e.children[r],t.children[r],n))return!1;return!0}function HI(e,t){return Object.keys(t).length<=Object.keys(e).length&&Object.keys(t).every(n=>gg(e[n],t[n]))}function Cg(e,t,n){return bg(e,t,t.segments,n)}function bg(e,t,n,r){if(e.segments.length>n.length){let o=e.segments.slice(0,n.length);return!(!Vn(o,n)||t.hasChildren()||!ga(o,n,r))}else if(e.segments.length===n.length){if(!Vn(e.segments,n)||!ga(e.segments,n,r))return!1;for(let o in t.children)if(!e.children[o]||!Cg(e.children[o],t.children[o],r))return!1;return!0}else{let o=n.slice(0,e.segments.length),i=n.slice(e.segments.length);return!Vn(e.segments,o)||!ga(e.segments,o,r)||!e.children[M]?!1:bg(e.children[M],t,i,r)}}function ga(e,t,n){return t.every((r,o)=>yg[n](e[o].parameters,r.parameters))}var St=class{root;queryParams;fragment;_queryParamMap;constructor(t=new G([],{}),n={},r=null){this.root=t,this.queryParams=n,this.fragment=r}get queryParamMap(){return this._queryParamMap??=Hn(this.queryParams),this._queryParamMap}toString(){return zI.serialize(this)}},G=class{segments;children;parent=null;constructor(t,n){this.segments=t,this.children=n,Object.values(n).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return va(this)}},vn=class{path;parameters;_parameterMap;constructor(t,n){this.path=t,this.parameters=n}get parameterMap(){return this._parameterMap??=Hn(this.parameters),this._parameterMap}toString(){return Eg(this)}};function $I(e,t){return Vn(e,t)&&e.every((n,r)=>wt(n.parameters,t[r].parameters))}function Vn(e,t){return e.length!==t.length?!1:e.every((n,r)=>n.path===t[r].path)}function UI(e,t){let n=[];return Object.entries(e.children).forEach(([r,o])=>{r===M&&(n=n.concat(t(o,r)))}),Object.entries(e.children).forEach(([r,o])=>{r!==M&&(n=n.concat(t(o,r)))}),n}var Un=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:()=>new $n,providedIn:"root"})}return e})(),$n=class{parse(t){let n=new dd(t);return new St(n.parseRootSegment(),n.parseQueryParams(),n.parseFragment())}serialize(t){let n=`/${Yo(t.root,!0)}`,r=qI(t.queryParams),o=typeof t.fragment=="string"?`#${GI(t.fragment)}`:"";return`${n}${r}${o}`}},zI=new $n;function va(e){return e.segments.map(t=>Eg(t)).join("/")}function Yo(e,t){if(!e.hasChildren())return va(e);if(t){let n=e.children[M]?Yo(e.children[M],!1):"",r=[];return Object.entries(e.children).forEach(([o,i])=>{o!==M&&r.push(`${o}:${Yo(i,!1)}`)}),r.length>0?`${n}(${r.join("//")})`:n}else{let n=UI(e,(r,o)=>o===M?[Yo(e.children[M],!1)]:[`${o}:${Yo(r,!1)}`]);return Object.keys(e.children).length===1&&e.children[M]!=null?`${va(e)}/${n[0]}`:`${va(e)}/(${n.join("//")})`}}function Ig(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function ha(e){return Ig(e).replace(/%3B/gi,";")}function GI(e){return encodeURI(e)}function ud(e){return Ig(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function ya(e){return decodeURIComponent(e)}function lg(e){return ya(e.replace(/\+/g,"%20"))}function Eg(e){return`${ud(e.path)}${WI(e.parameters)}`}function WI(e){return Object.entries(e).map(([t,n])=>`;${ud(t)}=${ud(n)}`).join("")}function qI(e){let t=Object.entries(e).map(([n,r])=>Array.isArray(r)?r.map(o=>`${ha(n)}=${ha(o)}`).join("&"):`${ha(n)}=${ha(r)}`).filter(n=>n);return t.length?`?${t.join("&")}`:""}var ZI=/^[^\/()?;#]+/;function od(e){let t=e.match(ZI);return t?t[0]:""}var YI=/^[^\/()?;=#]+/;function QI(e){let t=e.match(YI);return t?t[0]:""}var KI=/^[^=?&#]+/;function XI(e){let t=e.match(KI);return t?t[0]:""}var JI=/^[^&#]+/;function eE(e){let t=e.match(JI);return t?t[0]:""}var dd=class{url;remaining;constructor(t){this.url=t,this.remaining=t}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new G([],{}):new G([],this.parseChildren())}parseQueryParams(){let t={};if(this.consumeOptional("?"))do this.parseQueryParam(t);while(this.consumeOptional("&"));return t}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let t=[];for(this.peekStartsWith("(")||t.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),t.push(this.parseSegment());let n={};this.peekStartsWith("/(")&&(this.capture("/"),n=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(t.length>0||Object.keys(n).length>0)&&(r[M]=new G(t,n)),r}parseSegment(){let t=od(this.remaining);if(t===""&&this.peekStartsWith(";"))throw new E(4009,!1);return this.capture(t),new vn(ya(t),this.parseMatrixParams())}parseMatrixParams(){let t={};for(;this.consumeOptional(";");)this.parseParam(t);return t}parseParam(t){let n=QI(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let o=od(this.remaining);o&&(r=o,this.capture(r))}t[ya(n)]=ya(r)}parseQueryParam(t){let n=XI(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let s=eE(this.remaining);s&&(r=s,this.capture(r))}let o=lg(n),i=lg(r);if(t.hasOwnProperty(o)){let s=t[o];Array.isArray(s)||(s=[s],t[o]=s),s.push(i)}else t[o]=i}parseParens(t){let n={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=od(this.remaining),o=this.remaining[r.length];if(o!=="/"&&o!==")"&&o!==";")throw new E(4010,!1);let i;r.indexOf(":")>-1?(i=r.slice(0,r.indexOf(":")),this.capture(i),this.capture(":")):t&&(i=M);let s=this.parseChildren();n[i]=Object.keys(s).length===1?s[M]:new G([],s),this.consumeOptional("//")}return n}peekStartsWith(t){return this.remaining.startsWith(t)}consumeOptional(t){return this.peekStartsWith(t)?(this.remaining=this.remaining.substring(t.length),!0):!1}capture(t){if(!this.consumeOptional(t))throw new E(4011,!1)}};function wg(e){return e.segments.length>0?new G([],{[M]:e}):e}function _g(e){let t={};for(let[r,o]of Object.entries(e.children)){let i=_g(o);if(r===M&&i.segments.length===0&&i.hasChildren())for(let[s,a]of Object.entries(i.children))t[s]=a;else(i.segments.length>0||i.hasChildren())&&(t[r]=i)}let n=new G(e.segments,t);return tE(n)}function tE(e){if(e.numberOfChildren===1&&e.children[M]){let t=e.children[M];return new G(e.segments.concat(t.segments),t.children)}return e}function Pr(e){return e instanceof St}function Sg(e,t,n=null,r=null){let o=Tg(e);return Mg(o,t,n,r)}function Tg(e){let t;function n(i){let s={};for(let c of i.children){let l=n(c);s[c.outlet]=l}let a=new G(i.url,s);return i===e&&(t=a),a}let r=n(e.root),o=wg(r);return t??o}function Mg(e,t,n,r){let o=e;for(;o.parent;)o=o.parent;if(t.length===0)return id(o,o,o,n,r);let i=nE(t);if(i.toRoot())return id(o,o,new G([],{}),n,r);let s=rE(i,o,e),a=s.processChildren?Ko(s.segmentGroup,s.index,i.commands):Ag(s.segmentGroup,s.index,i.commands);return id(o,s.segmentGroup,a,n,r)}function Da(e){return typeof e=="object"&&e!=null&&!e.outlets&&!e.segmentPath}function ei(e){return typeof e=="object"&&e!=null&&e.outlets}function id(e,t,n,r,o){let i={};r&&Object.entries(r).forEach(([c,l])=>{i[c]=Array.isArray(l)?l.map(u=>`${u}`):`${l}`});let s;e===t?s=n:s=xg(e,t,n);let a=wg(_g(s));return new St(a,i,o)}function xg(e,t,n){let r={};return Object.entries(e.children).forEach(([o,i])=>{i===t?r[o]=n:r[o]=xg(i,t,n)}),new G(e.segments,r)}var Ca=class{isAbsolute;numberOfDoubleDots;commands;constructor(t,n,r){if(this.isAbsolute=t,this.numberOfDoubleDots=n,this.commands=r,t&&r.length>0&&Da(r[0]))throw new E(4003,!1);let o=r.find(ei);if(o&&o!==vg(r))throw new E(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function nE(e){if(typeof e[0]=="string"&&e.length===1&&e[0]==="/")return new Ca(!0,0,e);let t=0,n=!1,r=e.reduce((o,i,s)=>{if(typeof i=="object"&&i!=null){if(i.outlets){let a={};return Object.entries(i.outlets).forEach(([c,l])=>{a[c]=typeof l=="string"?l.split("/"):l}),[...o,{outlets:a}]}if(i.segmentPath)return[...o,i.segmentPath]}return typeof i!="string"?[...o,i]:s===0?(i.split("/").forEach((a,c)=>{c==0&&a==="."||(c==0&&a===""?n=!0:a===".."?t++:a!=""&&o.push(a))}),o):[...o,i]},[]);return new Ca(n,t,r)}var kr=class{segmentGroup;processChildren;index;constructor(t,n,r){this.segmentGroup=t,this.processChildren=n,this.index=r}};function rE(e,t,n){if(e.isAbsolute)return new kr(t,!0,0);if(!n)return new kr(t,!1,NaN);if(n.parent===null)return new kr(n,!0,0);let r=Da(e.commands[0])?0:1,o=n.segments.length-1+r;return oE(n,o,e.numberOfDoubleDots)}function oE(e,t,n){let r=e,o=t,i=n;for(;i>o;){if(i-=o,r=r.parent,!r)throw new E(4005,!1);o=r.segments.length}return new kr(r,!1,o-i)}function iE(e){return ei(e[0])?e[0].outlets:{[M]:e}}function Ag(e,t,n){if(e??=new G([],{}),e.segments.length===0&&e.hasChildren())return Ko(e,t,n);let r=sE(e,t,n),o=n.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){let i=new G(e.segments.slice(0,r.pathIndex),{});return i.children[M]=new G(e.segments.slice(r.pathIndex),e.children),Ko(i,0,o)}else return r.match&&o.length===0?new G(e.segments,{}):r.match&&!e.hasChildren()?fd(e,t,n):r.match?Ko(e,0,o):fd(e,t,n)}function Ko(e,t,n){if(n.length===0)return new G(e.segments,{});{let r=iE(n),o={};if(Object.keys(r).some(i=>i!==M)&&e.children[M]&&e.numberOfChildren===1&&e.children[M].segments.length===0){let i=Ko(e.children[M],t,n);return new G(e.segments,i.children)}return Object.entries(r).forEach(([i,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(o[i]=Ag(e.children[i],t,s))}),Object.entries(e.children).forEach(([i,s])=>{r[i]===void 0&&(o[i]=s)}),new G(e.segments,o)}}function sE(e,t,n){let r=0,o=t,i={match:!1,pathIndex:0,commandIndex:0};for(;o<e.segments.length;){if(r>=n.length)return i;let s=e.segments[o],a=n[r];if(ei(a))break;let c=`${a}`,l=r<n.length-1?n[r+1]:null;if(o>0&&c===void 0)break;if(c&&l&&typeof l=="object"&&l.outlets===void 0){if(!dg(c,l,s))return i;r+=2}else{if(!dg(c,{},s))return i;r++}o++}return{match:!0,pathIndex:o,commandIndex:r}}function fd(e,t,n){let r=e.segments.slice(0,t),o=0;for(;o<n.length;){let i=n[o];if(ei(i)){let c=aE(i.outlets);return new G(r,c)}if(o===0&&Da(n[0])){let c=e.segments[t];r.push(new vn(c.path,ug(n[0]))),o++;continue}let s=ei(i)?i.outlets[M]:`${i}`,a=o<n.length-1?n[o+1]:null;s&&a&&Da(a)?(r.push(new vn(s,ug(a))),o+=2):(r.push(new vn(s,{})),o++)}return new G(r,{})}function aE(e){let t={};return Object.entries(e).forEach(([n,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(t[n]=fd(new G([],{}),0,r))}),t}function ug(e){let t={};return Object.entries(e).forEach(([n,r])=>t[n]=`${r}`),t}function dg(e,t,n){return e==n.path&&wt(t,n.parameters)}var Xo="imperative",_e=function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e}(_e||{}),Ye=class{id;url;constructor(t,n){this.id=t,this.url=n}},qt=class extends Ye{type=_e.NavigationStart;navigationTrigger;restoredState;constructor(t,n,r="imperative",o=null){super(t,n),this.navigationTrigger=r,this.restoredState=o}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},Tt=class extends Ye{urlAfterRedirects;type=_e.NavigationEnd;constructor(t,n,r){super(t,n),this.urlAfterRedirects=r}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},Fe=function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e[e.Aborted=4]="Aborted",e}(Fe||{}),ti=function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e}(ti||{}),_t=class extends Ye{reason;code;type=_e.NavigationCancel;constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},Zt=class extends Ye{reason;code;type=_e.NavigationSkipped;constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o}},Fr=class extends Ye{error;target;type=_e.NavigationError;constructor(t,n,r,o){super(t,n),this.error=r,this.target=o}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},ni=class extends Ye{urlAfterRedirects;state;type=_e.RoutesRecognized;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},ba=class extends Ye{urlAfterRedirects;state;type=_e.GuardsCheckStart;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Ia=class extends Ye{urlAfterRedirects;state;shouldActivate;type=_e.GuardsCheckEnd;constructor(t,n,r,o,i){super(t,n),this.urlAfterRedirects=r,this.state=o,this.shouldActivate=i}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},Ea=class extends Ye{urlAfterRedirects;state;type=_e.ResolveStart;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},wa=class extends Ye{urlAfterRedirects;state;type=_e.ResolveEnd;constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},_a=class{route;type=_e.RouteConfigLoadStart;constructor(t){this.route=t}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},Sa=class{route;type=_e.RouteConfigLoadEnd;constructor(t){this.route=t}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},Ta=class{snapshot;type=_e.ChildActivationStart;constructor(t){this.snapshot=t}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Ma=class{snapshot;type=_e.ChildActivationEnd;constructor(t){this.snapshot=t}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},xa=class{snapshot;type=_e.ActivationStart;constructor(t){this.snapshot=t}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Aa=class{snapshot;type=_e.ActivationEnd;constructor(t){this.snapshot=t}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}};var ri=class{},Lr=class{url;navigationBehaviorOptions;constructor(t,n){this.url=t,this.navigationBehaviorOptions=n}};function cE(e){return!(e instanceof ri)&&!(e instanceof Lr)}function lE(e,t){return e.providers&&!e._injector&&(e._injector=xr(e.providers,t,`Route: ${e.path}`)),e._injector??t}function dt(e){return e.outlet||M}function uE(e,t){let n=e.filter(r=>dt(r)===t);return n.push(...e.filter(r=>dt(r)!==t)),n}function ui(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let t=e.parent;t;t=t.parent){let n=t.routeConfig;if(n?._loadedInjector)return n._loadedInjector;if(n?._injector)return n._injector}return null}var Ra=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){return ui(this.route?.snapshot)??this.rootInjector}constructor(t){this.rootInjector=t,this.children=new Qt(this.rootInjector)}},Qt=(()=>{class e{rootInjector;contexts=new Map;constructor(n){this.rootInjector=n}onChildOutletCreated(n,r){let o=this.getOrCreateContext(n);o.outlet=r,this.contexts.set(n,o)}onChildOutletDestroyed(n){let r=this.getContext(n);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let n=this.contexts;return this.contexts=new Map,n}onOutletReAttached(n){this.contexts=n}getOrCreateContext(n){let r=this.getContext(n);return r||(r=new Ra(this.rootInjector),this.contexts.set(n,r)),r}getContext(n){return this.contexts.get(n)||null}static \u0275fac=function(r){return new(r||e)(I(ee))};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Na=class{_root;constructor(t){this._root=t}get root(){return this._root.value}parent(t){let n=this.pathFromRoot(t);return n.length>1?n[n.length-2]:null}children(t){let n=pd(t,this._root);return n?n.children.map(r=>r.value):[]}firstChild(t){let n=pd(t,this._root);return n&&n.children.length>0?n.children[0].value:null}siblings(t){let n=hd(t,this._root);return n.length<2?[]:n[n.length-2].children.map(o=>o.value).filter(o=>o!==t)}pathFromRoot(t){return hd(t,this._root).map(n=>n.value)}};function pd(e,t){if(e===t.value)return t;for(let n of t.children){let r=pd(e,n);if(r)return r}return null}function hd(e,t){if(e===t.value)return[t];for(let n of t.children){let r=hd(e,n);if(r.length)return r.unshift(t),r}return[]}var Ze=class{value;children;constructor(t,n){this.value=t,this.children=n}toString(){return`TreeNode(${this.value})`}};function Nr(e){let t={};return e&&e.children.forEach(n=>t[n.value.outlet]=n),t}var oi=class extends Na{snapshot;constructor(t,n){super(t),this.snapshot=n,Id(this,t)}toString(){return this.snapshot.toString()}};function Rg(e){let t=dE(e),n=new pe([new vn("",{})]),r=new pe({}),o=new pe({}),i=new pe({}),s=new pe(""),a=new Le(n,r,i,s,o,M,e,t.root);return a.snapshot=t.root,new oi(new Ze(a,[]),t)}function dE(e){let t={},n={},r={},o="",i=new Bn([],t,r,o,n,M,e,null,{});return new ii("",new Ze(i,[]))}var Le=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(t,n,r,o,i,s,a,c){this.urlSubject=t,this.paramsSubject=n,this.queryParamsSubject=r,this.fragmentSubject=o,this.dataSubject=i,this.outlet=s,this.component=a,this._futureSnapshot=c,this.title=this.dataSubject?.pipe(L(l=>l[li]))??S(void 0),this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(L(t=>Hn(t))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(L(t=>Hn(t))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function ka(e,t,n="emptyOnly"){let r,{routeConfig:o}=e;return t!==null&&(n==="always"||o?.path===""||!t.component&&!t.routeConfig?.loadComponent)?r={params:y(y({},t.params),e.params),data:y(y({},t.data),e.data),resolve:y(y(y(y({},e.data),t.data),o?.data),e._resolvedData)}:r={params:y({},e.params),data:y({},e.data),resolve:y(y({},e.data),e._resolvedData??{})},o&&kg(o)&&(r.resolve[li]=o.title),r}var Bn=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){return this.data?.[li]}constructor(t,n,r,o,i,s,a,c,l){this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i,this.outlet=s,this.component=a,this.routeConfig=c,this._resolve=l}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=Hn(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=Hn(this.queryParams),this._queryParamMap}toString(){let t=this.url.map(r=>r.toString()).join("/"),n=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${t}', path:'${n}')`}},ii=class extends Na{url;constructor(t,n){super(n),this.url=t,Id(this,n)}toString(){return Ng(this._root)}};function Id(e,t){t.value._routerState=e,t.children.forEach(n=>Id(e,n))}function Ng(e){let t=e.children.length>0?` { ${e.children.map(Ng).join(", ")} } `:"";return`${e.value}${t}`}function sd(e){if(e.snapshot){let t=e.snapshot,n=e._futureSnapshot;e.snapshot=n,wt(t.queryParams,n.queryParams)||e.queryParamsSubject.next(n.queryParams),t.fragment!==n.fragment&&e.fragmentSubject.next(n.fragment),wt(t.params,n.params)||e.paramsSubject.next(n.params),jI(t.url,n.url)||e.urlSubject.next(n.url),wt(t.data,n.data)||e.dataSubject.next(n.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function md(e,t){let n=wt(e.params,t.params)&&$I(e.url,t.url),r=!e.parent!=!t.parent;return n&&!r&&(!e.parent||md(e.parent,t.parent))}function kg(e){return typeof e.title=="string"||e.title===null}var Og=new w(""),Ed=(()=>{class e{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=M;activateEvents=new ce;deactivateEvents=new ce;attachEvents=new ce;detachEvents=new ce;routerOutletData=Vm(void 0);parentContexts=h(Qt);location=h(lt);changeDetector=h(et);inputBinder=h(La,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(n){if(n.name){let{firstChange:r,previousValue:o}=n.name;if(r)return;this.isTrackedInParentContexts(o)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(o)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(n){return this.parentContexts.getContext(n)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let n=this.parentContexts.getContext(this.name);n?.route&&(n.attachRef?this.attach(n.attachRef,n.route):this.activateWith(n.route,n.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new E(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new E(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new E(4012,!1);this.location.detach();let n=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(n.instance),n}attach(n,r){this.activated=n,this._activatedRoute=r,this.location.insert(n.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(n.instance)}deactivate(){if(this.activated){let n=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(n)}}activateWith(n,r){if(this.isActivated)throw new E(4013,!1);this._activatedRoute=n;let o=this.location,s=n.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,c=new gd(n,a,o.injector,this.routerOutletData);this.activated=o.createComponent(s,{index:o.length,injector:c,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static \u0275fac=function(r){return new(r||e)};static \u0275dir=zt({type:e,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[Ln]})}return e})(),gd=class{route;childContexts;parent;outletData;constructor(t,n,r,o){this.route=t,this.childContexts=n,this.parent=r,this.outletData=o}get(t,n){return t===Le?this.route:t===Qt?this.childContexts:t===Og?this.outletData:this.parent.get(t,n)}},La=new w("");var wd=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=It({type:e,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(r,o){r&1&&oa(0,"router-outlet")},dependencies:[Ed],encapsulation:2})}return e})();function _d(e){let t=e.children&&e.children.map(_d),n=t?U(y({},e),{children:t}):y({},e);return!n.component&&!n.loadComponent&&(t||n.loadChildren)&&n.outlet&&n.outlet!==M&&(n.component=wd),n}function fE(e,t,n){let r=si(e,t._root,n?n._root:void 0);return new oi(r,t)}function si(e,t,n){if(n&&e.shouldReuseRoute(t.value,n.value.snapshot)){let r=n.value;r._futureSnapshot=t.value;let o=pE(e,t,n);return new Ze(r,o)}else{if(e.shouldAttach(t.value)){let i=e.retrieve(t.value);if(i!==null){let s=i.route;return s.value._futureSnapshot=t.value,s.children=t.children.map(a=>si(e,a)),s}}let r=hE(t.value),o=t.children.map(i=>si(e,i));return new Ze(r,o)}}function pE(e,t,n){return t.children.map(r=>{for(let o of n.children)if(e.shouldReuseRoute(r.value,o.value.snapshot))return si(e,r,o);return si(e,r)})}function hE(e){return new Le(new pe(e.url),new pe(e.params),new pe(e.queryParams),new pe(e.fragment),new pe(e.data),e.outlet,e.component,e)}var jr=class{redirectTo;navigationBehaviorOptions;constructor(t,n){this.redirectTo=t,this.navigationBehaviorOptions=n}},Pg="ngNavigationCancelingError";function Oa(e,t){let{redirectTo:n,navigationBehaviorOptions:r}=Pr(t)?{redirectTo:t,navigationBehaviorOptions:void 0}:t,o=Fg(!1,Fe.Redirect);return o.url=n,o.navigationBehaviorOptions=r,o}function Fg(e,t){let n=new Error(`NavigationCancelingError: ${e||""}`);return n[Pg]=!0,n.cancellationCode=t,n}function mE(e){return Lg(e)&&Pr(e.url)}function Lg(e){return!!e&&e[Pg]}var gE=(e,t,n,r)=>L(o=>(new vd(t,o.targetRouterState,o.currentRouterState,n,r).activate(e),o)),vd=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(t,n,r,o,i){this.routeReuseStrategy=t,this.futureState=n,this.currState=r,this.forwardEvent=o,this.inputBindingEnabled=i}activate(t){let n=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(n,r,t),sd(this.futureState.root),this.activateChildRoutes(n,r,t)}deactivateChildRoutes(t,n,r){let o=Nr(n);t.children.forEach(i=>{let s=i.value.outlet;this.deactivateRoutes(i,o[s],r),delete o[s]}),Object.values(o).forEach(i=>{this.deactivateRouteAndItsChildren(i,r)})}deactivateRoutes(t,n,r){let o=t.value,i=n?n.value:null;if(o===i)if(o.component){let s=r.getContext(o.outlet);s&&this.deactivateChildRoutes(t,n,s.children)}else this.deactivateChildRoutes(t,n,r);else i&&this.deactivateRouteAndItsChildren(n,r)}deactivateRouteAndItsChildren(t,n){t.value.component&&this.routeReuseStrategy.shouldDetach(t.value.snapshot)?this.detachAndStoreRouteSubtree(t,n):this.deactivateRouteAndOutlet(t,n)}detachAndStoreRouteSubtree(t,n){let r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=Nr(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);if(r&&r.outlet){let s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(t.value.snapshot,{componentRef:s,route:t,contexts:a})}}deactivateRouteAndOutlet(t,n){let r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=Nr(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(t,n,r){let o=Nr(n);t.children.forEach(i=>{this.activateRoutes(i,o[i.value.outlet],r),this.forwardEvent(new Aa(i.value.snapshot))}),t.children.length&&this.forwardEvent(new Ma(t.value.snapshot))}activateRoutes(t,n,r){let o=t.value,i=n?n.value:null;if(sd(o),o===i)if(o.component){let s=r.getOrCreateContext(o.outlet);this.activateChildRoutes(t,n,s.children)}else this.activateChildRoutes(t,n,r);else if(o.component){let s=r.getOrCreateContext(o.outlet);if(this.routeReuseStrategy.shouldAttach(o.snapshot)){let a=this.routeReuseStrategy.retrieve(o.snapshot);this.routeReuseStrategy.store(o.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),sd(a.route.value),this.activateChildRoutes(t,null,s.children)}else s.attachRef=null,s.route=o,s.outlet&&s.outlet.activateWith(o,s.injector),this.activateChildRoutes(t,null,s.children)}else this.activateChildRoutes(t,null,r)}},Pa=class{path;route;constructor(t){this.path=t,this.route=this.path[this.path.length-1]}},Or=class{component;route;constructor(t,n){this.component=t,this.route=n}};function vE(e,t,n){let r=e._root,o=t?t._root:null;return Qo(r,o,n,[r.value])}function yE(e){let t=e.routeConfig?e.routeConfig.canActivateChild:null;return!t||t.length===0?null:{node:e,guards:t}}function Br(e,t){let n=Symbol(),r=t.get(e,n);return r===n?typeof e=="function"&&!il(e)?e:t.get(e):r}function Qo(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=Nr(t);return e.children.forEach(s=>{DE(s,i[s.value.outlet],n,r.concat([s.value]),o),delete i[s.value.outlet]}),Object.entries(i).forEach(([s,a])=>Jo(a,n.getContext(s),o)),o}function DE(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=e.value,s=t?t.value:null,a=n?n.getContext(e.value.outlet):null;if(s&&i.routeConfig===s.routeConfig){let c=CE(s,i,i.routeConfig.runGuardsAndResolvers);c?o.canActivateChecks.push(new Pa(r)):(i.data=s.data,i._resolvedData=s._resolvedData),i.component?Qo(e,t,a?a.children:null,r,o):Qo(e,t,n,r,o),c&&a&&a.outlet&&a.outlet.isActivated&&o.canDeactivateChecks.push(new Or(a.outlet.component,s))}else s&&Jo(t,a,o),o.canActivateChecks.push(new Pa(r)),i.component?Qo(e,null,a?a.children:null,r,o):Qo(e,null,n,r,o);return o}function CE(e,t,n){if(typeof n=="function")return n(e,t);switch(n){case"pathParamsChange":return!Vn(e.url,t.url);case"pathParamsOrQueryParamsChange":return!Vn(e.url,t.url)||!wt(e.queryParams,t.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!md(e,t)||!wt(e.queryParams,t.queryParams);case"paramsChange":default:return!md(e,t)}}function Jo(e,t,n){let r=Nr(e),o=e.value;Object.entries(r).forEach(([i,s])=>{o.component?t?Jo(s,t.children.getContext(i),n):Jo(s,null,n):Jo(s,t,n)}),o.component?t&&t.outlet&&t.outlet.isActivated?n.canDeactivateChecks.push(new Or(t.outlet.component,o)):n.canDeactivateChecks.push(new Or(null,o)):n.canDeactivateChecks.push(new Or(null,o))}function di(e){return typeof e=="function"}function bE(e){return typeof e=="boolean"}function IE(e){return e&&di(e.canLoad)}function EE(e){return e&&di(e.canActivate)}function wE(e){return e&&di(e.canActivateChild)}function _E(e){return e&&di(e.canDeactivate)}function SE(e){return e&&di(e.canMatch)}function jg(e){return e instanceof kt||e?.name==="EmptyError"}var ma=Symbol("INITIAL_VALUE");function Vr(){return Ae(e=>ir(e.map(t=>t.pipe(Ot(1),zc(ma)))).pipe(L(t=>{for(let n of t)if(n!==!0){if(n===ma)return ma;if(n===!1||TE(n))return n}return!0}),Se(t=>t!==ma),Ot(1)))}function TE(e){return Pr(e)||e instanceof jr}function ME(e,t){return oe(n=>{let{targetSnapshot:r,currentSnapshot:o,guards:{canActivateChecks:i,canDeactivateChecks:s}}=n;return s.length===0&&i.length===0?S(U(y({},n),{guardsResult:!0})):xE(s,r,o,e).pipe(oe(a=>a&&bE(a)?AE(r,i,e,t):S(a)),L(a=>U(y({},n),{guardsResult:a})))})}function xE(e,t,n,r){return ie(e).pipe(oe(o=>PE(o.component,o.route,n,t,r)),Pt(o=>o!==!0,!0))}function AE(e,t,n,r){return ie(t).pipe(tn(o=>ar(NE(o.route.parent,r),RE(o.route,r),OE(e,o.path,n),kE(e,o.route,n))),Pt(o=>o!==!0,!0))}function RE(e,t){return e!==null&&t&&t(new xa(e)),S(!0)}function NE(e,t){return e!==null&&t&&t(new Ta(e)),S(!0)}function kE(e,t,n){let r=t.routeConfig?t.routeConfig.canActivate:null;if(!r||r.length===0)return S(!0);let o=r.map(i=>lo(()=>{let s=ui(t)??n,a=Br(i,s),c=EE(a)?a.canActivate(t,e):Ge(s,()=>a(t,e));return Yt(c).pipe(Pt())}));return S(o).pipe(Vr())}function OE(e,t,n){let r=t[t.length-1],i=t.slice(0,t.length-1).reverse().map(s=>yE(s)).filter(s=>s!==null).map(s=>lo(()=>{let a=s.guards.map(c=>{let l=ui(s.node)??n,u=Br(c,l),f=wE(u)?u.canActivateChild(r,e):Ge(l,()=>u(r,e));return Yt(f).pipe(Pt())});return S(a).pipe(Vr())}));return S(i).pipe(Vr())}function PE(e,t,n,r,o){let i=t&&t.routeConfig?t.routeConfig.canDeactivate:null;if(!i||i.length===0)return S(!0);let s=i.map(a=>{let c=ui(t)??o,l=Br(a,c),u=_E(l)?l.canDeactivate(e,t,n,r):Ge(c,()=>l(e,t,n,r));return Yt(u).pipe(Pt())});return S(s).pipe(Vr())}function FE(e,t,n,r){let o=t.canLoad;if(o===void 0||o.length===0)return S(!0);let i=o.map(s=>{let a=Br(s,e),c=IE(a)?a.canLoad(t,n):Ge(e,()=>a(t,n));return Yt(c)});return S(i).pipe(Vr(),Vg(r))}function Vg(e){return Fc(be(t=>{if(typeof t!="boolean")throw Oa(e,t)}),L(t=>t===!0))}function LE(e,t,n,r){let o=t.canMatch;if(!o||o.length===0)return S(!0);let i=o.map(s=>{let a=Br(s,e),c=SE(a)?a.canMatch(t,n):Ge(e,()=>a(t,n));return Yt(c)});return S(i).pipe(Vr(),Vg(r))}var ai=class{segmentGroup;constructor(t){this.segmentGroup=t||null}},ci=class extends Error{urlTree;constructor(t){super(),this.urlTree=t}};function Rr(e){return or(new ai(e))}function jE(e){return or(new E(4e3,!1))}function VE(e){return or(Fg(!1,Fe.GuardRejected))}var yd=class{urlSerializer;urlTree;constructor(t,n){this.urlSerializer=t,this.urlTree=n}lineralizeSegments(t,n){let r=[],o=n.root;for(;;){if(r=r.concat(o.segments),o.numberOfChildren===0)return S(r);if(o.numberOfChildren>1||!o.children[M])return jE(`${t.redirectTo}`);o=o.children[M]}}applyRedirectCommands(t,n,r,o,i){return BE(n,o,i).pipe(L(s=>{if(s instanceof St)throw new ci(s);let a=this.applyRedirectCreateUrlTree(s,this.urlSerializer.parse(s),t,r);if(s[0]==="/")throw new ci(a);return a}))}applyRedirectCreateUrlTree(t,n,r,o){let i=this.createSegmentGroup(t,n.root,r,o);return new St(i,this.createQueryParams(n.queryParams,this.urlTree.queryParams),n.fragment)}createQueryParams(t,n){let r={};return Object.entries(t).forEach(([o,i])=>{if(typeof i=="string"&&i[0]===":"){let a=i.substring(1);r[o]=n[a]}else r[o]=i}),r}createSegmentGroup(t,n,r,o){let i=this.createSegments(t,n.segments,r,o),s={};return Object.entries(n.children).forEach(([a,c])=>{s[a]=this.createSegmentGroup(t,c,r,o)}),new G(i,s)}createSegments(t,n,r,o){return n.map(i=>i.path[0]===":"?this.findPosParam(t,i,o):this.findOrReturn(i,r))}findPosParam(t,n,r){let o=r[n.path.substring(1)];if(!o)throw new E(4001,!1);return o}findOrReturn(t,n){let r=0;for(let o of n){if(o.path===t.path)return n.splice(r),o;r++}return t}};function BE(e,t,n){if(typeof e=="string")return S(e);let r=e,{queryParams:o,fragment:i,routeConfig:s,url:a,outlet:c,params:l,data:u,title:f}=t;return Yt(Ge(n,()=>r({params:l,data:u,queryParams:o,fragment:i,routeConfig:s,url:a,outlet:c,title:f})))}var Dd={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function HE(e,t,n,r,o){let i=Bg(e,t,n);return i.matched?(r=lE(t,r),LE(r,t,n,o).pipe(L(s=>s===!0?i:y({},Dd)))):S(i)}function Bg(e,t,n){if(t.path==="**")return $E(n);if(t.path==="")return t.pathMatch==="full"&&(e.hasChildren()||n.length>0)?y({},Dd):{matched:!0,consumedSegments:[],remainingSegments:n,parameters:{},positionalParamSegments:{}};let o=(t.matcher||mg)(n,e,t);if(!o)return y({},Dd);let i={};Object.entries(o.posParams??{}).forEach(([a,c])=>{i[a]=c.path});let s=o.consumed.length>0?y(y({},i),o.consumed[o.consumed.length-1].parameters):i;return{matched:!0,consumedSegments:o.consumed,remainingSegments:n.slice(o.consumed.length),parameters:s,positionalParamSegments:o.posParams??{}}}function $E(e){return{matched:!0,parameters:e.length>0?vg(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}function fg(e,t,n,r){return n.length>0&&GE(e,n,r)?{segmentGroup:new G(t,zE(r,new G(n,e.children))),slicedSegments:[]}:n.length===0&&WE(e,n,r)?{segmentGroup:new G(e.segments,UE(e,n,r,e.children)),slicedSegments:n}:{segmentGroup:new G(e.segments,e.children),slicedSegments:n}}function UE(e,t,n,r){let o={};for(let i of n)if(ja(e,t,i)&&!r[dt(i)]){let s=new G([],{});o[dt(i)]=s}return y(y({},r),o)}function zE(e,t){let n={};n[M]=t;for(let r of e)if(r.path===""&&dt(r)!==M){let o=new G([],{});n[dt(r)]=o}return n}function GE(e,t,n){return n.some(r=>ja(e,t,r)&&dt(r)!==M)}function WE(e,t,n){return n.some(r=>ja(e,t,r))}function ja(e,t,n){return(e.hasChildren()||t.length>0)&&n.pathMatch==="full"?!1:n.path===""}function qE(e,t,n){return t.length===0&&!e.children[n]}var Cd=class{};function ZE(e,t,n,r,o,i,s="emptyOnly"){return new bd(e,t,n,r,o,s,i).recognize()}var YE=31,bd=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(t,n,r,o,i,s,a){this.injector=t,this.configLoader=n,this.rootComponentType=r,this.config=o,this.urlTree=i,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new yd(this.urlSerializer,this.urlTree)}noMatchError(t){return new E(4002,`'${t.segmentGroup}'`)}recognize(){let t=fg(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(t).pipe(L(({children:n,rootSnapshot:r})=>{let o=new Ze(r,n),i=new ii("",o),s=Sg(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,i.url=this.urlSerializer.serialize(s),{state:i,tree:s}}))}match(t){let n=new Bn([],Object.freeze({}),Object.freeze(y({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),M,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,t,M,n).pipe(L(r=>({children:r,rootSnapshot:n})),pt(r=>{if(r instanceof ci)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof ai?this.noMatchError(r):r}))}processSegmentGroup(t,n,r,o,i){return r.segments.length===0&&r.hasChildren()?this.processChildren(t,n,r,i):this.processSegment(t,n,r,r.segments,o,!0,i).pipe(L(s=>s instanceof Ze?[s]:[]))}processChildren(t,n,r,o){let i=[];for(let s of Object.keys(r.children))s==="primary"?i.unshift(s):i.push(s);return ie(i).pipe(tn(s=>{let a=r.children[s],c=uE(n,s);return this.processSegmentGroup(t,c,a,s,o)}),Uc((s,a)=>(s.push(...a),s)),nn(null),$c(),oe(s=>{if(s===null)return Rr(r);let a=Hg(s);return QE(a),S(a)}))}processSegment(t,n,r,o,i,s,a){return ie(n).pipe(tn(c=>this.processSegmentAgainstRoute(c._injector??t,n,c,r,o,i,s,a).pipe(pt(l=>{if(l instanceof ai)return S(null);throw l}))),Pt(c=>!!c),pt(c=>{if(jg(c))return qE(r,o,i)?S(new Cd):Rr(r);throw c}))}processSegmentAgainstRoute(t,n,r,o,i,s,a,c){return dt(r)!==s&&(s===M||!ja(o,i,r))?Rr(o):r.redirectTo===void 0?this.matchSegmentAgainstRoute(t,o,r,i,s,c):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(t,o,n,r,i,s,c):Rr(o)}expandSegmentAgainstRouteUsingRedirect(t,n,r,o,i,s,a){let{matched:c,parameters:l,consumedSegments:u,positionalParamSegments:f,remainingSegments:p}=Bg(n,o,i);if(!c)return Rr(n);typeof o.redirectTo=="string"&&o.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>YE&&(this.allowRedirects=!1));let d=new Bn(i,l,Object.freeze(y({},this.urlTree.queryParams)),this.urlTree.fragment,pg(o),dt(o),o.component??o._loadedComponent??null,o,hg(o)),m=ka(d,a,this.paramsInheritanceStrategy);return d.params=Object.freeze(m.params),d.data=Object.freeze(m.data),this.applyRedirects.applyRedirectCommands(u,o.redirectTo,f,d,t).pipe(Ae(v=>this.applyRedirects.lineralizeSegments(o,v)),oe(v=>this.processSegment(t,r,n,v.concat(p),s,!1,a)))}matchSegmentAgainstRoute(t,n,r,o,i,s){let a=HE(n,r,o,t,this.urlSerializer);return r.path==="**"&&(n.children={}),a.pipe(Ae(c=>c.matched?(t=r._injector??t,this.getChildConfig(t,r,o).pipe(Ae(({routes:l})=>{let u=r._loadedInjector??t,{parameters:f,consumedSegments:p,remainingSegments:d}=c,m=new Bn(p,f,Object.freeze(y({},this.urlTree.queryParams)),this.urlTree.fragment,pg(r),dt(r),r.component??r._loadedComponent??null,r,hg(r)),g=ka(m,s,this.paramsInheritanceStrategy);m.params=Object.freeze(g.params),m.data=Object.freeze(g.data);let{segmentGroup:v,slicedSegments:D}=fg(n,p,d,l);if(D.length===0&&v.hasChildren())return this.processChildren(u,l,v,m).pipe(L(N=>new Ze(m,N)));if(l.length===0&&D.length===0)return S(new Ze(m,[]));let Z=dt(r)===i;return this.processSegment(u,l,v,D,Z?M:i,!0,m).pipe(L(N=>new Ze(m,N instanceof Ze?[N]:[])))}))):Rr(n)))}getChildConfig(t,n,r){return n.children?S({routes:n.children,injector:t}):n.loadChildren?n._loadedRoutes!==void 0?S({routes:n._loadedRoutes,injector:n._loadedInjector}):FE(t,n,r,this.urlSerializer).pipe(oe(o=>o?this.configLoader.loadChildren(t,n).pipe(be(i=>{n._loadedRoutes=i.routes,n._loadedInjector=i.injector})):VE(n))):S({routes:[],injector:t})}};function QE(e){e.sort((t,n)=>t.value.outlet===M?-1:n.value.outlet===M?1:t.value.outlet.localeCompare(n.value.outlet))}function KE(e){let t=e.value.routeConfig;return t&&t.path===""}function Hg(e){let t=[],n=new Set;for(let r of e){if(!KE(r)){t.push(r);continue}let o=t.find(i=>r.value.routeConfig===i.value.routeConfig);o!==void 0?(o.children.push(...r.children),n.add(o)):t.push(r)}for(let r of n){let o=Hg(r.children);t.push(new Ze(r.value,o))}return t.filter(r=>!n.has(r))}function pg(e){return e.data||{}}function hg(e){return e.resolve||{}}function XE(e,t,n,r,o,i){return oe(s=>ZE(e,t,n,r,s.extractedUrl,o,i).pipe(L(({state:a,tree:c})=>U(y({},s),{targetSnapshot:a,urlAfterRedirects:c}))))}function JE(e,t){return oe(n=>{let{targetSnapshot:r,guards:{canActivateChecks:o}}=n;if(!o.length)return S(n);let i=new Set(o.map(c=>c.route)),s=new Set;for(let c of i)if(!s.has(c))for(let l of $g(c))s.add(l);let a=0;return ie(s).pipe(tn(c=>i.has(c)?ew(c,r,e,t):(c.data=ka(c,c.parent,e).resolve,S(void 0))),be(()=>a++),lr(1),oe(c=>a===s.size?S(n):je))})}function $g(e){let t=e.children.map(n=>$g(n)).flat();return[e,...t]}function ew(e,t,n,r){let o=e.routeConfig,i=e._resolve;return o?.title!==void 0&&!kg(o)&&(i[li]=o.title),lo(()=>(e.data=ka(e,e.parent,n).resolve,tw(i,e,t,r).pipe(L(s=>(e._resolvedData=s,e.data=y(y({},e.data),s),null)))))}function tw(e,t,n,r){let o=ld(e);if(o.length===0)return S({});let i={};return ie(o).pipe(oe(s=>nw(e[s],t,n,r).pipe(Pt(),be(a=>{if(a instanceof jr)throw Oa(new $n,a);i[s]=a}))),lr(1),L(()=>i),pt(s=>jg(s)?je:or(s)))}function nw(e,t,n,r){let o=ui(t)??r,i=Br(e,o),s=i.resolve?i.resolve(t,n):Ge(o,()=>i(t,n));return Yt(s)}function ad(e){return Ae(t=>{let n=e(t);return n?ie(n).pipe(L(()=>t)):S(t)})}var Sd=(()=>{class e{buildTitle(n){let r,o=n.root;for(;o!==void 0;)r=this.getResolvedTitleForRoute(o)??r,o=o.children.find(i=>i.outlet===M);return r}getResolvedTitleForRoute(n){return n.data[li]}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:()=>h(Ug),providedIn:"root"})}return e})(),Ug=(()=>{class e extends Sd{title;constructor(n){super(),this.title=n}updateTitle(n){let r=this.buildTitle(n);r!==void 0&&this.title.setTitle(r)}static \u0275fac=function(r){return new(r||e)(I(ag))};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),fi=new w("",{providedIn:"root",factory:()=>({})}),pi=new w(""),Td=(()=>{class e{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=h(Hu);loadComponent(n){if(this.componentLoaders.get(n))return this.componentLoaders.get(n);if(n._loadedComponent)return S(n._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(n);let r=Yt(n.loadComponent()).pipe(L(Gg),be(i=>{this.onLoadEndListener&&this.onLoadEndListener(n),n._loadedComponent=i}),uo(()=>{this.componentLoaders.delete(n)})),o=new nr(r,()=>new te).pipe(tr());return this.componentLoaders.set(n,o),o}loadChildren(n,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return S({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let i=zg(r,this.compiler,n,this.onLoadEndListener).pipe(uo(()=>{this.childrenLoaders.delete(r)})),s=new nr(i,()=>new te).pipe(tr());return this.childrenLoaders.set(r,s),s}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function zg(e,t,n,r){return Yt(e.loadChildren()).pipe(L(Gg),oe(o=>o instanceof Js||Array.isArray(o)?S(o):ie(t.compileModuleAsync(o))),L(o=>{r&&r(e);let i,s,a=!1;return Array.isArray(o)?(s=o,a=!0):(i=o.create(n).injector,s=i.get(pi,[],{optional:!0,self:!0}).flat()),{routes:s.map(_d),injector:i}}))}function rw(e){return e&&typeof e=="object"&&"default"in e}function Gg(e){return rw(e)?e.default:e}var Va=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:()=>h(ow),providedIn:"root"})}return e})(),ow=(()=>{class e{shouldProcessUrl(n){return!0}extract(n){return n}merge(n,r){return n}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Wg=new w("");var qg=new w(""),Zg=(()=>{class e{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new te;transitionAbortWithErrorSubject=new te;configLoader=h(Td);environmentInjector=h(ee);destroyRef=h(Cr);urlSerializer=h(Un);rootContexts=h(Qt);location=h(ut);inputBindingEnabled=h(La,{optional:!0})!==null;titleStrategy=h(Sd);options=h(fi,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=h(Va);createViewTransition=h(Wg,{optional:!0});navigationErrorHandler=h(qg,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>S(void 0);rootComponentType=null;destroyed=!1;constructor(){let n=o=>this.events.next(new _a(o)),r=o=>this.events.next(new Sa(o));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=n,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){this.transitions?.complete()}handleNavigationRequest(n){let r=++this.navigationId;this.transitions?.next(U(y({},n),{extractedUrl:this.urlHandlingStrategy.extract(n.rawUrl),targetSnapshot:null,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null,abortController:new AbortController,id:r}))}setupNavigations(n){return this.transitions=new pe(null),this.transitions.pipe(Se(r=>r!==null),Ae(r=>{let o=!1;return S(r).pipe(Ae(i=>{if(this.navigationId>r.id)return this.cancelNavigationTransition(r,"",Fe.SupersededByNewNavigation),je;this.currentTransition=r,this.currentNavigation={id:i.id,initialUrl:i.rawUrl,extractedUrl:i.extractedUrl,targetBrowserUrl:typeof i.extras.browserUrl=="string"?this.urlSerializer.parse(i.extras.browserUrl):i.extras.browserUrl,trigger:i.source,extras:i.extras,previousNavigation:this.lastSuccessfulNavigation?U(y({},this.lastSuccessfulNavigation),{previousNavigation:null}):null,abort:()=>i.abortController.abort()};let s=!n.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),a=i.extras.onSameUrlNavigation??n.onSameUrlNavigation;if(!s&&a!=="reload"){let c="";return this.events.next(new Zt(i.id,this.urlSerializer.serialize(i.rawUrl),c,ti.IgnoredSameUrlNavigation)),i.resolve(!1),je}if(this.urlHandlingStrategy.shouldProcessUrl(i.rawUrl))return S(i).pipe(Ae(c=>(this.events.next(new qt(c.id,this.urlSerializer.serialize(c.extractedUrl),c.source,c.restoredState)),c.id!==this.navigationId?je:Promise.resolve(c))),XE(this.environmentInjector,this.configLoader,this.rootComponentType,n.config,this.urlSerializer,this.paramsInheritanceStrategy),be(c=>{r.targetSnapshot=c.targetSnapshot,r.urlAfterRedirects=c.urlAfterRedirects,this.currentNavigation=U(y({},this.currentNavigation),{finalUrl:c.urlAfterRedirects});let l=new ni(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(l)}));if(s&&this.urlHandlingStrategy.shouldProcessUrl(i.currentRawUrl)){let{id:c,extractedUrl:l,source:u,restoredState:f,extras:p}=i,d=new qt(c,this.urlSerializer.serialize(l),u,f);this.events.next(d);let m=Rg(this.rootComponentType).snapshot;return this.currentTransition=r=U(y({},i),{targetSnapshot:m,urlAfterRedirects:l,extras:U(y({},p),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=l,S(r)}else{let c="";return this.events.next(new Zt(i.id,this.urlSerializer.serialize(i.extractedUrl),c,ti.IgnoredByUrlHandlingStrategy)),i.resolve(!1),je}}),be(i=>{let s=new ba(i.id,this.urlSerializer.serialize(i.extractedUrl),this.urlSerializer.serialize(i.urlAfterRedirects),i.targetSnapshot);this.events.next(s)}),L(i=>(this.currentTransition=r=U(y({},i),{guards:vE(i.targetSnapshot,i.currentSnapshot,this.rootContexts)}),r)),ME(this.environmentInjector,i=>this.events.next(i)),be(i=>{if(r.guardsResult=i.guardsResult,i.guardsResult&&typeof i.guardsResult!="boolean")throw Oa(this.urlSerializer,i.guardsResult);let s=new Ia(i.id,this.urlSerializer.serialize(i.extractedUrl),this.urlSerializer.serialize(i.urlAfterRedirects),i.targetSnapshot,!!i.guardsResult);this.events.next(s)}),Se(i=>i.guardsResult?!0:(this.cancelNavigationTransition(i,"",Fe.GuardRejected),!1)),ad(i=>{if(i.guards.canActivateChecks.length!==0)return S(i).pipe(be(s=>{let a=new Ea(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}),Ae(s=>{let a=!1;return S(s).pipe(JE(this.paramsInheritanceStrategy,this.environmentInjector),be({next:()=>a=!0,complete:()=>{a||this.cancelNavigationTransition(s,"",Fe.NoDataFromResolver)}}))}),be(s=>{let a=new wa(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}))}),ad(i=>{let s=a=>{let c=[];a.routeConfig?.loadComponent&&!a.routeConfig._loadedComponent&&c.push(this.configLoader.loadComponent(a.routeConfig).pipe(be(l=>{a.component=l}),L(()=>{})));for(let l of a.children)c.push(...s(l));return c};return ir(s(i.targetSnapshot.root)).pipe(nn(null),Ot(1))}),ad(()=>this.afterPreactivation()),Ae(()=>{let{currentSnapshot:i,targetSnapshot:s}=r,a=this.createViewTransition?.(this.environmentInjector,i.root,s.root);return a?ie(a).pipe(L(()=>r)):S(r)}),L(i=>{let s=fE(n.routeReuseStrategy,i.targetSnapshot,i.currentRouterState);return this.currentTransition=r=U(y({},i),{targetRouterState:s}),this.currentNavigation.targetRouterState=s,r}),be(()=>{this.events.next(new ri)}),gE(this.rootContexts,n.routeReuseStrategy,i=>this.events.next(i),this.inputBindingEnabled),Ot(1),ns(new F(i=>{let s=r.abortController.signal,a=()=>i.next();return s.addEventListener("abort",a),()=>s.removeEventListener("abort",a)}).pipe(Se(()=>!o&&!r.targetRouterState),be(()=>{this.cancelNavigationTransition(r,r.abortController.signal.reason+"",Fe.Aborted)}))),be({next:i=>{o=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new Tt(i.id,this.urlSerializer.serialize(i.extractedUrl),this.urlSerializer.serialize(i.urlAfterRedirects))),this.titleStrategy?.updateTitle(i.targetRouterState.snapshot),i.resolve(!0)},complete:()=>{o=!0}}),ns(this.transitionAbortWithErrorSubject.pipe(be(i=>{throw i}))),uo(()=>{o||this.cancelNavigationTransition(r,"",Fe.SupersededByNewNavigation),this.currentTransition?.id===r.id&&(this.currentNavigation=null,this.currentTransition=null)}),pt(i=>{if(this.destroyed)return r.resolve(!1),je;if(o=!0,Lg(i))this.events.next(new _t(r.id,this.urlSerializer.serialize(r.extractedUrl),i.message,i.cancellationCode)),mE(i)?this.events.next(new Lr(i.url,i.navigationBehaviorOptions)):r.resolve(!1);else{let s=new Fr(r.id,this.urlSerializer.serialize(r.extractedUrl),i,r.targetSnapshot??void 0);try{let a=Ge(this.environmentInjector,()=>this.navigationErrorHandler?.(s));if(a instanceof jr){let{message:c,cancellationCode:l}=Oa(this.urlSerializer,a);this.events.next(new _t(r.id,this.urlSerializer.serialize(r.extractedUrl),c,l)),this.events.next(new Lr(a.redirectTo,a.navigationBehaviorOptions))}else throw this.events.next(s),i}catch(a){this.options.resolveNavigationPromiseOnError?r.resolve(!1):r.reject(a)}}return je}))}))}cancelNavigationTransition(n,r,o){let i=new _t(n.id,this.urlSerializer.serialize(n.extractedUrl),r,o);this.events.next(i),n.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let n=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return n.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function iw(e){return e!==Xo}var Yg=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:()=>h(sw),providedIn:"root"})}return e})(),Fa=class{shouldDetach(t){return!1}store(t,n){}shouldAttach(t){return!1}retrieve(t){return null}shouldReuseRoute(t,n){return t.routeConfig===n.routeConfig}},sw=(()=>{class e extends Fa{static \u0275fac=(()=>{let n;return function(o){return(n||(n=_r(e)))(o||e)}})();static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Qg=(()=>{class e{urlSerializer=h(Un);options=h(fi,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";location=h(ut);urlHandlingStrategy=h(Va);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new St;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}createBrowserPath({finalUrl:n,initialUrl:r,targetBrowserUrl:o}){let i=n!==void 0?this.urlHandlingStrategy.merge(n,r):r,s=o??i;return s instanceof St?this.urlSerializer.serialize(s):s}commitTransition({targetRouterState:n,finalUrl:r,initialUrl:o}){r&&n?(this.currentUrlTree=r,this.rawUrlTree=this.urlHandlingStrategy.merge(r,o),this.routerState=n):this.rawUrlTree=o}routerState=Rg(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();updateStateMemento(){this.stateMemento=this.createStateMemento()}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}resetInternalState({finalUrl:n}){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,n??this.rawUrlTree)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:()=>h(aw),providedIn:"root"})}return e})(),aw=(()=>{class e extends Qg{currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}registerNonRouterCurrentEntryChangeListener(n){return this.location.subscribe(r=>{r.type==="popstate"&&setTimeout(()=>{n(r.url,r.state,"popstate")})})}handleRouterEvent(n,r){n instanceof qt?this.updateStateMemento():n instanceof Zt?this.commitTransition(r):n instanceof ni?this.urlUpdateStrategy==="eager"&&(r.extras.skipLocationChange||this.setBrowserUrl(this.createBrowserPath(r),r)):n instanceof ri?(this.commitTransition(r),this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(this.createBrowserPath(r),r)):n instanceof _t&&n.code!==Fe.SupersededByNewNavigation&&n.code!==Fe.Redirect?this.restoreHistory(r):n instanceof Fr?this.restoreHistory(r,!0):n instanceof Tt&&(this.lastSuccessfulId=n.id,this.currentPageId=this.browserPageId)}setBrowserUrl(n,{extras:r,id:o}){let{replaceUrl:i,state:s}=r;if(this.location.isCurrentPathEqualTo(n)||i){let a=this.browserPageId,c=y(y({},s),this.generateNgRouterState(o,a));this.location.replaceState(n,"",c)}else{let a=y(y({},s),this.generateNgRouterState(o,this.browserPageId+1));this.location.go(n,"",a)}}restoreHistory(n,r=!1){if(this.canceledNavigationResolution==="computed"){let o=this.browserPageId,i=this.currentPageId-o;i!==0?this.location.historyGo(i):this.getCurrentUrlTree()===n.finalUrl&&i===0&&(this.resetInternalState(n),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetInternalState(n),this.resetUrlToCurrentUrlTree())}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.getRawUrlTree()),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(n,r){return this.canceledNavigationResolution==="computed"?{navigationId:n,\u0275routerPageId:r}:{navigationId:n}}static \u0275fac=(()=>{let n;return function(o){return(n||(n=_r(e)))(o||e)}})();static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Md(e,t){e.events.pipe(Se(n=>n instanceof Tt||n instanceof _t||n instanceof Fr||n instanceof Zt),L(n=>n instanceof Tt||n instanceof Zt?0:(n instanceof _t?n.code===Fe.Redirect||n.code===Fe.SupersededByNewNavigation:!1)?2:1),Se(n=>n!==2),Ot(1)).subscribe(()=>{t()})}var cw={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},lw={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},tt=(()=>{class e{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=h(Lu);stateManager=h(Qg);options=h(fi,{optional:!0})||{};pendingTasks=h(dn);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=h(Zg);urlSerializer=h(Un);location=h(ut);urlHandlingStrategy=h(Va);injector=h(ee);_events=new te;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=h(Yg);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=h(pi,{optional:!0})?.flat()??[];componentInputBindingEnabled=!!h(La,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this).subscribe({error:n=>{this.console.warn(n)}}),this.subscribeToNavigationEvents()}eventsSubscription=new fe;subscribeToNavigationEvents(){let n=this.navigationTransitions.events.subscribe(r=>{try{let o=this.navigationTransitions.currentTransition,i=this.navigationTransitions.currentNavigation;if(o!==null&&i!==null){if(this.stateManager.handleRouterEvent(r,i),r instanceof _t&&r.code!==Fe.Redirect&&r.code!==Fe.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof Tt)this.navigated=!0;else if(r instanceof Lr){let s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,o.currentRawUrl),c=y({browserUrl:o.extras.browserUrl,info:o.extras.info,skipLocationChange:o.extras.skipLocationChange,replaceUrl:o.extras.replaceUrl||this.urlUpdateStrategy==="eager"||iw(o.source)},s);this.scheduleNavigation(a,Xo,null,c,{resolve:o.resolve,reject:o.reject,promise:o.promise})}}cE(r)&&this._events.next(r)}catch(o){this.navigationTransitions.transitionAbortWithErrorSubject.next(o)}});this.eventsSubscription.add(n)}resetRootComponentType(n){this.routerState.root.component=n,this.navigationTransitions.rootComponentType=n}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),Xo,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((n,r,o)=>{this.navigateToSyncWithBrowser(n,o,r)})}navigateToSyncWithBrowser(n,r,o){let i={replaceUrl:!0},s=o?.navigationId?o:null;if(o){let c=y({},o);delete c.navigationId,delete c.\u0275routerPageId,Object.keys(c).length!==0&&(i.state=c)}let a=this.parseUrl(n);this.scheduleNavigation(a,r,s,i).catch(c=>{this.injector.get(Ct)(c)})}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(n){this.config=n.map(_d),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(n,r={}){let{relativeTo:o,queryParams:i,fragment:s,queryParamsHandling:a,preserveFragment:c}=r,l=c?this.currentUrlTree.fragment:s,u=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":u=y(y({},this.currentUrlTree.queryParams),i);break;case"preserve":u=this.currentUrlTree.queryParams;break;default:u=i||null}u!==null&&(u=this.removeEmptyProps(u));let f;try{let p=o?o.snapshot:this.routerState.snapshot.root;f=Tg(p)}catch{(typeof n[0]!="string"||n[0][0]!=="/")&&(n=[]),f=this.currentUrlTree.root}return Mg(f,n,u,l??null)}navigateByUrl(n,r={skipLocationChange:!1}){let o=Pr(n)?n:this.parseUrl(n),i=this.urlHandlingStrategy.merge(o,this.rawUrlTree);return this.scheduleNavigation(i,Xo,null,r)}navigate(n,r={skipLocationChange:!1}){return uw(n),this.navigateByUrl(this.createUrlTree(n,r),r)}serializeUrl(n){return this.urlSerializer.serialize(n)}parseUrl(n){try{return this.urlSerializer.parse(n)}catch{return this.urlSerializer.parse("/")}}isActive(n,r){let o;if(r===!0?o=y({},cw):r===!1?o=y({},lw):o=r,Pr(n))return cg(this.currentUrlTree,n,o);let i=this.parseUrl(n);return cg(this.currentUrlTree,i,o)}removeEmptyProps(n){return Object.entries(n).reduce((r,[o,i])=>(i!=null&&(r[o]=i),r),{})}scheduleNavigation(n,r,o,i,s){if(this.disposed)return Promise.resolve(!1);let a,c,l;s?(a=s.resolve,c=s.reject,l=s.promise):l=new Promise((f,p)=>{a=f,c=p});let u=this.pendingTasks.add();return Md(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(u))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:o,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:n,extras:i,resolve:a,reject:c,promise:l,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),l.catch(f=>Promise.reject(f))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function uw(e){for(let t=0;t<e.length;t++)if(e[t]==null)throw new E(4008,!1)}var hi=class{},hw=(()=>{class e{preload(n,r){return r().pipe(pt(()=>S(null)))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Kg=(()=>{class e{router;injector;preloadingStrategy;loader;subscription;constructor(n,r,o,i){this.router=n,this.injector=r,this.preloadingStrategy=o,this.loader=i}setUpPreloading(){this.subscription=this.router.events.pipe(Se(n=>n instanceof Tt),tn(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(n,r){let o=[];for(let i of r){i.providers&&!i._injector&&(i._injector=xr(i.providers,n,`Route: ${i.path}`));let s=i._injector??n,a=i._loadedInjector??s;(i.loadChildren&&!i._loadedRoutes&&i.canLoad===void 0||i.loadComponent&&!i._loadedComponent)&&o.push(this.preloadConfig(s,i)),(i.children||i._loadedRoutes)&&o.push(this.processRoutes(a,i.children??i._loadedRoutes))}return ie(o).pipe(sr())}preloadConfig(n,r){return this.preloadingStrategy.preload(r,()=>{let o;r.loadChildren&&r.canLoad===void 0?o=this.loader.loadChildren(n,r):o=S(null);let i=o.pipe(oe(s=>s===null?S(void 0):(r._loadedRoutes=s.routes,r._loadedInjector=s.injector,this.processRoutes(s.injector??n,s.routes))));if(r.loadComponent&&!r._loadedComponent){let s=this.loader.loadComponent(r);return ie([i,s]).pipe(sr())}else return i})}static \u0275fac=function(r){return new(r||e)(I(tt),I(ee),I(hi),I(Td))};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),mw=new w("");function gw(e,...t){return fr([{provide:pi,multi:!0,useValue:e},[],{provide:Le,useFactory:vw,deps:[tt]},{provide:ta,multi:!0,useFactory:Dw},t.map(n=>n.\u0275providers)])}function vw(e){return e.routerState.root}function yw(e,t){return{\u0275kind:e,\u0275providers:t}}function Dw(){let e=h(Ee);return t=>{let n=e.get(Gt);if(t!==n.components[0])return;let r=e.get(tt),o=e.get(Cw);e.get(bw)===1&&r.initialNavigation(),e.get(Xg,null,{optional:!0})?.setUpPreloading(),e.get(mw,null,{optional:!0})?.init(),r.resetRootComponentType(n.componentTypes[0]),o.closed||(o.next(),o.complete(),o.unsubscribe())}}var Cw=new w("",{factory:()=>new te}),bw=new w("",{providedIn:"root",factory:()=>1});var Xg=new w("");function Iw(e){return yw(0,[{provide:Xg,useExisting:Kg},{provide:hi,useExisting:e}])}var Ew={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},kH=U(y({},Ew),{"[class.ng-submitted]":"isSubmitted"});var ww=new w("",{providedIn:"root",factory:()=>tv}),tv="always";var _w=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=Ut({type:e});static \u0275inj=mt({})}return e})();var OH=(()=>{class e{static withConfig(n){return{ngModule:e,providers:[{provide:ww,useValue:n.callSetDisabledState??tv}]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=Ut({type:e});static \u0275inj=mt({imports:[_w]})}return e})();var zn=(e,t,n,r,o)=>Tw(e[1],t[1],n[1],r[1],o).map(i=>Sw(e[0],t[0],n[0],r[0],i)),Sw=(e,t,n,r,o)=>{let i=3*t*Math.pow(o-1,2),s=-3*n*o+3*n+r*o,a=e*Math.pow(o-1,3);return o*(i+o*s)-a},Tw=(e,t,n,r,o)=>(e-=o,t-=o,n-=o,r-=o,xw(r-3*n+3*t-e,3*n-6*t+3*e,3*t-3*e,e).filter(s=>s>=0&&s<=1)),Mw=(e,t,n)=>{let r=t*t-4*e*n;return r<0?[]:[(-t+Math.sqrt(r))/(2*e),(-t-Math.sqrt(r))/(2*e)]},xw=(e,t,n,r)=>{if(e===0)return Mw(t,n,r);t/=e,n/=e,r/=e;let o=(3*n-t*t)/3,i=(2*t*t*t-9*t*n+27*r)/27;if(o===0)return[Math.pow(-i,.3333333333333333)];if(i===0)return[Math.sqrt(-o),-Math.sqrt(-o)];let s=Math.pow(i/2,2)+Math.pow(o/3,3);if(s===0)return[Math.pow(i/2,.5)-t/3];if(s>0)return[Math.pow(-(i/2)+Math.sqrt(s),.3333333333333333)-Math.pow(i/2+Math.sqrt(s),.3333333333333333)-t/3];let a=Math.sqrt(Math.pow(-(o/3),3)),c=Math.acos(-(i/(2*Math.sqrt(Math.pow(-(o/3),3))))),l=2*Math.pow(a,1/3);return[l*Math.cos(c/3)-t/3,l*Math.cos((c+2*Math.PI)/3)-t/3,l*Math.cos((c+4*Math.PI)/3)-t/3]};var Ba=e=>rv(e),Te=(e,t)=>(typeof e=="string"&&(t=e,e=void 0),Ba(e).includes(t)),rv=(e=window)=>{if(typeof e>"u")return[];e.Ionic=e.Ionic||{};let t=e.Ionic.platforms;return t==null&&(t=e.Ionic.platforms=Aw(e),t.forEach(n=>e.document.documentElement.classList.add(`plt-${n}`))),t},Aw=e=>{let t=X.get("platform");return Object.keys(nv).filter(n=>{let r=t?.[n];return typeof r=="function"?r(e):nv[n](e)})},Rw=e=>Ha(e)&&!iv(e),xd=e=>!!(Gn(e,/iPad/i)||Gn(e,/Macintosh/i)&&Ha(e)),Nw=e=>Gn(e,/iPhone/i),kw=e=>Gn(e,/iPhone|iPod/i)||xd(e),ov=e=>Gn(e,/android|sink/i),Ow=e=>ov(e)&&!Gn(e,/mobile/i),Pw=e=>{let t=e.innerWidth,n=e.innerHeight,r=Math.min(t,n),o=Math.max(t,n);return r>390&&r<520&&o>620&&o<800},Fw=e=>{let t=e.innerWidth,n=e.innerHeight,r=Math.min(t,n),o=Math.max(t,n);return xd(e)||Ow(e)||r>460&&r<820&&o>780&&o<1400},Ha=e=>Bw(e,"(any-pointer:coarse)"),Lw=e=>!Ha(e),iv=e=>sv(e)||av(e),sv=e=>!!(e.cordova||e.phonegap||e.PhoneGap),av=e=>{let t=e.Capacitor;return!!(t?.isNative||t?.isNativePlatform&&t.isNativePlatform())},jw=e=>Gn(e,/electron/i),Vw=e=>{var t;return!!(!((t=e.matchMedia)===null||t===void 0)&&t.call(e,"(display-mode: standalone)").matches||e.navigator.standalone)},Gn=(e,t)=>t.test(e.navigator.userAgent),Bw=(e,t)=>{var n;return(n=e.matchMedia)===null||n===void 0?void 0:n.call(e,t).matches},nv={ipad:xd,iphone:Nw,ios:kw,android:ov,phablet:Pw,tablet:Fw,cordova:sv,capacitor:av,electron:jw,pwa:Vw,mobile:Ha,mobileweb:Rw,desktop:Lw,hybrid:iv},Hr,J=e=>e&&Jd(e)||Hr,Ad=(e={})=>{if(typeof window>"u")return;let t=window.document,n=window,r=n.Ionic=n.Ionic||{},o=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},ef(n)),{persistConfig:!1}),r.config),nf(n)),e);X.reset(o),X.getBoolean("persistConfig")&&tf(n,o),rv(n),r.config=X,r.mode=Hr=X.get("mode",t.documentElement.getAttribute("mode")||(Te(n,"ios")?"ios":"md")),X.set("mode",Hr),t.documentElement.setAttribute("mode",Hr),t.documentElement.classList.add(Hr),X.getBoolean("_testing")&&X.set("animated",!1);let i=a=>{var c;return(c=a.tagName)===null||c===void 0?void 0:c.startsWith("ION-")},s=a=>["ios","md"].includes(a);Xd(a=>{for(;a;){let c=a.mode||a.getAttribute("mode");if(c){if(s(c))return c;i(a)&&Qe('Invalid ionic mode: "'+c+'", expected: "ios" or "md"')}a=a.parentElement}return Hr})};var $r=(e,t)=>t.closest(e)!==null,Ur=(e,t)=>typeof e=="string"&&e.length>0?Object.assign({"ion-color":!0,[`ion-color-${e}`]:!0},t):t,Hw=e=>e!==void 0?(Array.isArray(e)?e:e.split(" ")).filter(n=>n!=null).map(n=>n.trim()).filter(n=>n!==""):[],$a=e=>{let t={};return Hw(e).forEach(n=>t[n]=!0),t};var zr=(e,t,n,r,o,i)=>b(null,null,function*(){var s;if(e)return e.attachViewToDom(t,n,o,r);if(!i&&typeof n!="string"&&!(n instanceof HTMLElement))throw new Error("framework delegate is missing");let a=typeof n=="string"?(s=t.ownerDocument)===null||s===void 0?void 0:s.createElement(n):n;return r&&r.forEach(c=>a.classList.add(c)),o&&Object.assign(a,o),t.appendChild(a),yield new Promise(c=>rt(a,c)),a}),Gr=(e,t)=>{if(t){if(e){let n=t.parentElement;return e.removeViewFromDom(n,t)}t.remove()}return Promise.resolve()},Ua=()=>{let e,t;return{attachViewToDom:(c,l,...u)=>b(null,[c,l,...u],function*(o,i,s={},a=[]){var f,p;e=o;let d;if(i){let g=typeof i=="string"?(f=e.ownerDocument)===null||f===void 0?void 0:f.createElement(i):i;a.forEach(v=>g.classList.add(v)),Object.assign(g,s),e.appendChild(g),d=g,yield new Promise(v=>rt(g,v))}else if(e.children.length>0&&(e.tagName==="ION-MODAL"||e.tagName==="ION-POPOVER")&&!(d=e.children[0]).classList.contains("ion-delegate-host")){let v=(p=e.ownerDocument)===null||p===void 0?void 0:p.createElement("div");v.classList.add("ion-delegate-host"),a.forEach(D=>v.classList.add(D)),v.append(...e.children),e.appendChild(v),d=v}let m=document.querySelector("ion-app")||document.body;return t=document.createComment("ionic teleport"),e.parentNode.insertBefore(t,e),m.appendChild(e),d??e}),removeViewFromDom:()=>(e&&t&&(t.parentNode.insertBefore(e,t),t.remove()),Promise.resolve())}};var gi='[tabindex]:not([tabindex^="-"]):not([hidden]):not([disabled]), input:not([type=hidden]):not([tabindex^="-"]):not([hidden]):not([disabled]), textarea:not([tabindex^="-"]):not([hidden]):not([disabled]), button:not([tabindex^="-"]):not([hidden]):not([disabled]), select:not([tabindex^="-"]):not([hidden]):not([disabled]), ion-checkbox:not([tabindex^="-"]):not([hidden]):not([disabled]), ion-radio:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable[disabled="false"]:not([tabindex^="-"]):not([hidden])',za=(e,t)=>{let n=e.querySelector(gi);uv(n,t??e)},cv=(e,t)=>{let n=Array.from(e.querySelectorAll(gi)),r=n.length>0?n[n.length-1]:null;uv(r,t??e)},uv=(e,t)=>{let n=e,r=e?.shadowRoot;if(r&&(n=r.querySelector(gi)||e),n){let o=n.closest("ion-radio-group");o?o.setFocus():lc(n)}else t.focus()},Rd=0,$w=0,Ga=new WeakMap,dv=e=>({create(n){return Uw(e,n)},dismiss(n,r,o){return qw(document,n,r,e,o)},getTop(){return b(this,null,function*(){return mi(document,e)})}});var Nd=dv("ion-modal");var kd=dv("ion-popover");var Wa=e=>{typeof document<"u"&&Ww(document);let t=Rd++;e.overlayIndex=t},qa=e=>(e.hasAttribute("id")||(e.id=`ion-overlay-${++$w}`),e.id),Uw=(e,t)=>typeof window<"u"&&typeof window.customElements<"u"?window.customElements.whenDefined(e).then(()=>{let n=document.createElement(e);return n.classList.add("overlay-hidden"),Object.assign(n,Object.assign(Object.assign({},t),{hasController:!0})),pv(document).appendChild(n),new Promise(r=>rt(n,r))}):Promise.resolve(),zw=e=>e.classList.contains("overlay-hidden"),lv=(e,t)=>{let n=e,r=e?.shadowRoot;r&&(n=r.querySelector(gi)||e),n?lc(n):t.focus()},Gw=(e,t)=>{let n=mi(t,"ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover"),r=e.target;if(!n||!r||n.classList.contains(Wn))return;let o=()=>{if(n===r)n.lastFocus=void 0;else if(r.tagName==="ION-TOAST")lv(n.lastFocus,n);else{let s=de(n);if(!s.contains(r))return;let a=s.querySelector(".ion-overlay-wrapper");if(!a)return;if(a.contains(r)||r===s.querySelector("ion-backdrop"))n.lastFocus=r;else{let c=n.lastFocus;za(a,n),c===t.activeElement&&cv(a,n),n.lastFocus=t.activeElement}}},i=()=>{if(n.contains(r))n.lastFocus=r;else if(r.tagName==="ION-TOAST")lv(n.lastFocus,n);else{let s=n.lastFocus;za(n),s===t.activeElement&&cv(n),n.lastFocus=t.activeElement}};n.shadowRoot?i():o()},Ww=e=>{Rd===0&&(Rd=1,e.addEventListener("focus",t=>{Gw(t,e)},!0),e.addEventListener("ionBackButton",t=>{let n=mi(e);n?.backdropDismiss&&t.detail.register(cf,()=>{n.dismiss(void 0,Wr)})}),ro()||e.addEventListener("keydown",t=>{if(t.key==="Escape"){let n=mi(e);n?.backdropDismiss&&n.dismiss(void 0,Wr)}}))},qw=(e,t,n,r,o)=>{let i=mi(e,r,o);return i?i.dismiss(t,n):Promise.reject("overlay does not exist")},Zw=(e,t)=>(t===void 0&&(t="ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover,ion-toast"),Array.from(e.querySelectorAll(t)).filter(n=>n.overlayIndex>0)),Za=(e,t)=>Zw(e,t).filter(n=>!zw(n)),mi=(e,t,n)=>{let r=Za(e,t);return n===void 0?r[r.length-1]:r.find(o=>o.id===n)},fv=(e=!1)=>{let n=pv(document).querySelector("ion-router-outlet, ion-nav, #ion-view-container-root");n&&(e?n.setAttribute("aria-hidden","true"):n.removeAttribute("aria-hidden"))},Ya=(e,t,n,r,o)=>b(null,null,function*(){var i,s;if(e.presented)return;e.el.tagName!=="ION-TOAST"&&(fv(!0),document.body.classList.add(mc)),Kw(e.el),vv(e.el),e.presented=!0,e.willPresent.emit(),(i=e.willPresentShorthand)===null||i===void 0||i.emit();let a=J(e),c=e.enterAnimation?e.enterAnimation:X.get(t,a==="ios"?n:r);(yield hv(e,c,e.el,o))&&(e.didPresent.emit(),(s=e.didPresentShorthand)===null||s===void 0||s.emit()),e.el.tagName!=="ION-TOAST"&&Yw(e.el),e.keyboardClose&&(document.activeElement===null||!e.el.contains(document.activeElement))&&e.el.focus(),e.el.removeAttribute("aria-hidden")}),Yw=e=>b(null,null,function*(){let t=document.activeElement;if(!t)return;let n=t?.shadowRoot;n&&(t=n.querySelector(gi)||t),yield e.onDidDismiss(),(document.activeElement===null||document.activeElement===document.body)&&t.focus()}),Qa=(e,t,n,r,o,i,s)=>b(null,null,function*(){var a,c;if(!e.presented)return!1;let u=(Xt!==void 0?Za(Xt):[]).filter(p=>p.tagName!=="ION-TOAST");u.length===1&&u[0].id===e.el.id&&(fv(!1),document.body.classList.remove(mc)),e.presented=!1;try{vv(e.el),e.el.style.setProperty("pointer-events","none"),e.willDismiss.emit({data:t,role:n}),(a=e.willDismissShorthand)===null||a===void 0||a.emit({data:t,role:n});let p=J(e),d=e.leaveAnimation?e.leaveAnimation:X.get(r,p==="ios"?o:i);n!==Zr&&(yield hv(e,d,e.el,s)),e.didDismiss.emit({data:t,role:n}),(c=e.didDismissShorthand)===null||c===void 0||c.emit({data:t,role:n}),(Ga.get(e)||[]).forEach(g=>g.destroy()),Ga.delete(e),e.el.classList.add("overlay-hidden"),e.el.style.removeProperty("pointer-events"),e.el.lastFocus!==void 0&&(e.el.lastFocus=void 0)}catch(p){Ei(`[${e.el.tagName.toLowerCase()}] - `,p)}return e.el.remove(),Xw(),!0}),pv=e=>e.querySelector("ion-app")||e.body,hv=(e,t,n,r)=>b(null,null,function*(){n.classList.remove("overlay-hidden");let o=e.el,i=t(o,r);(!e.animated||!X.getBoolean("animated",!0))&&i.duration(0),e.keyboardClose&&i.beforeAddWrite(()=>{let a=n.ownerDocument.activeElement;a?.matches("input,ion-input, ion-textarea")&&a.blur()});let s=Ga.get(e)||[];return Ga.set(e,[...s,i]),yield i.play(),!0}),qr=(e,t)=>{let n,r=new Promise(o=>n=o);return Qw(e,t,o=>{n(o.detail)}),r},Qw=(e,t,n)=>{let r=o=>{of(e,t,r),n(o)};_i(e,t,r)};var Wr="backdrop",Zr="gesture",mv=39;var gv=()=>{let e,t=()=>{e&&(e(),e=void 0)};return{addClickListener:(r,o)=>{t();let i=o!==void 0?document.getElementById(o):null;if(!i){Qe(`[${r.tagName.toLowerCase()}] - A trigger element with the ID "${o}" was not found in the DOM. The trigger element must be in the DOM when the "trigger" property is set on an overlay component.`,r);return}e=((a,c)=>{let l=()=>{c.present()};return a.addEventListener("click",l),()=>{a.removeEventListener("click",l)}})(i,r)},removeClickListener:t}},vv=e=>{Xt!==void 0&&Te("android")&&e.setAttribute("aria-hidden","true")},Kw=e=>{var t;if(Xt===void 0)return;let n=Za(Xt);for(let r=n.length-1;r>=0;r--){let o=n[r],i=(t=n[r+1])!==null&&t!==void 0?t:e;(i.hasAttribute("aria-hidden")||i.tagName!=="ION-TOAST")&&o.setAttribute("aria-hidden","true")}},Xw=()=>{if(Xt===void 0)return;let e=Za(Xt);for(let t=e.length-1;t>=0;t--){let n=e[t];if(n.removeAttribute("aria-hidden"),n.tagName!=="ION-TOAST")break}},Wn="ion-disable-focus-trap";var bv=(()=>{class e{doc;_readyPromise;win;backButton=new te;keyboardDidShow=new te;keyboardDidHide=new te;pause=new te;resume=new te;resize=new te;constructor(n,r){this.doc=n,r.run(()=>{this.win=n.defaultView,this.backButton.subscribeWithPriority=function(i,s){return this.subscribe(a=>a.register(i,c=>r.run(()=>s(c))))},Yr(this.pause,n,"pause",r),Yr(this.resume,n,"resume",r),Yr(this.backButton,n,"ionBackButton",r),Yr(this.resize,this.win,"resize",r),Yr(this.keyboardDidShow,this.win,"ionKeyboardDidShow",r),Yr(this.keyboardDidHide,this.win,"ionKeyboardDidHide",r);let o;this._readyPromise=new Promise(i=>{o=i}),this.win?.cordova?n.addEventListener("deviceready",()=>{o("cordova")},{once:!0}):o("dom")})}is(n){return Te(this.win,n)}platforms(){return Ba(this.win)}ready(){return this._readyPromise}get isRTL(){return this.doc.dir==="rtl"}getQueryParam(n){return o_(this.win.location.href,n)}isLandscape(){return!this.isPortrait()}isPortrait(){return this.win.matchMedia?.("(orientation: portrait)").matches}testUserAgent(n){let r=this.win.navigator;return!!(r?.userAgent&&r.userAgent.indexOf(n)>=0)}url(){return this.win.location.href}width(){return this.win.innerWidth}height(){return this.win.innerHeight}static \u0275fac=function(r){return new(r||e)(I(ae),I(q))};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),o_=(e,t)=>{t=t.replace(/[[\]\\]/g,"\\$&");let r=new RegExp("[\\?&]"+t+"=([^&#]*)").exec(e);return r?decodeURIComponent(r[1].replace(/\+/g," ")):null},Yr=(e,t,n,r)=>{t&&t.addEventListener(n,o=>{r.run(()=>{let i=o?.detail;e.next(i)})})},Iv=(()=>{class e{location;serializer;router;topOutlet;direction=yv;animated=Dv;animationBuilder;guessDirection="forward";guessAnimation;lastNavId=-1;constructor(n,r,o,i){this.location=r,this.serializer=o,this.router=i,i&&i.events.subscribe(s=>{if(s instanceof qt){let a=s.restoredState?s.restoredState.navigationId:s.id;this.guessDirection=this.guessAnimation=a<this.lastNavId?"back":"forward",this.lastNavId=this.guessDirection==="forward"?s.id:a}}),n.backButton.subscribeWithPriority(0,s=>{this.pop(),s()})}navigateForward(n,r={}){return this.setDirection("forward",r.animated,r.animationDirection,r.animation),this.navigate(n,r)}navigateBack(n,r={}){return this.setDirection("back",r.animated,r.animationDirection,r.animation),this.navigate(n,r)}navigateRoot(n,r={}){return this.setDirection("root",r.animated,r.animationDirection,r.animation),this.navigate(n,r)}back(n={animated:!0,animationDirection:"back"}){return this.setDirection("back",n.animated,n.animationDirection,n.animation),this.location.back()}pop(){return b(this,null,function*(){let n=this.topOutlet;for(;n;){if(yield n.pop())return!0;n=n.parentOutlet}return!1})}setDirection(n,r,o,i){this.direction=n,this.animated=i_(n,r,o),this.animationBuilder=i}setTopOutlet(n){this.topOutlet=n}consumeTransition(){let n="root",r,o=this.animationBuilder;return this.direction==="auto"?(n=this.guessDirection,r=this.guessAnimation):(r=this.animated,n=this.direction),this.direction=yv,this.animated=Dv,this.animationBuilder=void 0,{direction:n,animation:r,animationBuilder:o}}navigate(n,r){if(Array.isArray(n))return this.router.navigate(n,r);{let o=this.serializer.parse(n.toString());return r.queryParams!==void 0&&(o.queryParams=y({},r.queryParams)),r.fragment!==void 0&&(o.fragment=r.fragment),this.router.navigateByUrl(o,r)}}static \u0275fac=function(r){return new(r||e)(I(bv),I(ut),I(Un),I(tt,8))};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),i_=(e,t,n)=>{if(t!==!1){if(n!==void 0)return n;if(e==="forward"||e==="back")return e;if(e==="root"&&t===!0)return"forward"}},yv="auto",Dv=void 0,Ev=(()=>{class e{get(n,r){let o=Od();return o?o.get(n,r):null}getBoolean(n,r){let o=Od();return o?o.getBoolean(n,r):!1}getNumber(n,r){let o=Od();return o?o.getNumber(n,r):0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Ka=new w("USERCONFIG"),Od=()=>{if(typeof window<"u"){let e=window.Ionic;if(e?.config)return e.config}return null},vi=class{data;constructor(t={}){this.data=t,console.warn("[Ionic Warning]: NavParams has been deprecated in favor of using Angular's input API. Developers should migrate to either the @Input decorator or the Signals-based input API.")}get(t){return this.data[t]}},Xa=(()=>{class e{zone=h(q);applicationRef=h(Gt);config=h(Ka);create(n,r,o){return new Pd(n,r,this.applicationRef,this.zone,o,this.config.useSetInputAPI??!1)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:e.\u0275fac})}return e})(),Pd=class{environmentInjector;injector;applicationRef;zone;elementReferenceKey;enableSignalsSupport;elRefMap=new WeakMap;elEventsMap=new WeakMap;constructor(t,n,r,o,i,s){this.environmentInjector=t,this.injector=n,this.applicationRef=r,this.zone=o,this.elementReferenceKey=i,this.enableSignalsSupport=s}attachViewToDom(t,n,r,o){return this.zone.run(()=>new Promise(i=>{let s=y({},r);this.elementReferenceKey!==void 0&&(s[this.elementReferenceKey]=t);let a=s_(this.zone,this.environmentInjector,this.injector,this.applicationRef,this.elRefMap,this.elEventsMap,t,n,s,o,this.elementReferenceKey,this.enableSignalsSupport);i(a)}))}removeViewFromDom(t,n){return this.zone.run(()=>new Promise(r=>{let o=this.elRefMap.get(n);if(o){o.destroy(),this.elRefMap.delete(n);let i=this.elEventsMap.get(n);i&&(i(),this.elEventsMap.delete(n))}r()}))}},s_=(e,t,n,r,o,i,s,a,c,l,u,f)=>{let p=Ee.create({providers:c_(c),parent:n}),d=Hm(a,{environmentInjector:t,elementInjector:p}),m=d.instance,g=d.location.nativeElement;if(c)if(u&&m[u]!==void 0&&console.error(`[Ionic Error]: ${u} is a reserved property when using ${s.tagName.toLowerCase()}. Rename or remove the "${u}" property from ${a.name}.`),f===!0&&d.setInput!==void 0){let D=c,{modal:Z,popover:N}=D,V=Kd(D,["modal","popover"]);for(let Y in V)d.setInput(Y,V[Y]);Z!==void 0&&Object.assign(m,{modal:Z}),N!==void 0&&Object.assign(m,{popover:N})}else Object.assign(m,c);if(l)for(let Z of l)g.classList.add(Z);let v=wv(e,m,g);return s.appendChild(g),r.attachView(d.hostView),o.set(g,d),i.set(g,v),g},a_=[uc,dc,fc,pc,hc],wv=(e,t,n)=>e.run(()=>{let r=a_.filter(o=>typeof t[o]=="function").map(o=>{let i=s=>t[o](s.detail);return n.addEventListener(o,i),()=>n.removeEventListener(o,i)});return()=>r.forEach(o=>o())}),Cv=new w("NavParamsToken"),c_=e=>[{provide:Cv,useValue:e},{provide:vi,useFactory:l_,deps:[Cv]}],l_=e=>new vi(e),u_=(e,t)=>{let n=e.prototype;t.forEach(r=>{Object.defineProperty(n,r,{get(){return this.el[r]},set(o){this.z.runOutsideAngular(()=>this.el[r]=o)}})})},d_=(e,t)=>{let n=e.prototype;t.forEach(r=>{n[r]=function(){let o=arguments;return this.z.runOutsideAngular(()=>this.el[r].apply(this.el,o))}})};function _v(e){return function(n){let{defineCustomElementFn:r,inputs:o,methods:i}=e;return r!==void 0&&r(),o&&u_(n,o),i&&d_(n,i),n}}var f_=(e,t,n)=>n==="root"?Sv(e,t):n==="forward"?p_(e,t):h_(e,t),Sv=(e,t)=>(e=e.filter(n=>n.stackId!==t.stackId),e.push(t),e),p_=(e,t)=>(e.indexOf(t)>=0?e=e.filter(r=>r.stackId!==t.stackId||r.id<=t.id):e.push(t),e),h_=(e,t)=>e.indexOf(t)>=0?e.filter(r=>r.stackId!==t.stackId||r.id<=t.id):Sv(e,t),Fd=(e,t)=>{let n=e.createUrlTree(["."],{relativeTo:t});return e.serializeUrl(n)},Tv=(e,t)=>t?e.stackId!==t.stackId:!0,m_=(e,t)=>{if(!e)return;let n=Mv(t);for(let r=0;r<n.length;r++){if(r>=e.length)return n[r];if(n[r]!==e[r])return}},Mv=e=>e.split("/").map(t=>t.trim()).filter(t=>t!==""),xv=e=>{e&&(e.ref.destroy(),e.unlistenEvents())},Ld=class{containerEl;router;navCtrl;zone;location;views=[];runningTask;skipTransition=!1;tabsPrefix;activeView;nextId=0;constructor(t,n,r,o,i,s){this.containerEl=n,this.router=r,this.navCtrl=o,this.zone=i,this.location=s,this.tabsPrefix=t!==void 0?Mv(t):void 0}createView(t,n){let r=Fd(this.router,n),o=t?.location?.nativeElement,i=wv(this.zone,t.instance,o);return{id:this.nextId++,stackId:m_(this.tabsPrefix,r),unlistenEvents:i,element:o,ref:t,url:r}}getExistingView(t){let n=Fd(this.router,t),r=this.views.find(o=>o.url===n);return r&&r.ref.changeDetectorRef.reattach(),r}setActive(t){let n=this.navCtrl.consumeTransition(),{direction:r,animation:o,animationBuilder:i}=n,s=this.activeView,a=Tv(t,s);a&&(r="back",o=void 0);let c=this.views.slice(),l,u=this.router;u.getCurrentNavigation?l=u.getCurrentNavigation():u.navigations?.value&&(l=u.navigations.value),l?.extras?.replaceUrl&&this.views.length>0&&this.views.splice(-1,1);let f=this.views.includes(t),p=this.insertView(t,r);f||t.ref.changeDetectorRef.detectChanges();let d=t.animationBuilder;return i===void 0&&r==="back"&&!a&&d!==void 0&&(i=d),s&&(s.animationBuilder=i),this.zone.runOutsideAngular(()=>this.wait(()=>(s&&s.ref.changeDetectorRef.detach(),t.ref.changeDetectorRef.reattach(),this.transition(t,s,o,this.canGoBack(1),!1,i).then(()=>g_(t,p,c,this.location,this.zone)).then(()=>({enteringView:t,direction:r,animation:o,tabSwitch:a})))))}canGoBack(t,n=this.getActiveStackId()){return this.getStack(n).length>t}pop(t,n=this.getActiveStackId()){return this.zone.run(()=>{let r=this.getStack(n);if(r.length<=t)return Promise.resolve(!1);let o=r[r.length-t-1],i=o.url,s=o.savedData;if(s){let c=s.get("primary");c?.route?._routerState?.snapshot.url&&(i=c.route._routerState.snapshot.url)}let{animationBuilder:a}=this.navCtrl.consumeTransition();return this.navCtrl.navigateBack(i,U(y({},o.savedExtras),{animation:a})).then(()=>!0)})}startBackTransition(){let t=this.activeView;if(t){let n=this.getStack(t.stackId),r=n[n.length-2],o=r.animationBuilder;return this.wait(()=>this.transition(r,t,"back",this.canGoBack(2),!0,o))}return Promise.resolve()}endBackTransition(t){t?(this.skipTransition=!0,this.pop(1)):this.activeView&&Av(this.activeView,this.views,this.views,this.location,this.zone)}getLastUrl(t){let n=this.getStack(t);return n.length>0?n[n.length-1]:void 0}getRootUrl(t){let n=this.getStack(t);return n.length>0?n[0]:void 0}getActiveStackId(){return this.activeView?this.activeView.stackId:void 0}getActiveView(){return this.activeView}hasRunningTask(){return this.runningTask!==void 0}destroy(){this.containerEl=void 0,this.views.forEach(xv),this.activeView=void 0,this.views=[]}getStack(t){return this.views.filter(n=>n.stackId===t)}insertView(t,n){return this.activeView=t,this.views=f_(this.views,t,n),this.views.slice()}transition(t,n,r,o,i,s){if(this.skipTransition)return this.skipTransition=!1,Promise.resolve(!1);if(n===t)return Promise.resolve(!1);let a=t?t.element:void 0,c=n?n.element:void 0,l=this.containerEl;return a&&a!==c&&(a.classList.add("ion-page"),a.classList.add("ion-page-invisible"),l.commit)?l.commit(a,c,{duration:r===void 0?0:void 0,direction:r,showGoBack:o,progressAnimation:i,animationBuilder:s}):Promise.resolve(!1)}wait(t){return b(this,null,function*(){this.runningTask!==void 0&&(yield this.runningTask,this.runningTask=void 0);let n=this.runningTask=t();return n.finally(()=>this.runningTask=void 0),n})}},g_=(e,t,n,r,o)=>typeof requestAnimationFrame=="function"?new Promise(i=>{requestAnimationFrame(()=>{Av(e,t,n,r,o),i()})}):Promise.resolve(),Av=(e,t,n,r,o)=>{o.run(()=>n.filter(i=>!t.includes(i)).forEach(xv)),t.forEach(i=>{let a=r.path().split("?")[0].split("#")[0];if(i!==e&&i.url!==a){let c=i.element;c.setAttribute("aria-hidden","true"),c.classList.add("ion-page-hidden"),i.ref.changeDetectorRef.detach()}})},Rv=(()=>{class e{parentOutlet;nativeEl;activatedView=null;tabsPrefix;_swipeGesture;stackCtrl;proxyMap=new WeakMap;currentActivatedRoute$=new pe(null);activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=M;stackWillChange=new ce;stackDidChange=new ce;activateEvents=new ce;deactivateEvents=new ce;parentContexts=h(Qt);location=h(lt);environmentInjector=h(ee);inputBinder=h(Nv,{optional:!0});supportsBindingToComponentInputs=!0;config=h(Ev);navCtrl=h(Iv);set animation(n){this.nativeEl.animation=n}set animated(n){this.nativeEl.animated=n}set swipeGesture(n){this._swipeGesture=n,this.nativeEl.swipeHandler=n?{canStart:()=>this.stackCtrl.canGoBack(1)&&!this.stackCtrl.hasRunningTask(),onStart:()=>this.stackCtrl.startBackTransition(),onEnd:r=>this.stackCtrl.endBackTransition(r)}:void 0}constructor(n,r,o,i,s,a,c,l){this.parentOutlet=l,this.nativeEl=i.nativeElement,this.name=n||M,this.tabsPrefix=r==="true"?Fd(s,c):void 0,this.stackCtrl=new Ld(this.tabsPrefix,this.nativeEl,s,this.navCtrl,a,o),this.parentContexts.onChildOutletCreated(this.name,this)}ngOnDestroy(){this.stackCtrl.destroy(),this.inputBinder?.unsubscribeFromRouteData(this)}getContext(){return this.parentContexts.getContext(this.name)}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(!this.activated){let n=this.getContext();n?.route&&this.activateWith(n.route,n.injector)}new Promise(n=>rt(this.nativeEl,n)).then(()=>{this._swipeGesture===void 0&&(this.swipeGesture=this.config.getBoolean("swipeBackEnabled",this.nativeEl.mode==="ios"))})}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new Error("Outlet is not activated");return this.activated.instance}get activatedRoute(){if(!this.activated)throw new Error("Outlet is not activated");return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){throw new Error("incompatible reuse strategy")}attach(n,r){throw new Error("incompatible reuse strategy")}deactivate(){if(this.activated){if(this.activatedView){let r=this.getContext();this.activatedView.savedData=new Map(r.children.contexts);let o=this.activatedView.savedData.get("primary");if(o&&r.route&&(o.route=y({},r.route)),this.activatedView.savedExtras={},r.route){let i=r.route.snapshot;this.activatedView.savedExtras.queryParams=i.queryParams,this.activatedView.savedExtras.fragment=i.fragment}}let n=this.component;this.activatedView=null,this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(n)}}activateWith(n,r){if(this.isActivated)throw new Error("Cannot activate an already activated outlet");this._activatedRoute=n;let o,i=this.stackCtrl.getExistingView(n);if(i){o=this.activated=i.ref;let a=i.savedData;if(a){let c=this.getContext();c.children.contexts=a}this.updateActivatedRouteProxy(o.instance,n)}else{let a=n._futureSnapshot,c=this.parentContexts.getOrCreateContext(this.name).children,l=new pe(null),u=this.createActivatedRouteProxy(l,n),f=new jd(u,c,this.location.injector),p=a.routeConfig.component??a.component;o=this.activated=this.outletContent.createComponent(p,{index:this.outletContent.length,injector:f,environmentInjector:r??this.environmentInjector}),l.next(o.instance),i=this.stackCtrl.createView(this.activated,n),this.proxyMap.set(o.instance,u),this.currentActivatedRoute$.next({component:o.instance,activatedRoute:n})}this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activatedView=i,this.navCtrl.setTopOutlet(this);let s=this.stackCtrl.getActiveView();this.stackWillChange.emit({enteringView:i,tabSwitch:Tv(i,s)}),this.stackCtrl.setActive(i).then(a=>{this.activateEvents.emit(o.instance),this.stackDidChange.emit(a)})}canGoBack(n=1,r){return this.stackCtrl.canGoBack(n,r)}pop(n=1,r){return this.stackCtrl.pop(n,r)}getLastUrl(n){let r=this.stackCtrl.getLastUrl(n);return r?r.url:void 0}getLastRouteView(n){return this.stackCtrl.getLastUrl(n)}getRootView(n){return this.stackCtrl.getRootUrl(n)}getActiveStackId(){return this.stackCtrl.getActiveStackId()}createActivatedRouteProxy(n,r){let o=new Le;return o._futureSnapshot=r._futureSnapshot,o._routerState=r._routerState,o.snapshot=r.snapshot,o.outlet=r.outlet,o.component=r.component,o._paramMap=this.proxyObservable(n,"paramMap"),o._queryParamMap=this.proxyObservable(n,"queryParamMap"),o.url=this.proxyObservable(n,"url"),o.params=this.proxyObservable(n,"params"),o.queryParams=this.proxyObservable(n,"queryParams"),o.fragment=this.proxyObservable(n,"fragment"),o.data=this.proxyObservable(n,"data"),o}proxyObservable(n,r){return n.pipe(Se(o=>!!o),Ae(o=>this.currentActivatedRoute$.pipe(Se(i=>i!==null&&i.component===o),Ae(i=>i&&i.activatedRoute[r]),Hc())))}updateActivatedRouteProxy(n,r){let o=this.proxyMap.get(n);if(!o)throw new Error("Could not find activated route proxy for view");o._futureSnapshot=r._futureSnapshot,o._routerState=r._routerState,o.snapshot=r.snapshot,o.outlet=r.outlet,o.component=r.component,this.currentActivatedRoute$.next({component:n,activatedRoute:r})}static \u0275fac=function(r){return new(r||e)($t("name"),$t("tabs"),j(ut),j(we),j(tt),j(q),j(Le),j(e,12))};static \u0275dir=zt({type:e,selectors:[["ion-router-outlet"]],inputs:{animated:"animated",animation:"animation",mode:"mode",swipeGesture:"swipeGesture",name:"name"},outputs:{stackWillChange:"stackWillChange",stackDidChange:"stackDidChange",activateEvents:"activate",deactivateEvents:"deactivate"},exportAs:["outlet"],standalone:!1})}return e})(),jd=class{route;childContexts;parent;constructor(t,n,r){this.route=t,this.childContexts=n,this.parent=r}get(t,n){return t===Le?this.route:t===Qt?this.childContexts:this.parent.get(t,n)}},Nv=new w(""),v_=(()=>{class e{outletDataSubscriptions=new Map;bindActivatedRouteToOutletComponent(n){this.unsubscribeFromRouteData(n),this.subscribeToRouteData(n)}unsubscribeFromRouteData(n){this.outletDataSubscriptions.get(n)?.unsubscribe(),this.outletDataSubscriptions.delete(n)}subscribeToRouteData(n){let{activatedRoute:r}=n,o=ir([r.queryParams,r.params,r.data]).pipe(Ae(([i,s,a],c)=>(a=y(y(y({},i),s),a),c===0?S(a):Promise.resolve(a)))).subscribe(i=>{if(!n.isActivated||!n.activatedComponentRef||n.activatedRoute!==r||r.component===null){this.unsubscribeFromRouteData(n);return}let s=Wu(r.component);if(!s){this.unsubscribeFromRouteData(n);return}for(let{templateName:a}of s.inputs)n.activatedComponentRef.setInput(a,i[a])});this.outletDataSubscriptions.set(n,o)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:e.\u0275fac})}return e})(),kv=()=>({provide:Nv,useFactory:y_,deps:[tt]});function y_(e){return e?.componentInputBindingEnabled?new v_:null}var Vd=class{shouldDetach(t){return!1}shouldAttach(t){return!1}store(t,n){}retrieve(t){return null}shouldReuseRoute(t,n){if(t.routeConfig!==n.routeConfig)return!1;let r=t.params,o=n.params,i=Object.keys(r),s=Object.keys(o);if(i.length!==s.length)return!1;for(let a of i)if(o[a]!==r[a])return!1;return!0}},yi=class{ctrl;constructor(t){this.ctrl=t}create(t){return this.ctrl.create(t||{})}dismiss(t,n,r){return this.ctrl.dismiss(t,n,r)}getTop(){return this.ctrl.getTop()}};var Qr=()=>{let e;return{lock:()=>b(null,null,function*(){let n=e,r;return e=new Promise(o=>r=o),n!==void 0&&(yield n),r})}};var D_=":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:layout size style;z-index:0}",C_=Ce(class extends De{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionNavWillLoad=H(this,"ionNavWillLoad",7),this.ionNavWillChange=H(this,"ionNavWillChange",3),this.ionNavDidChange=H(this,"ionNavDidChange",3),this.lockController=Qr(),this.gestureOrAnimationInProgress=!1,this.mode=J(this),this.animated=!0}swipeHandlerChanged(){this.gesture&&this.gesture.enable(this.swipeHandler!==void 0)}connectedCallback(){return b(this,null,function*(){let t=()=>{this.gestureOrAnimationInProgress=!0,this.swipeHandler&&this.swipeHandler.onStart()};this.gesture=(yield import("./chunk-VTPPEPWF.js")).createSwipeBackGesture(this.el,()=>!this.gestureOrAnimationInProgress&&!!this.swipeHandler&&this.swipeHandler.canStart(),()=>t(),n=>{var r;return(r=this.ani)===null||r===void 0?void 0:r.progressStep(n)},(n,r,o)=>{if(this.ani){this.ani.onFinish(()=>{this.gestureOrAnimationInProgress=!1,this.swipeHandler&&this.swipeHandler.onEnd(n)},{oneTimeCallback:!0});let i=n?-.001:.001;n?i+=zn([0,0],[.32,.72],[0,1],[1,1],r)[0]:(this.ani.easing("cubic-bezier(1, 0, 0.68, 0.28)"),i+=zn([0,0],[1,0],[.68,.28],[1,1],r)[0]),this.ani.progressEnd(n?1:0,i,o)}else this.gestureOrAnimationInProgress=!1}),this.swipeHandlerChanged()})}componentWillLoad(){this.ionNavWillLoad.emit()}disconnectedCallback(){this.gesture&&(this.gesture.destroy(),this.gesture=void 0)}commit(t,n,r){return b(this,null,function*(){let o=yield this.lockController.lock(),i=!1;try{i=yield this.transition(t,n,r)}catch(s){Ei("[ion-router-outlet] - Exception in commit:",s)}return o(),i})}setRouteId(t,n,r,o){return b(this,null,function*(){return{changed:yield this.setRoot(t,n,{duration:r==="root"?0:void 0,direction:r==="back"?"back":"forward",animationBuilder:o}),element:this.activeEl}})}getRouteId(){return b(this,null,function*(){let t=this.activeEl;return t?{id:t.tagName,element:t,params:this.activeParams}:void 0})}setRoot(t,n,r){return b(this,null,function*(){if(this.activeComponent===t&&sf(n,this.activeParams))return!1;let o=this.activeEl,i=yield zr(this.delegate,this.el,t,["ion-page","ion-page-invisible"],n);return this.activeComponent=t,this.activeEl=i,this.activeParams=n,yield this.commit(i,o,r),yield Gr(this.delegate,o),!0})}transition(o,i){return b(this,arguments,function*(t,n,r={}){if(n===t)return!1;this.ionNavWillChange.emit();let{el:s,mode:a}=this,c=this.animated&&X.getBoolean("animated",!0),l=r.animationBuilder||this.animation||X.get("navAnimation");return yield af(Object.assign(Object.assign({mode:a,animated:c,enteringEl:t,leavingEl:n,baseEl:s,deepWait:At(s),progressCallback:r.progressAnimation?u=>{u!==void 0&&!this.gestureOrAnimationInProgress?(this.gestureOrAnimationInProgress=!0,u.onFinish(()=>{this.gestureOrAnimationInProgress=!1,this.swipeHandler&&this.swipeHandler.onEnd(!1)},{oneTimeCallback:!0}),u.progressEnd(0,0,0)):this.ani=u}:void 0},r),{animationBuilder:l})),this.ionNavDidChange.emit(),!0})}render(){return T("slot",{key:"84b50f1155b0d780dff802ee13223287259fd525"})}get el(){return this}static get watchers(){return{swipeHandler:["swipeHandlerChanged"]}}static get style(){return D_}},[1,"ion-router-outlet",{mode:[1025],delegate:[16],animated:[4],animation:[16],swipeHandler:[16,"swipe-handler"],commit:[64],setRouteId:[64],getRouteId:[64]},void 0,{swipeHandler:["swipeHandlerChanged"]}]);function b_(){if(typeof customElements>"u")return;["ion-router-outlet"].forEach(t=>{switch(t){case"ion-router-outlet":customElements.get(t)||customElements.define(t,C_);break}})}var Ov=b_;var I_=":host{left:0;right:0;top:0;bottom:0;display:block;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);contain:strict;cursor:pointer;opacity:0.01;-ms-touch-action:none;touch-action:none;z-index:2}:host(.backdrop-hide){background:transparent}:host(.backdrop-no-tappable){cursor:auto}:host{background-color:var(--ion-backdrop-color, #000)}",E_=":host{left:0;right:0;top:0;bottom:0;display:block;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);contain:strict;cursor:pointer;opacity:0.01;-ms-touch-action:none;touch-action:none;z-index:2}:host(.backdrop-hide){background:transparent}:host(.backdrop-no-tappable){cursor:auto}:host{background-color:var(--ion-backdrop-color, #000)}",w_=Ce(class extends De{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionBackdropTap=H(this,"ionBackdropTap",7),this.visible=!0,this.tappable=!0,this.stopPropagation=!0}onMouseDown(t){this.emitTap(t)}emitTap(t){this.stopPropagation&&(t.preventDefault(),t.stopPropagation()),this.tappable&&this.ionBackdropTap.emit()}render(){let t=J(this);return T(xe,{key:"7abaf2c310aa399607451b14063265e8a5846938","aria-hidden":"true",class:{[t]:!0,"backdrop-hide":!this.visible,"backdrop-no-tappable":!this.tappable}})}static get style(){return{ios:I_,md:E_}}},[33,"ion-backdrop",{visible:[4],tappable:[4],stopPropagation:[4,"stop-propagation"]},[[2,"click","onMouseDown"]]]);function Ja(){if(typeof customElements>"u")return;["ion-backdrop"].forEach(t=>{switch(t){case"ion-backdrop":customElements.get(t)||customElements.define(t,w_);break}})}var Di=function(e){return e.Dark="DARK",e.Light="LIGHT",e.Default="DEFAULT",e}(Di||{}),Ud={getEngine(){let e=df();if(e?.isPluginAvailable("StatusBar"))return e.Plugins.StatusBar},setStyle(e){let t=this.getEngine();t&&t.setStyle(e)},getStyle:function(){return b(this,null,function*(){let e=this.getEngine();if(!e)return Di.Default;let{style:t}=yield e.getInfo();return t})}},Bd=(e,t)=>{if(t===1)return 0;let n=1/(1-t),r=-(t*n);return e*n+r},jv=()=>{!no||no.innerWidth>=768||Ud.setStyle({style:Di.Dark})},Hd=(e=Di.Default)=>{!no||no.innerWidth>=768||Ud.setStyle({style:e})},Vv=(e,t)=>b(null,null,function*(){typeof e.canDismiss!="function"||!(yield e.canDismiss(void 0,Zr))||(t.isRunning()?t.onFinish(()=>{e.dismiss(void 0,"handler")},{oneTimeCallback:!0}):e.dismiss(void 0,"handler"))}),$d=e=>.00255275*2.71828**(-14.9619*e)-1.00255*2.71828**(-.0380968*e)+1,ec={MIN_PRESENTING_SCALE:.915},__=(e,t,n,r)=>{let i=e.offsetHeight,s=!1,a=!1,c=null,l=null,u=.2,f=!0,p=0,d=()=>c&&Zn(c)?c.scrollY:!0,Z=Mi({el:e,gestureName:"modalSwipeToClose",gesturePriority:mv,direction:"y",threshold:10,canStart:N=>{let V=N.event.target;return V===null||!V.closest?!0:(c=xi(V),c?(Zn(c)?l=de(c).querySelector(".inner-scroll"):l=c,!!!c.querySelector("ion-refresher")&&l.scrollTop===0):V.closest("ion-footer")===null)},onStart:N=>{let{deltaY:V}=N;f=d(),a=e.canDismiss!==void 0&&e.canDismiss!==!0,V>0&&c&&vc(c),t.progressStart(!0,s?1:0)},onMove:N=>{let{deltaY:V}=N;V>0&&c&&vc(c);let Y=N.deltaY/i,k=Y>=0&&a,me=k?u:.9999,ye=k?$d(Y/me):Y,ge=Jt(1e-4,ye,me);t.progressStep(ge),ge>=.5&&p<.5?Hd(n):ge<.5&&p>=.5&&jv(),p=ge},onEnd:N=>{let V=N.velocityY,Y=N.deltaY/i,k=Y>=0&&a,me=k?u:.9999,ye=k?$d(Y/me):Y,ge=Jt(1e-4,ye,me),Ne=(N.deltaY+V*1e3)/i,le=!k&&Ne>=.5,ue=le?-.001:.001;le?(t.easing("cubic-bezier(0.32, 0.72, 0, 1)"),ue+=zn([0,0],[.32,.72],[0,1],[1,1],ge)[0]):(t.easing("cubic-bezier(1, 0, 0.68, 0.28)"),ue+=zn([0,0],[1,0],[.68,.28],[1,1],ge)[0]);let ft=Pv(le?Y*i:(1-ge)*i,V);s=le,Z.enable(!1),c&&uf(c,f),t.onFinish(()=>{le||Z.enable(!0)}).progressEnd(le?1:0,ue,ft),k&&ge>me/4?Vv(e,t):le&&r()}});return Z},Pv=(e,t)=>Jt(400,e/Math.abs(t*1.1),500),Bv=e=>{let{currentBreakpoint:t,backdropBreakpoint:n,expandToScroll:r}=e,o=n===void 0||n<t,i=o?`calc(var(--backdrop-opacity) * ${t})`:"0",s=R("backdropAnimation").fromTo("opacity",0,i);o&&s.beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]);let a=R("wrapperAnimation").keyframes([{offset:0,opacity:1,transform:"translateY(100%)"},{offset:1,opacity:1,transform:`translateY(${100-t*100}%)`}]),c=r?void 0:R("contentAnimation").keyframes([{offset:0,opacity:1,maxHeight:`${(1-t)*100}%`},{offset:1,opacity:1,maxHeight:`${t*100}%`}]);return{wrapperAnimation:a,backdropAnimation:s,contentAnimation:c}},Hv=e=>{let{currentBreakpoint:t,backdropBreakpoint:n}=e,r=`calc(var(--backdrop-opacity) * ${Bd(t,n)})`,o=[{offset:0,opacity:r},{offset:1,opacity:0}],i=[{offset:0,opacity:r},{offset:n,opacity:0},{offset:1,opacity:0}],s=R("backdropAnimation").keyframes(n!==0?i:o);return{wrapperAnimation:R("wrapperAnimation").keyframes([{offset:0,opacity:1,transform:`translateY(${100-t*100}%)`},{offset:1,opacity:1,transform:"translateY(100%)"}]),backdropAnimation:s}},S_=()=>{let e=R().fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),t=R().fromTo("transform","translateY(100vh)","translateY(0vh)");return{backdropAnimation:e,wrapperAnimation:t,contentAnimation:void 0}},Fv=(e,t)=>{let{presentingEl:n,currentBreakpoint:r,expandToScroll:o}=t,i=de(e),{wrapperAnimation:s,backdropAnimation:a,contentAnimation:c}=r!==void 0?Bv(t):S_();a.addElement(i.querySelector("ion-backdrop")),s.addElement(i.querySelectorAll(".modal-wrapper, .modal-shadow")).beforeStyles({opacity:1}),!o&&c?.addElement(e.querySelector(".ion-page"));let l=R("entering-base").addElement(e).easing("cubic-bezier(0.32,0.72,0,1)").duration(500).addAnimation([s]);if(c&&l.addAnimation(c),n){let u=window.innerWidth<768,f=n.tagName==="ION-MODAL"&&n.presentingElement!==void 0,p=de(n),d=R().beforeStyles({transform:"translateY(0)","transform-origin":"top center",overflow:"hidden"}),m=document.body;if(u){let g=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",v=f?"-10px":g,D=ec.MIN_PRESENTING_SCALE,Z=`translateY(${v}) scale(${D})`;d.afterStyles({transform:Z}).beforeAddWrite(()=>m.style.setProperty("background-color","black")).addElement(n).keyframes([{offset:0,filter:"contrast(1)",transform:"translateY(0px) scale(1)",borderRadius:"0px"},{offset:1,filter:"contrast(0.85)",transform:Z,borderRadius:"10px 10px 0 0"}]),l.addAnimation(d)}else if(l.addAnimation(a),!f)s.fromTo("opacity","0","1");else{let v=`translateY(-10px) scale(${f?ec.MIN_PRESENTING_SCALE:1})`;d.afterStyles({transform:v}).addElement(p.querySelector(".modal-wrapper")).keyframes([{offset:0,filter:"contrast(1)",transform:"translateY(0) scale(1)"},{offset:1,filter:"contrast(0.85)",transform:v}]);let D=R().afterStyles({transform:v}).addElement(p.querySelector(".modal-shadow")).keyframes([{offset:0,opacity:"1",transform:"translateY(0) scale(1)"},{offset:1,opacity:"0",transform:v}]);l.addAnimation([d,D])}}else l.addAnimation(a);return l},T_=()=>{let e=R().fromTo("opacity","var(--backdrop-opacity)",0),t=R().fromTo("transform","translateY(0vh)","translateY(100vh)");return{backdropAnimation:e,wrapperAnimation:t}},Lv=(e,t,n=500)=>{let{presentingEl:r,currentBreakpoint:o}=t,i=de(e),{wrapperAnimation:s,backdropAnimation:a}=o!==void 0?Hv(t):T_();a.addElement(i.querySelector("ion-backdrop")),s.addElement(i.querySelectorAll(".modal-wrapper, .modal-shadow")).beforeStyles({opacity:1});let c=R("leaving-base").addElement(e).easing("cubic-bezier(0.32,0.72,0,1)").duration(n).addAnimation(s);if(r){let l=window.innerWidth<768,u=r.tagName==="ION-MODAL"&&r.presentingElement!==void 0,f=de(r),p=R().beforeClearStyles(["transform"]).afterClearStyles(["transform"]).onFinish(m=>{if(m!==1)return;r.style.setProperty("overflow",""),Array.from(d.querySelectorAll("ion-modal:not(.overlay-hidden)")).filter(v=>v.presentingElement!==void 0).length<=1&&d.style.setProperty("background-color","")}),d=document.body;if(l){let m=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",g=u?"-10px":m,v=ec.MIN_PRESENTING_SCALE,D=`translateY(${g}) scale(${v})`;p.addElement(r).keyframes([{offset:0,filter:"contrast(0.85)",transform:D,borderRadius:"10px 10px 0 0"},{offset:1,filter:"contrast(1)",transform:"translateY(0px) scale(1)",borderRadius:"0px"}]),c.addAnimation(p)}else if(c.addAnimation(a),!u)s.fromTo("opacity","1","0");else{let g=`translateY(-10px) scale(${u?ec.MIN_PRESENTING_SCALE:1})`;p.addElement(f.querySelector(".modal-wrapper")).afterStyles({transform:"translate3d(0, 0, 0)"}).keyframes([{offset:0,filter:"contrast(0.85)",transform:g},{offset:1,filter:"contrast(1)",transform:"translateY(0) scale(1)"}]);let v=R().addElement(f.querySelector(".modal-shadow")).afterStyles({transform:"translateY(0) scale(1)"}).keyframes([{offset:0,opacity:"0",transform:g},{offset:1,opacity:"1",transform:"translateY(0) scale(1)"}]);c.addAnimation([p,v])}}else c.addAnimation(a);return c},M_=()=>{let e=R().fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),t=R().keyframes([{offset:0,opacity:.01,transform:"translateY(40px)"},{offset:1,opacity:1,transform:"translateY(0px)"}]);return{backdropAnimation:e,wrapperAnimation:t,contentAnimation:void 0}},x_=(e,t)=>{let{currentBreakpoint:n,expandToScroll:r}=t,o=de(e),{wrapperAnimation:i,backdropAnimation:s,contentAnimation:a}=n!==void 0?Bv(t):M_();s.addElement(o.querySelector("ion-backdrop")),i.addElement(o.querySelector(".modal-wrapper")),!r&&a?.addElement(e.querySelector(".ion-page"));let c=R().addElement(e).easing("cubic-bezier(0.36,0.66,0.04,1)").duration(280).addAnimation([s,i]);return a&&c.addAnimation(a),c},A_=()=>{let e=R().fromTo("opacity","var(--backdrop-opacity)",0),t=R().keyframes([{offset:0,opacity:.99,transform:"translateY(0px)"},{offset:1,opacity:0,transform:"translateY(40px)"}]);return{backdropAnimation:e,wrapperAnimation:t}},R_=(e,t)=>{let{currentBreakpoint:n}=t,r=de(e),{wrapperAnimation:o,backdropAnimation:i}=n!==void 0?Hv(t):A_();return i.addElement(r.querySelector("ion-backdrop")),o.addElement(r.querySelector(".modal-wrapper")),R().easing("cubic-bezier(0.47,0,0.745,0.715)").duration(200).addAnimation([i,o])},N_=(e,t,n,r,o,i,s=[],a,c,l,u)=>{let f=[{offset:0,opacity:"var(--backdrop-opacity)"},{offset:1,opacity:.01}],p=[{offset:0,opacity:"var(--backdrop-opacity)"},{offset:1-o,opacity:0},{offset:1,opacity:0}],d={WRAPPER_KEYFRAMES:[{offset:0,transform:"translateY(0%)"},{offset:1,transform:"translateY(100%)"}],BACKDROP_KEYFRAMES:o!==0?p:f,CONTENT_KEYFRAMES:[{offset:0,maxHeight:"100%"},{offset:1,maxHeight:"0%"}]},m=e.querySelector("ion-content"),g=n.clientHeight,v=r,D=0,Z=!1,N=null,V=null,Y=null,k=null,me=.95,ye=s[s.length-1],ge=s[0],Ne=i.childAnimations.find(Q=>Q.id==="wrapperAnimation"),le=i.childAnimations.find(Q=>Q.id==="backdropAnimation"),ue=i.childAnimations.find(Q=>Q.id==="contentAnimation"),ft=()=>{e.style.setProperty("pointer-events","auto"),t.style.setProperty("pointer-events","auto"),e.classList.remove(Wn)},yn=()=>{e.style.setProperty("pointer-events","none"),t.style.setProperty("pointer-events","none"),e.classList.add(Wn)},Kt=Q=>{if(!V&&(V=Array.from(e.querySelectorAll("ion-footer")),!V.length))return;let B=e.querySelector(".ion-page");if(k=Q,Q==="stationary")V.forEach(K=>{K.classList.remove("modal-footer-moving"),K.style.removeProperty("position"),K.style.removeProperty("width"),K.style.removeProperty("height"),K.style.removeProperty("top"),K.style.removeProperty("left"),B?.style.removeProperty("padding-bottom"),B?.appendChild(K)});else{let K=0;V.forEach((W,Mt)=>{let nt=W.getBoundingClientRect(),Me=document.body.getBoundingClientRect();K+=W.clientHeight;let Jr=nt.top-Me.top,eo=nt.left-Me.left;if(W.style.setProperty("--pinned-width",`${W.clientWidth}px`),W.style.setProperty("--pinned-height",`${W.clientHeight}px`),W.style.setProperty("--pinned-top",`${Jr}px`),W.style.setProperty("--pinned-left",`${eo}px`),Mt===0){Y=Jr;let cc=e.querySelector("ion-header");cc&&(Y-=cc.clientHeight)}}),V.forEach(W=>{B?.style.setProperty("padding-bottom",`${K}px`),W.classList.add("modal-footer-moving"),W.style.setProperty("position","absolute"),W.style.setProperty("width","var(--pinned-width)"),W.style.setProperty("height","var(--pinned-height)"),W.style.setProperty("top","var(--pinned-top)"),W.style.setProperty("left","var(--pinned-left)"),document.body.appendChild(W)})}};Ne&&le&&(Ne.keyframes([...d.WRAPPER_KEYFRAMES]),le.keyframes([...d.BACKDROP_KEYFRAMES]),ue?.keyframes([...d.CONTENT_KEYFRAMES]),i.progressStart(!0,1-v),v>o?ft():yn()),m&&v!==ye&&a&&(m.scrollY=!1);let oc=Q=>{let B=xi(Q.event.target);if(v=c(),!a&&B)return(Zn(B)?de(B).querySelector(".inner-scroll"):B).scrollTop===0;if(v===1&&B){let K=Zn(B)?de(B).querySelector(".inner-scroll"):B;return!!!B.querySelector("ion-refresher")&&K.scrollTop===0}return!0},ic=Q=>{if(Z=e.canDismiss!==void 0&&e.canDismiss!==!0&&ge===0,!a){let B=xi(Q.event.target);N=B&&Zn(B)?de(B).querySelector(".inner-scroll"):B}a||Kt("moving"),Q.deltaY>0&&m&&(m.scrollY=!1),Rt(()=>{e.focus()}),i.progressStart(!0,1-v)},sc=Q=>{if(!a&&Y!==null&&k!==null&&(Q.currentY>=Y&&k==="moving"?Kt("stationary"):Q.currentY<Y&&k==="stationary"&&Kt("moving")),!a&&Q.deltaY<=0&&N)return;Q.deltaY>0&&m&&(m.scrollY=!1);let B=1-v,K=s.length>1?1-s[1]:void 0,W=B+Q.deltaY/g,Mt=K!==void 0&&W>=K&&Z,nt=Mt?me:.9999,Me=Mt&&K!==void 0?K+$d((W-K)/(nt-K)):W;D=Jt(1e-4,Me,nt),i.progressStep(D)},ac=Q=>{if(!a&&Q.deltaY<=0&&N&&N.scrollTop>0){Kt("stationary");return}let B=Q.velocityY,K=(Q.deltaY+B*350)/g,W=v-K,Mt=s.reduce((nt,Me)=>Math.abs(Me-W)<Math.abs(nt-W)?Me:nt);Xr({breakpoint:Mt,breakpointOffset:D,canDismiss:Z,animated:!0})},Xr=Q=>{let{breakpoint:B,canDismiss:K,breakpointOffset:W,animated:Mt}=Q,nt=K&&B===0,Me=nt?v:B,Jr=Me!==0;return v=0,Ne&&le&&(Ne.keyframes([{offset:0,transform:`translateY(${W*100}%)`},{offset:1,transform:`translateY(${(1-Me)*100}%)`}]),le.keyframes([{offset:0,opacity:`calc(var(--backdrop-opacity) * ${Bd(1-W,o)})`},{offset:1,opacity:`calc(var(--backdrop-opacity) * ${Bd(Me,o)})`}]),ue&&ue.keyframes([{offset:0,maxHeight:`${(1-W)*100}%`},{offset:1,maxHeight:`${Me*100}%`}]),i.progressStep(0)),Dn.enable(!1),nt?Vv(e,i):Jr||l(),m&&(Me===s[s.length-1]||!a)&&(m.scrollY=!0),!a&&Me===0&&Kt("stationary"),new Promise(eo=>{i.onFinish(()=>{Jr?(a||Kt("stationary"),Ne&&le?Rt(()=>{Ne.keyframes([...d.WRAPPER_KEYFRAMES]),le.keyframes([...d.BACKDROP_KEYFRAMES]),ue?.keyframes([...d.CONTENT_KEYFRAMES]),i.progressStart(!0,1-Me),v=Me,u(v),v>o?ft():yn(),Dn.enable(!0),eo()}):(Dn.enable(!0),eo())):eo()},{oneTimeCallback:!0}).progressEnd(1,0,Mt?500:0)})},Dn=Mi({el:n,gestureName:"modalSheet",gesturePriority:40,direction:"y",threshold:10,canStart:oc,onStart:ic,onMove:sc,onEnd:ac});return{gesture:Dn,moveSheetToBreakpoint:Xr}},k_=':host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, var(--ion-background-color-step-350, #c0c0be));cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:""}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host(.modal-sheet.modal-no-expand-scroll) ion-footer{position:absolute;bottom:0;width:var(--width)}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.4)}:host(.modal-card),:host(.modal-sheet){--border-radius:10px}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:10px}}.modal-wrapper{-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0)}@media screen and (max-width: 767px){@supports (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - max(30px, var(--ion-safe-area-top)) - 10px)}}@supports not (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - 40px)}}:host(.modal-card) .modal-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0;border-end-start-radius:0}:host(.modal-card){--backdrop-opacity:0;--width:100%;-ms-flex-align:end;align-items:flex-end}:host(.modal-card) .modal-shadow{display:none}:host(.modal-card) ion-backdrop{pointer-events:none}}@media screen and (min-width: 768px){:host(.modal-card){--width:calc(100% - 120px);--height:calc(100% - (120px + var(--ion-safe-area-top) + var(--ion-safe-area-bottom)));--max-width:720px;--max-height:1000px;--backdrop-opacity:0;--box-shadow:0px 0px 30px 10px rgba(0, 0, 0, 0.1);-webkit-transition:all 0.5s ease-in-out;transition:all 0.5s ease-in-out}:host(.modal-card) .modal-wrapper{-webkit-box-shadow:none;box-shadow:none}:host(.modal-card) .modal-shadow{-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow)}}:host(.modal-sheet) .modal-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0;border-end-start-radius:0}',O_=':host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, var(--ion-background-color-step-350, #c0c0be));cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:""}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host(.modal-sheet.modal-no-expand-scroll) ion-footer{position:absolute;bottom:0;width:var(--width)}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.32)}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:2px;--box-shadow:0 28px 48px rgba(0, 0, 0, 0.4)}}.modal-wrapper{-webkit-transform:translate3d(0,  40px,  0);transform:translate3d(0,  40px,  0);opacity:0.01}',$v=Ce(class extends De{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.didPresent=H(this,"ionModalDidPresent",7),this.willPresent=H(this,"ionModalWillPresent",7),this.willDismiss=H(this,"ionModalWillDismiss",7),this.didDismiss=H(this,"ionModalDidDismiss",7),this.ionBreakpointDidChange=H(this,"ionBreakpointDidChange",7),this.didPresentShorthand=H(this,"didPresent",7),this.willPresentShorthand=H(this,"willPresent",7),this.willDismissShorthand=H(this,"willDismiss",7),this.didDismissShorthand=H(this,"didDismiss",7),this.ionMount=H(this,"ionMount",7),this.lockController=Qr(),this.triggerController=gv(),this.coreDelegate=Ua(),this.isSheetModal=!1,this.inheritedAttributes={},this.inline=!1,this.gestureAnimationDismissing=!1,this.presented=!1,this.hasController=!1,this.keyboardClose=!0,this.expandToScroll=!0,this.backdropBreakpoint=0,this.handleBehavior="none",this.backdropDismiss=!0,this.showBackdrop=!0,this.animated=!0,this.isOpen=!1,this.keepContentsMounted=!1,this.focusTrap=!0,this.canDismiss=!0,this.onHandleClick=()=>{let{sheetTransition:t,handleBehavior:n}=this;n!=="cycle"||t!==void 0||this.moveToNextBreakpoint()},this.onBackdropTap=()=>{let{sheetTransition:t}=this;t===void 0&&this.dismiss(void 0,Wr)},this.onLifecycle=t=>{let n=this.usersElement,r=P_[t.type];if(n&&r){let o=new CustomEvent(r,{bubbles:!1,cancelable:!1,detail:t.detail});n.dispatchEvent(o)}}}onIsOpenChange(t,n){t===!0&&n===!1?this.present():t===!1&&n===!0&&this.dismiss()}triggerChanged(){let{trigger:t,el:n,triggerController:r}=this;t&&r.addClickListener(n,t)}breakpointsChanged(t){t!==void 0&&(this.sortedBreakpoints=t.sort((n,r)=>n-r))}connectedCallback(){let{el:t}=this;Wa(t),this.triggerChanged()}disconnectedCallback(){this.triggerController.removeClickListener()}componentWillLoad(){var t;let{breakpoints:n,initialBreakpoint:r,el:o,htmlAttributes:i}=this,s=this.isSheetModal=n!==void 0&&r!==void 0,a=["aria-label","role"];this.inheritedAttributes=rf(o,a),i!==void 0&&a.forEach(c=>{i[c]&&(this.inheritedAttributes=Object.assign(Object.assign({},this.inheritedAttributes),{[c]:i[c]}),delete i[c])}),s&&(this.currentBreakpoint=this.initialBreakpoint),n!==void 0&&r!==void 0&&!n.includes(r)&&Qe("[ion-modal] - Your breakpoints array must include the initialBreakpoint value."),!((t=this.htmlAttributes)===null||t===void 0)&&t.id||qa(this.el)}componentDidLoad(){this.isOpen===!0&&Rt(()=>this.present()),this.breakpointsChanged(this.breakpoints),this.triggerChanged()}getDelegate(t=!1){if(this.workingDelegate&&!t)return{delegate:this.workingDelegate,inline:this.inline};let n=this.el.parentNode,r=this.inline=n!==null&&!this.hasController,o=this.workingDelegate=r?this.delegate||this.coreDelegate:this.delegate;return{inline:r,delegate:o}}checkCanDismiss(t,n){return b(this,null,function*(){let{canDismiss:r}=this;return typeof r=="function"?r(t,n):r})}present(){return b(this,null,function*(){let t=yield this.lockController.lock();if(this.presented){t();return}let{presentingElement:n,el:r}=this;this.currentBreakpoint=this.initialBreakpoint;let{inline:o,delegate:i}=this.getDelegate(!0);this.ionMount.emit(),this.usersElement=yield zr(i,r,this.component,["ion-page"],this.componentProps,o),At(r)?yield Ti(this.usersElement):this.keepContentsMounted||(yield Si()),xt(()=>this.el.classList.add("show-modal"));let s=n!==void 0;s&&J(this)==="ios"&&(this.statusBarStyle=yield Ud.getStyle(),jv()),yield Ya(this,"modalEnter",Fv,x_,{presentingEl:n,currentBreakpoint:this.initialBreakpoint,backdropBreakpoint:this.backdropBreakpoint,expandToScroll:this.expandToScroll}),typeof window<"u"&&(this.keyboardOpenCallback=()=>{this.gesture&&(this.gesture.enable(!1),Rt(()=>{this.gesture&&this.gesture.enable(!0)}))},window.addEventListener(yc,this.keyboardOpenCallback)),this.isSheetModal?this.initSheetGesture():s&&this.initSwipeToClose(),t()})}initSwipeToClose(){var t;if(J(this)!=="ios")return;let{el:n}=this,r=this.leaveAnimation||X.get("modalLeave",Lv),o=this.animation=r(n,{presentingEl:this.presentingElement,expandToScroll:this.expandToScroll});if(!oo(n)){io(n);return}let s=(t=this.statusBarStyle)!==null&&t!==void 0?t:Di.Default;this.gesture=__(n,o,s,()=>{this.gestureAnimationDismissing=!0,Hd(this.statusBarStyle),this.animation.onFinish(()=>b(this,null,function*(){yield this.dismiss(void 0,Zr),this.gestureAnimationDismissing=!1}))}),this.gesture.enable(!0)}initSheetGesture(){let{wrapperEl:t,initialBreakpoint:n,backdropBreakpoint:r}=this;if(!t||n===void 0)return;let o=this.enterAnimation||X.get("modalEnter",Fv),i=this.animation=o(this.el,{presentingEl:this.presentingElement,currentBreakpoint:n,backdropBreakpoint:r,expandToScroll:this.expandToScroll});i.progressStart(!0,1);let{gesture:s,moveSheetToBreakpoint:a}=N_(this.el,this.backdropEl,t,n,r,i,this.sortedBreakpoints,this.expandToScroll,()=>{var c;return(c=this.currentBreakpoint)!==null&&c!==void 0?c:0},()=>this.sheetOnDismiss(),c=>{this.currentBreakpoint!==c&&(this.currentBreakpoint=c,this.ionBreakpointDidChange.emit({breakpoint:c}))});this.gesture=s,this.moveSheetToBreakpoint=a,this.gesture.enable(!0)}sheetOnDismiss(){this.gestureAnimationDismissing=!0,this.animation.onFinish(()=>b(this,null,function*(){this.currentBreakpoint=0,this.ionBreakpointDidChange.emit({breakpoint:this.currentBreakpoint}),yield this.dismiss(void 0,Zr),this.gestureAnimationDismissing=!1}))}dismiss(t,n){return b(this,null,function*(){var r;if(this.gestureAnimationDismissing&&n!==Zr)return!1;let o=yield this.lockController.lock();if(n!=="handler"&&!(yield this.checkCanDismiss(t,n)))return o(),!1;let{presentingElement:i}=this;i!==void 0&&J(this)==="ios"&&Hd(this.statusBarStyle),typeof window<"u"&&this.keyboardOpenCallback&&(window.removeEventListener(yc,this.keyboardOpenCallback),this.keyboardOpenCallback=void 0);let a=yield Qa(this,t,n,"modalLeave",Lv,R_,{presentingEl:i,currentBreakpoint:(r=this.currentBreakpoint)!==null&&r!==void 0?r:this.initialBreakpoint,backdropBreakpoint:this.backdropBreakpoint,expandToScroll:this.expandToScroll});if(a){let{delegate:c}=this.getDelegate();yield Gr(c,this.usersElement),xt(()=>this.el.classList.remove("show-modal")),this.animation&&this.animation.destroy(),this.gesture&&this.gesture.destroy()}return this.currentBreakpoint=void 0,this.animation=void 0,o(),a})}onDidDismiss(){return qr(this.el,"ionModalDidDismiss")}onWillDismiss(){return qr(this.el,"ionModalWillDismiss")}setCurrentBreakpoint(t){return b(this,null,function*(){if(!this.isSheetModal){Qe("[ion-modal] - setCurrentBreakpoint is only supported on sheet modals.");return}if(!this.breakpoints.includes(t)){Qe(`[ion-modal] - Attempted to set invalid breakpoint value ${t}. Please double check that the breakpoint value is part of your defined breakpoints.`);return}let{currentBreakpoint:n,moveSheetToBreakpoint:r,canDismiss:o,breakpoints:i,animated:s}=this;n!==t&&r&&(this.sheetTransition=r({breakpoint:t,breakpointOffset:1-n,canDismiss:o!==void 0&&o!==!0&&i[0]===0,animated:s}),yield this.sheetTransition,this.sheetTransition=void 0)})}getCurrentBreakpoint(){return b(this,null,function*(){return this.currentBreakpoint})}moveToNextBreakpoint(){return b(this,null,function*(){let{breakpoints:t,currentBreakpoint:n}=this;if(!t||n==null)return!1;let r=t.filter(a=>a!==0),i=(r.indexOf(n)+1)%r.length,s=r[i];return yield this.setCurrentBreakpoint(s),!0})}render(){let{handle:t,isSheetModal:n,presentingElement:r,htmlAttributes:o,handleBehavior:i,inheritedAttributes:s,focusTrap:a,expandToScroll:c}=this,l=t!==!1&&n,u=J(this),f=r!==void 0&&u==="ios",p=i==="cycle";return T(xe,Object.assign({key:"0bcbdcfcd7d890eb599da3f97f21c317d34f8e0e","no-router":!0,tabindex:"-1"},o,{style:{zIndex:`${2e4+this.overlayIndex}`},class:Object.assign({[u]:!0,"modal-default":!f&&!n,"modal-card":f,"modal-sheet":n,"modal-no-expand-scroll":n&&!c,"overlay-hidden":!0,[Wn]:a===!1},$a(this.cssClass)),onIonBackdropTap:this.onBackdropTap,onIonModalDidPresent:this.onLifecycle,onIonModalWillPresent:this.onLifecycle,onIonModalWillDismiss:this.onLifecycle,onIonModalDidDismiss:this.onLifecycle}),T("ion-backdrop",{key:"d72159e73daa5af7349aa9e8f695aa435eb43069",ref:d=>this.backdropEl=d,visible:this.showBackdrop,tappable:this.backdropDismiss,part:"backdrop"}),u==="ios"&&T("div",{key:"fd2d9b13676ae72473881649a397b6eacde03a03",class:"modal-shadow"}),T("div",Object.assign({key:"908eccb1ad982dcde2dbcff0cbb18b6e60f8ba74",role:"dialog"},s,{"aria-modal":"true",class:"modal-wrapper ion-overlay-wrapper",part:"content",ref:d=>this.wrapperEl=d}),l&&T("button",{key:"332dc0b40363a77c7be62331d9f26def91c790e9",class:"modal-handle",tabIndex:p?0:-1,"aria-label":"Activate to adjust the size of the dialog overlaying the screen",onClick:p?this.onHandleClick:void 0,part:"handle"}),T("slot",{key:"c32698350193c450327e97049daf8b8d1fda0d0e"})))}get el(){return this}static get watchers(){return{isOpen:["onIsOpenChange"],trigger:["triggerChanged"]}}static get style(){return{ios:k_,md:O_}}},[33,"ion-modal",{hasController:[4,"has-controller"],overlayIndex:[2,"overlay-index"],delegate:[16],keyboardClose:[4,"keyboard-close"],enterAnimation:[16,"enter-animation"],leaveAnimation:[16,"leave-animation"],breakpoints:[16],expandToScroll:[4,"expand-to-scroll"],initialBreakpoint:[2,"initial-breakpoint"],backdropBreakpoint:[2,"backdrop-breakpoint"],handle:[4],handleBehavior:[1,"handle-behavior"],component:[1],componentProps:[16,"component-props"],cssClass:[1,"css-class"],backdropDismiss:[4,"backdrop-dismiss"],showBackdrop:[4,"show-backdrop"],animated:[4],presentingElement:[16,"presenting-element"],htmlAttributes:[16,"html-attributes"],isOpen:[4,"is-open"],trigger:[1],keepContentsMounted:[4,"keep-contents-mounted"],focusTrap:[4,"focus-trap"],canDismiss:[4,"can-dismiss"],presented:[32],present:[64],dismiss:[64],onDidDismiss:[64],onWillDismiss:[64],setCurrentBreakpoint:[64],getCurrentBreakpoint:[64]},void 0,{isOpen:["onIsOpenChange"],trigger:["triggerChanged"]}]),P_={ionModalDidPresent:"ionViewDidEnter",ionModalWillPresent:"ionViewWillEnter",ionModalWillDismiss:"ionViewWillLeave",ionModalDidDismiss:"ionViewDidLeave"};function Uv(){if(typeof customElements>"u")return;["ion-modal","ion-backdrop"].forEach(t=>{switch(t){case"ion-modal":customElements.get(t)||customElements.define(t,$v);break;case"ion-backdrop":customElements.get(t)||Ja();break}})}var zv=Uv;var F_=e=>{if(!e)return{arrowWidth:0,arrowHeight:0};let{width:t,height:n}=e.getBoundingClientRect();return{arrowWidth:t,arrowHeight:n}},Wv=(e,t,n)=>{let r=t.getBoundingClientRect(),o=r.height,i=r.width;return e==="cover"&&n&&(i=n.getBoundingClientRect().width),{contentWidth:i,contentHeight:o}},L_=(e,t,n,r)=>{let o=[],s=de(r).querySelector(".popover-content");switch(t){case"hover":o=[{eventName:"mouseenter",callback:a=>{document.elementFromPoint(a.clientX,a.clientY)!==e&&n.dismiss(void 0,void 0,!1)}}];break;case"context-menu":case"click":default:o=[{eventName:"click",callback:a=>{if(a.target.closest("[data-ion-popover-trigger]")===e){a.stopPropagation();return}n.dismiss(void 0,void 0,!1)}}];break}return o.forEach(({eventName:a,callback:c})=>s.addEventListener(a,c)),()=>{o.forEach(({eventName:a,callback:c})=>s.removeEventListener(a,c))}},j_=(e,t,n)=>{let r=[];switch(t){case"hover":let o;r=[{eventName:"mouseenter",callback:i=>b(null,null,function*(){i.stopPropagation(),o&&clearTimeout(o),o=setTimeout(()=>{Rt(()=>{n.presentFromTrigger(i),o=void 0})},100)})},{eventName:"mouseleave",callback:i=>{o&&clearTimeout(o);let s=i.relatedTarget;s&&s.closest("ion-popover")!==n&&n.dismiss(void 0,void 0,!1)}},{eventName:"click",callback:i=>i.stopPropagation()},{eventName:"ionPopoverActivateTrigger",callback:i=>n.presentFromTrigger(i,!0)}];break;case"context-menu":r=[{eventName:"contextmenu",callback:i=>{i.preventDefault(),n.presentFromTrigger(i)}},{eventName:"click",callback:i=>i.stopPropagation()},{eventName:"ionPopoverActivateTrigger",callback:i=>n.presentFromTrigger(i,!0)}];break;case"click":default:r=[{eventName:"click",callback:i=>n.presentFromTrigger(i)},{eventName:"ionPopoverActivateTrigger",callback:i=>n.presentFromTrigger(i,!0)}];break}return r.forEach(({eventName:o,callback:i})=>e.addEventListener(o,i)),e.setAttribute("data-ion-popover-trigger","true"),()=>{r.forEach(({eventName:o,callback:i})=>e.removeEventListener(o,i)),e.removeAttribute("data-ion-popover-trigger")}},qv=(e,t)=>!t||t.tagName!=="ION-ITEM"?-1:e.findIndex(n=>n===t),V_=(e,t)=>{let n=qv(e,t);return e[n+1]},B_=(e,t)=>{let n=qv(e,t);return e[n-1]},tc=e=>{let n=de(e).querySelector("button");n&&Rt(()=>n.focus())},H_=e=>e.hasAttribute("data-ion-popover-trigger"),$_=e=>{let t=n=>b(null,null,function*(){var r;let o=document.activeElement,i=[],s=(r=n.target)===null||r===void 0?void 0:r.tagName;if(!(s!=="ION-POPOVER"&&s!=="ION-ITEM")){try{i=Array.from(e.querySelectorAll("ion-item:not(ion-popover ion-popover *):not([disabled])"))}catch{}switch(n.key){case"ArrowLeft":(yield e.getParentPopover())&&e.dismiss(void 0,void 0,!1);break;case"ArrowDown":n.preventDefault();let c=V_(i,o);c!==void 0&&tc(c);break;case"ArrowUp":n.preventDefault();let l=B_(i,o);l!==void 0&&tc(l);break;case"Home":n.preventDefault();let u=i[0];u!==void 0&&tc(u);break;case"End":n.preventDefault();let f=i[i.length-1];f!==void 0&&tc(f);break;case"ArrowRight":case" ":case"Enter":if(o&&H_(o)){let p=new CustomEvent("ionPopoverActivateTrigger");o.dispatchEvent(p)}break}}});return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)},Zv=(e,t,n,r,o,i,s,a,c,l,u)=>{var f;let p={top:0,left:0,width:0,height:0};switch(i){case"event":if(!u)return c;let Y=u;p={top:Y.clientY,left:Y.clientX,width:1,height:1};break;case"trigger":default:let k=u,me=l||((f=k?.detail)===null||f===void 0?void 0:f.ionShadowTarget)||k?.target;if(!me)return c;let ye=me.getBoundingClientRect();p={top:ye.top,left:ye.left,width:ye.width,height:ye.height};break}let d=G_(s,p,t,n,r,o,e),m=W_(a,s,p,t,n),g=d.top+m.top,v=d.left+m.left,{arrowTop:D,arrowLeft:Z}=z_(s,r,o,g,v,t,n,e),{originX:N,originY:V}=U_(s,a,e);return{top:g,left:v,referenceCoordinates:p,arrowTop:D,arrowLeft:Z,originX:N,originY:V}},U_=(e,t,n)=>{switch(e){case"top":return{originX:Gv(t),originY:"bottom"};case"bottom":return{originX:Gv(t),originY:"top"};case"left":return{originX:"right",originY:nc(t)};case"right":return{originX:"left",originY:nc(t)};case"start":return{originX:n?"left":"right",originY:nc(t)};case"end":return{originX:n?"right":"left",originY:nc(t)}}},Gv=e=>{switch(e){case"start":return"left";case"center":return"center";case"end":return"right"}},nc=e=>{switch(e){case"start":return"top";case"center":return"center";case"end":return"bottom"}},z_=(e,t,n,r,o,i,s,a)=>{let c={arrowTop:r+s/2-t/2,arrowLeft:o+i-t/2},l={arrowTop:r+s/2-t/2,arrowLeft:o-t*1.5};switch(e){case"top":return{arrowTop:r+s,arrowLeft:o+i/2-t/2};case"bottom":return{arrowTop:r-n,arrowLeft:o+i/2-t/2};case"left":return c;case"right":return l;case"start":return a?l:c;case"end":return a?c:l;default:return{arrowTop:0,arrowLeft:0}}},G_=(e,t,n,r,o,i,s)=>{let a={top:t.top,left:t.left-n-o},c={top:t.top,left:t.left+t.width+o};switch(e){case"top":return{top:t.top-r-i,left:t.left};case"right":return c;case"bottom":return{top:t.top+t.height+i,left:t.left};case"left":return a;case"start":return s?c:a;case"end":return s?a:c}},W_=(e,t,n,r,o)=>{switch(e){case"center":return Z_(t,n,r,o);case"end":return q_(t,n,r,o);case"start":default:return{top:0,left:0}}},q_=(e,t,n,r)=>{switch(e){case"start":case"end":case"left":case"right":return{top:-(r-t.height),left:0};case"top":case"bottom":default:return{top:0,left:-(n-t.width)}}},Z_=(e,t,n,r)=>{switch(e){case"start":case"end":case"left":case"right":return{top:-(r/2-t.height/2),left:0};case"top":case"bottom":default:return{top:0,left:-(n/2-t.width/2)}}},Yv=(e,t,n,r,o,i,s,a,c,l,u,f,p=0,d=0,m=0)=>{let g=p,v=d,D=n,Z=t,N,V=l,Y=u,k=!1,me=!1,ye=f?f.top+f.height:i/2-a/2,ge=f?f.height:0,Ne=!1;return D<r+c?(D=r,k=!0,V="left"):s+r+D+c>o&&(me=!0,D=o-s-r,V="right"),ye+ge+a>i&&(e==="top"||e==="bottom")&&(ye-a>0?(Z=Math.max(12,ye-a-ge-(m-1)),g=Z+a,Y="bottom",Ne=!0):N=r),{top:Z,left:D,bottom:N,originX:V,originY:Y,checkSafeAreaLeft:k,checkSafeAreaRight:me,arrowTop:g,arrowLeft:v,addPopoverBottomClass:Ne}},Y_=(e,t=!1,n,r)=>!(!n&&!r||e!=="top"&&e!=="bottom"&&t),Q_=5,K_=(e,t)=>{var n;let{event:r,size:o,trigger:i,reference:s,side:a,align:c}=t,l=e.ownerDocument,u=l.dir==="rtl",f=l.defaultView.innerWidth,p=l.defaultView.innerHeight,d=de(e),m=d.querySelector(".popover-content"),g=d.querySelector(".popover-arrow"),v=i||((n=r?.detail)===null||n===void 0?void 0:n.ionShadowTarget)||r?.target,{contentWidth:D,contentHeight:Z}=Wv(o,m,v),{arrowWidth:N,arrowHeight:V}=F_(g),Y={top:p/2-Z/2,left:f/2-D/2,originX:u?"right":"left",originY:"top"},k=Zv(u,D,Z,N,V,s,a,c,Y,i,r),me=o==="cover"?0:Q_,ye=o==="cover"?0:25,{originX:ge,originY:Ne,top:le,left:ue,bottom:ft,checkSafeAreaLeft:yn,checkSafeAreaRight:Kt,arrowTop:oc,arrowLeft:ic,addPopoverBottomClass:sc}=Yv(a,k.top,k.left,me,f,p,D,Z,ye,k.originX,k.originY,k.referenceCoordinates,k.arrowTop,k.arrowLeft,V),ac=R(),Xr=R(),Dn=R();return Xr.addElement(d.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),Dn.addElement(d.querySelector(".popover-arrow")).addElement(d.querySelector(".popover-content")).fromTo("opacity",.01,1),ac.easing("ease").duration(100).beforeAddWrite(()=>{o==="cover"&&e.style.setProperty("--width",`${D}px`),sc&&e.classList.add("popover-bottom"),ft!==void 0&&m.style.setProperty("bottom",`${ft}px`);let Q=" + var(--ion-safe-area-left, 0)",B=" - var(--ion-safe-area-right, 0)",K=`${ue}px`;if(yn&&(K=`${ue}px${Q}`),Kt&&(K=`${ue}px${B}`),m.style.setProperty("top",`calc(${le}px + var(--offset-y, 0))`),m.style.setProperty("left",`calc(${K} + var(--offset-x, 0))`),m.style.setProperty("transform-origin",`${Ne} ${ge}`),g!==null){let W=k.top!==le||k.left!==ue;Y_(a,W,r,i)?(g.style.setProperty("top",`calc(${oc}px + var(--offset-y, 0))`),g.style.setProperty("left",`calc(${ic}px + var(--offset-x, 0))`)):g.style.setProperty("display","none")}}).addAnimation([Xr,Dn])},X_=e=>{let t=de(e),n=t.querySelector(".popover-content"),r=t.querySelector(".popover-arrow"),o=R(),i=R(),s=R();return i.addElement(t.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",0),s.addElement(t.querySelector(".popover-arrow")).addElement(t.querySelector(".popover-content")).fromTo("opacity",.99,0),o.easing("ease").afterAddWrite(()=>{e.style.removeProperty("--width"),e.classList.remove("popover-bottom"),n.style.removeProperty("top"),n.style.removeProperty("left"),n.style.removeProperty("bottom"),n.style.removeProperty("transform-origin"),r&&(r.style.removeProperty("top"),r.style.removeProperty("left"),r.style.removeProperty("display"))}).duration(300).addAnimation([i,s])},J_=12,eS=(e,t)=>{var n;let{event:r,size:o,trigger:i,reference:s,side:a,align:c}=t,l=e.ownerDocument,u=l.dir==="rtl",f=l.defaultView.innerWidth,p=l.defaultView.innerHeight,d=de(e),m=d.querySelector(".popover-content"),g=i||((n=r?.detail)===null||n===void 0?void 0:n.ionShadowTarget)||r?.target,{contentWidth:v,contentHeight:D}=Wv(o,m,g),Z={top:p/2-D/2,left:f/2-v/2,originX:u?"right":"left",originY:"top"},N=Zv(u,v,D,0,0,s,a,c,Z,i,r),V=o==="cover"?0:J_,{originX:Y,originY:k,top:me,left:ye,bottom:ge}=Yv(a,N.top,N.left,V,f,p,v,D,0,N.originX,N.originY,N.referenceCoordinates),Ne=R(),le=R(),ue=R(),ft=R(),yn=R();return le.addElement(d.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),ue.addElement(d.querySelector(".popover-wrapper")).duration(150).fromTo("opacity",.01,1),ft.addElement(m).beforeStyles({top:`calc(${me}px + var(--offset-y, 0px))`,left:`calc(${ye}px + var(--offset-x, 0px))`,"transform-origin":`${k} ${Y}`}).beforeAddWrite(()=>{ge!==void 0&&m.style.setProperty("bottom",`${ge}px`)}).fromTo("transform","scale(0.8)","scale(1)"),yn.addElement(d.querySelector(".popover-viewport")).fromTo("opacity",.01,1),Ne.easing("cubic-bezier(0.36,0.66,0.04,1)").duration(300).beforeAddWrite(()=>{o==="cover"&&e.style.setProperty("--width",`${v}px`),k==="bottom"&&e.classList.add("popover-bottom")}).addAnimation([le,ue,ft,yn])},tS=e=>{let t=de(e),n=t.querySelector(".popover-content"),r=R(),o=R(),i=R();return o.addElement(t.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",0),i.addElement(t.querySelector(".popover-wrapper")).fromTo("opacity",.99,0),r.easing("ease").afterAddWrite(()=>{e.style.removeProperty("--width"),e.classList.remove("popover-bottom"),n.style.removeProperty("top"),n.style.removeProperty("left"),n.style.removeProperty("bottom"),n.style.removeProperty("transform-origin")}).duration(150).addAnimation([o,i])},nS=':host{--background:var(--ion-background-color, #fff);--min-width:0;--min-height:0;--max-width:auto;--height:auto;--offset-x:0px;--offset-y:0px;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);z-index:1001}:host(.popover-nested){pointer-events:none}:host(.popover-nested) .popover-wrapper{pointer-events:auto}:host(.overlay-hidden){display:none}.popover-wrapper{z-index:10}.popover-content{display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:auto;z-index:10}::slotted(.popover-viewport){--ion-safe-area-top:0px;--ion-safe-area-right:0px;--ion-safe-area-bottom:0px;--ion-safe-area-left:0px;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}:host(.popover-nested.popover-side-left){--offset-x:5px}:host(.popover-nested.popover-side-right){--offset-x:-5px}:host(.popover-nested.popover-side-start){--offset-x:5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-start),:host-context([dir=rtl]).popover-nested.popover-side-start{--offset-x:-5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-start:dir(rtl)){--offset-x:-5px}}:host(.popover-nested.popover-side-end){--offset-x:-5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-end),:host-context([dir=rtl]).popover-nested.popover-side-end{--offset-x:5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-end:dir(rtl)){--offset-x:5px}}:host{--width:200px;--max-height:90%;--box-shadow:none;--backdrop-opacity:var(--ion-backdrop-opacity, 0.08)}:host(.popover-desktop){--box-shadow:0px 4px 16px 0px rgba(0, 0, 0, 0.12)}.popover-content{border-radius:10px}:host(.popover-desktop) .popover-content{border:0.5px solid var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.popover-arrow{display:block;position:absolute;width:20px;height:10px;overflow:hidden;z-index:11}.popover-arrow::after{top:3px;border-radius:3px;position:absolute;width:14px;height:14px;-webkit-transform:rotate(45deg);transform:rotate(45deg);background:var(--background);content:"";z-index:10}.popover-arrow::after{inset-inline-start:3px}:host(.popover-bottom) .popover-arrow{top:auto;bottom:-10px}:host(.popover-bottom) .popover-arrow::after{top:-6px}:host(.popover-side-left) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}:host(.popover-side-right) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}:host(.popover-side-top) .popover-arrow{-webkit-transform:rotate(180deg);transform:rotate(180deg)}:host(.popover-side-start) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}:host-context([dir=rtl]):host(.popover-side-start) .popover-arrow,:host-context([dir=rtl]).popover-side-start .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}@supports selector(:dir(rtl)){:host(.popover-side-start:dir(rtl)) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}}:host(.popover-side-end) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}:host-context([dir=rtl]):host(.popover-side-end) .popover-arrow,:host-context([dir=rtl]).popover-side-end .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}@supports selector(:dir(rtl)){:host(.popover-side-end:dir(rtl)) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}}.popover-arrow,.popover-content{opacity:0}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){:host(.popover-translucent) .popover-content,:host(.popover-translucent) .popover-arrow::after{background:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8);-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}}',rS=":host{--background:var(--ion-background-color, #fff);--min-width:0;--min-height:0;--max-width:auto;--height:auto;--offset-x:0px;--offset-y:0px;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);z-index:1001}:host(.popover-nested){pointer-events:none}:host(.popover-nested) .popover-wrapper{pointer-events:auto}:host(.overlay-hidden){display:none}.popover-wrapper{z-index:10}.popover-content{display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:auto;z-index:10}::slotted(.popover-viewport){--ion-safe-area-top:0px;--ion-safe-area-right:0px;--ion-safe-area-bottom:0px;--ion-safe-area-left:0px;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}:host(.popover-nested.popover-side-left){--offset-x:5px}:host(.popover-nested.popover-side-right){--offset-x:-5px}:host(.popover-nested.popover-side-start){--offset-x:5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-start),:host-context([dir=rtl]).popover-nested.popover-side-start{--offset-x:-5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-start:dir(rtl)){--offset-x:-5px}}:host(.popover-nested.popover-side-end){--offset-x:-5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-end),:host-context([dir=rtl]).popover-nested.popover-side-end{--offset-x:5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-end:dir(rtl)){--offset-x:5px}}:host{--width:250px;--max-height:90%;--box-shadow:0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);--backdrop-opacity:var(--ion-backdrop-opacity, 0.32)}.popover-content{border-radius:4px;-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]) .popover-content{-webkit-transform-origin:right top;transform-origin:right top}[dir=rtl] .popover-content{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.popover-content:dir(rtl){-webkit-transform-origin:right top;transform-origin:right top}}.popover-viewport{-webkit-transition-delay:100ms;transition-delay:100ms}.popover-wrapper{opacity:0}",Qv=Ce(class extends De{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.didPresent=H(this,"ionPopoverDidPresent",7),this.willPresent=H(this,"ionPopoverWillPresent",7),this.willDismiss=H(this,"ionPopoverWillDismiss",7),this.didDismiss=H(this,"ionPopoverDidDismiss",7),this.didPresentShorthand=H(this,"didPresent",7),this.willPresentShorthand=H(this,"willPresent",7),this.willDismissShorthand=H(this,"willDismiss",7),this.didDismissShorthand=H(this,"didDismiss",7),this.ionMount=H(this,"ionMount",7),this.parentPopover=null,this.coreDelegate=Ua(),this.lockController=Qr(),this.inline=!1,this.focusDescendantOnPresent=!1,this.presented=!1,this.hasController=!1,this.keyboardClose=!0,this.backdropDismiss=!0,this.showBackdrop=!0,this.translucent=!1,this.animated=!0,this.triggerAction="click",this.size="auto",this.dismissOnSelect=!1,this.reference="trigger",this.side="bottom",this.arrow=!0,this.isOpen=!1,this.keyboardEvents=!1,this.focusTrap=!0,this.keepContentsMounted=!1,this.onBackdropTap=()=>{this.dismiss(void 0,Wr)},this.onLifecycle=t=>{let n=this.usersElement,r=oS[t.type];if(n&&r){let o=new CustomEvent(r,{bubbles:!1,cancelable:!1,detail:t.detail});n.dispatchEvent(o)}},this.configureTriggerInteraction=()=>{let{trigger:t,triggerAction:n,el:r,destroyTriggerInteraction:o}=this;if(o&&o(),t===void 0)return;let i=this.triggerEl=t!==void 0?document.getElementById(t):null;if(!i){Qe(`[ion-popover] - A trigger element with the ID "${t}" was not found in the DOM. The trigger element must be in the DOM when the "trigger" property is set on ion-popover.`,this.el);return}this.destroyTriggerInteraction=j_(i,n,r)},this.configureKeyboardInteraction=()=>{let{destroyKeyboardInteraction:t,el:n}=this;t&&t(),this.destroyKeyboardInteraction=$_(n)},this.configureDismissInteraction=()=>{let{destroyDismissInteraction:t,parentPopover:n,triggerAction:r,triggerEl:o,el:i}=this;!n||!o||(t&&t(),this.destroyDismissInteraction=L_(o,r,i,n))}}onTriggerChange(){this.configureTriggerInteraction()}onIsOpenChange(t,n){t===!0&&n===!1?this.present():t===!1&&n===!0&&this.dismiss()}connectedCallback(){let{configureTriggerInteraction:t,el:n}=this;Wa(n),t()}disconnectedCallback(){let{destroyTriggerInteraction:t}=this;t&&t()}componentWillLoad(){var t,n;let{el:r}=this,o=(n=(t=this.htmlAttributes)===null||t===void 0?void 0:t.id)!==null&&n!==void 0?n:qa(r);this.parentPopover=r.closest(`ion-popover:not(#${o})`),this.alignment===void 0&&(this.alignment=J(this)==="ios"?"center":"start")}componentDidLoad(){let{parentPopover:t,isOpen:n}=this;n===!0&&Rt(()=>this.present()),t&&_i(t,"ionPopoverWillDismiss",()=>{this.dismiss(void 0,void 0,!1)}),this.configureTriggerInteraction()}presentFromTrigger(t,n=!1){return b(this,null,function*(){this.focusDescendantOnPresent=n,yield this.present(t),this.focusDescendantOnPresent=!1})}getDelegate(t=!1){if(this.workingDelegate&&!t)return{delegate:this.workingDelegate,inline:this.inline};let n=this.el.parentNode,r=this.inline=n!==null&&!this.hasController,o=this.workingDelegate=r?this.delegate||this.coreDelegate:this.delegate;return{inline:r,delegate:o}}present(t){return b(this,null,function*(){let n=yield this.lockController.lock();if(this.presented){n();return}let{el:r}=this,{inline:o,delegate:i}=this.getDelegate(!0);this.ionMount.emit(),this.usersElement=yield zr(i,r,this.component,["popover-viewport"],this.componentProps,o),this.keyboardEvents||this.configureKeyboardInteraction(),this.configureDismissInteraction(),At(r)?yield Ti(this.usersElement):this.keepContentsMounted||(yield Si()),yield Ya(this,"popoverEnter",K_,eS,{event:t||this.event,size:this.size,trigger:this.triggerEl,reference:this.reference,side:this.side,align:this.alignment}),this.focusDescendantOnPresent&&za(r),n()})}dismiss(t,n,r=!0){return b(this,null,function*(){let o=yield this.lockController.lock(),{destroyKeyboardInteraction:i,destroyDismissInteraction:s}=this;r&&this.parentPopover&&this.parentPopover.dismiss(t,n,r);let a=yield Qa(this,t,n,"popoverLeave",X_,tS,this.event);if(a){i&&(i(),this.destroyKeyboardInteraction=void 0),s&&(s(),this.destroyDismissInteraction=void 0);let{delegate:c}=this.getDelegate();yield Gr(c,this.usersElement)}return o(),a})}getParentPopover(){return b(this,null,function*(){return this.parentPopover})}onDidDismiss(){return qr(this.el,"ionPopoverDidDismiss")}onWillDismiss(){return qr(this.el,"ionPopoverWillDismiss")}render(){let t=J(this),{onLifecycle:n,parentPopover:r,dismissOnSelect:o,side:i,arrow:s,htmlAttributes:a,focusTrap:c}=this,l=Te("desktop"),u=s&&!r;return T(xe,Object.assign({key:"1de4862099cfcb5035e78008e6dc7c1371846f9a","aria-modal":"true","no-router":!0,tabindex:"-1"},a,{style:{zIndex:`${2e4+this.overlayIndex}`},class:Object.assign(Object.assign({},$a(this.cssClass)),{[t]:!0,"popover-translucent":this.translucent,"overlay-hidden":!0,"popover-desktop":l,[`popover-side-${i}`]:!0,[Wn]:c===!1,"popover-nested":!!r}),onIonPopoverDidPresent:n,onIonPopoverWillPresent:n,onIonPopoverWillDismiss:n,onIonPopoverDidDismiss:n,onIonBackdropTap:this.onBackdropTap}),!r&&T("ion-backdrop",{key:"981aa4e0102cb93312ffbd8243cdf2a0cdc60469",tappable:this.backdropDismiss,visible:this.showBackdrop,part:"backdrop"}),T("div",{key:"1a28ed55e9d34ef78cf0eb0178643301fd2dd75d",class:"popover-wrapper ion-overlay-wrapper",onClick:o?()=>this.dismiss():void 0},u&&T("div",{key:"1c206ea5eb3c0b5883a3d45c34cd22dd5ffe4b65",class:"popover-arrow",part:"arrow"}),T("div",{key:"5ba561486a328c0c7ab825995fdbfb7a196429a4",class:"popover-content",part:"content"},T("slot",{key:"00fc244ce9dcc2dfc677e6c34b7c8e7a330b2b03"}))))}get el(){return this}static get watchers(){return{trigger:["onTriggerChange"],triggerAction:["onTriggerChange"],isOpen:["onIsOpenChange"]}}static get style(){return{ios:nS,md:rS}}},[33,"ion-popover",{hasController:[4,"has-controller"],delegate:[16],overlayIndex:[2,"overlay-index"],enterAnimation:[16,"enter-animation"],leaveAnimation:[16,"leave-animation"],component:[1],componentProps:[16,"component-props"],keyboardClose:[4,"keyboard-close"],cssClass:[1,"css-class"],backdropDismiss:[4,"backdrop-dismiss"],event:[8],showBackdrop:[4,"show-backdrop"],translucent:[4],animated:[4],htmlAttributes:[16,"html-attributes"],triggerAction:[1,"trigger-action"],trigger:[1],size:[1],dismissOnSelect:[4,"dismiss-on-select"],reference:[1],side:[1],alignment:[1025],arrow:[4],isOpen:[4,"is-open"],keyboardEvents:[4,"keyboard-events"],focusTrap:[4,"focus-trap"],keepContentsMounted:[4,"keep-contents-mounted"],presented:[32],presentFromTrigger:[64],present:[64],dismiss:[64],getParentPopover:[64],onDidDismiss:[64],onWillDismiss:[64]},void 0,{trigger:["onTriggerChange"],triggerAction:["onTriggerChange"],isOpen:["onIsOpenChange"]}]),oS={ionPopoverDidPresent:"ionViewDidEnter",ionPopoverWillPresent:"ionViewWillEnter",ionPopoverWillDismiss:"ionViewWillLeave",ionPopoverDidDismiss:"ionViewDidLeave"};function Kv(){if(typeof customElements>"u")return;["ion-popover","ion-backdrop"].forEach(t=>{switch(t){case"ion-popover":customElements.get(t)||customElements.define(t,Qv);break;case"ion-backdrop":customElements.get(t)||Ja();break}})}var Xv=Kv;var iS="html.plt-mobile ion-app{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}html.plt-mobile ion-app [contenteditable]{-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text}ion-app.force-statusbar-padding{--ion-safe-area-top:20px}",sS=Ce(class extends De{constructor(){super(),this.__registerHost()}componentDidLoad(){Ii.isBrowser&&cS(()=>b(this,null,function*(){let t=Te(window,"hybrid");if(X.getBoolean("_testing")||import("./chunk-3HHWTXPD.js").then(o=>o.startTapClick(X)),X.getBoolean("statusTap",t)&&import("./chunk-SCO5SHIW.js").then(o=>o.startStatusTap()),X.getBoolean("inputShims",aS())){let o=Te(window,"ios")?"ios":"android";import("./chunk-ZXPIQBF5.js").then(i=>i.startInputShims(X,o))}let n=yield import("./chunk-VCHLP75Z.js"),r=t||ro();X.getBoolean("hardwareBackButton",r)?n.startHardwareBackButton():(ro()&&Qe("[ion-app] - experimentalCloseWatcher was set to `true`, but hardwareBackButton was set to `false`. Both config options must be `true` for the Close Watcher API to be used."),n.blockHardwareBackButton()),typeof window<"u"&&import("./chunk-R5SHVWC7.js").then(o=>o.startKeyboardAssist(window)),import("./chunk-FA25AEA4.js").then(o=>this.focusVisible=o.startFocusVisible())}))}setFocus(t){return b(this,null,function*(){this.focusVisible&&this.focusVisible.setFocus(t)})}render(){let t=J(this);return T(xe,{key:"03aa892f986330078d112b1e8b010df98fa7e39e",class:{[t]:!0,"ion-page":!0,"force-statusbar-padding":X.getBoolean("_forceStatusbarPadding")}})}get el(){return this}static get style(){return iS}},[0,"ion-app",{setFocus:[64]}]),aS=()=>!!(Te(window,"ios")&&Te(window,"mobile")||Te(window,"android")&&Te(window,"mobileweb")),cS=e=>{"requestIdleCallback"in window?window.requestIdleCallback(e):setTimeout(e,32)};function lS(){if(typeof customElements>"u")return;["ion-app"].forEach(t=>{switch(t){case"ion-app":customElements.get(t)||customElements.define(t,sS);break}})}var Jv=lS;var uS=':host{--background:var(--ion-background-color, #fff);--color:var(--ion-text-color, #000);--padding-top:0px;--padding-bottom:0px;--padding-start:0px;--padding-end:0px;--keyboard-offset:0px;--offset-top:0px;--offset-bottom:0px;--overflow:auto;display:block;position:relative;-ms-flex:1;flex:1;width:100%;height:100%;margin:0 !important;padding:0 !important;font-family:var(--ion-font-family, inherit);contain:size style}:host(.ion-color) .inner-scroll{background:var(--ion-color-base);color:var(--ion-color-contrast)}#background-content{left:0px;right:0px;top:calc(var(--offset-top) * -1);bottom:calc(var(--offset-bottom) * -1);position:absolute;background:var(--background)}.inner-scroll{left:0px;right:0px;top:calc(var(--offset-top) * -1);bottom:calc(var(--offset-bottom) * -1);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:calc(var(--padding-top) + var(--offset-top));padding-bottom:calc(var(--padding-bottom) + var(--keyboard-offset) + var(--offset-bottom));position:absolute;color:var(--color);-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden;-ms-touch-action:pan-x pan-y pinch-zoom;touch-action:pan-x pan-y pinch-zoom}.scroll-y,.scroll-x{-webkit-overflow-scrolling:touch;z-index:0;will-change:scroll-position}.scroll-y{overflow-y:var(--overflow);overscroll-behavior-y:contain}.scroll-x{overflow-x:var(--overflow);overscroll-behavior-x:contain}.overscroll::before,.overscroll::after{position:absolute;width:1px;height:1px;content:""}.overscroll::before{bottom:-1px}.overscroll::after{top:-1px}:host(.content-sizing){display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;min-height:0;contain:none}:host(.content-sizing) .inner-scroll{position:relative;top:0;bottom:0;margin-top:calc(var(--offset-top) * -1);margin-bottom:calc(var(--offset-bottom) * -1)}.transition-effect{display:none;position:absolute;width:100%;height:100vh;opacity:0;pointer-events:none}:host(.content-ltr) .transition-effect{left:-100%;}:host(.content-rtl) .transition-effect{right:-100%;}.transition-cover{position:absolute;right:0;width:100%;height:100%;background:black;opacity:0.1}.transition-shadow{display:block;position:absolute;width:100%;height:100%;-webkit-box-shadow:inset -9px 0 9px 0 rgba(0, 0, 100, 0.03);box-shadow:inset -9px 0 9px 0 rgba(0, 0, 100, 0.03)}:host(.content-ltr) .transition-shadow{right:0;}:host(.content-rtl) .transition-shadow{left:0;-webkit-transform:scaleX(-1);transform:scaleX(-1)}::slotted([slot=fixed]){position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0)}',ey=Ce(class extends De{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionScrollStart=H(this,"ionScrollStart",7),this.ionScroll=H(this,"ionScroll",7),this.ionScrollEnd=H(this,"ionScrollEnd",7),this.watchDog=null,this.isScrolling=!1,this.lastScroll=0,this.queued=!1,this.cTop=-1,this.cBottom=-1,this.isMainContent=!0,this.resizeTimeout=null,this.inheritedAttributes={},this.tabsElement=null,this.detail={scrollTop:0,scrollLeft:0,type:"scroll",event:void 0,startX:0,startY:0,startTime:0,currentX:0,currentY:0,velocityX:0,velocityY:0,deltaX:0,deltaY:0,currentTime:0,data:void 0,isScrolling:!0},this.fullscreen=!1,this.fixedSlotPlacement="after",this.scrollX=!1,this.scrollY=!0,this.scrollEvents=!1}componentWillLoad(){this.inheritedAttributes=wi(this.el)}connectedCallback(){if(this.isMainContent=this.el.closest("ion-menu, ion-popover, ion-modal")===null,At(this.el)){let t=this.tabsElement=this.el.closest("ion-tabs");t!==null&&(this.tabsLoadCallback=()=>this.resize(),t.addEventListener("ionTabBarLoaded",this.tabsLoadCallback))}}disconnectedCallback(){if(this.onScrollEnd(),At(this.el)){let{tabsElement:t,tabsLoadCallback:n}=this;t!==null&&n!==void 0&&t.removeEventListener("ionTabBarLoaded",n),this.tabsElement=null,this.tabsLoadCallback=void 0}}onResize(){this.resizeTimeout&&(clearTimeout(this.resizeTimeout),this.resizeTimeout=null),this.resizeTimeout=setTimeout(()=>{this.el.offsetParent!==null&&this.resize()},100)}shouldForceOverscroll(){let{forceOverscroll:t}=this,n=J(this);return t===void 0?n==="ios"&&Te("ios"):t}resize(){Ii.isBrowser&&(this.fullscreen?qn(()=>this.readDimensions()):(this.cTop!==0||this.cBottom!==0)&&(this.cTop=this.cBottom=0,to(this)))}readDimensions(){let t=fS(this.el),n=Math.max(this.el.offsetTop,0),r=Math.max(t.offsetHeight-n-this.el.offsetHeight,0);(n!==this.cTop||r!==this.cBottom)&&(this.cTop=n,this.cBottom=r,to(this))}onScroll(t){let n=Date.now(),r=!this.isScrolling;this.lastScroll=n,r&&this.onScrollStart(),!this.queued&&this.scrollEvents&&(this.queued=!0,qn(o=>{this.queued=!1,this.detail.event=t,pS(this.detail,this.scrollEl,o,r),this.ionScroll.emit(this.detail)}))}getScrollElement(){return b(this,null,function*(){return this.scrollEl||(yield new Promise(t=>rt(this.el,t))),Promise.resolve(this.scrollEl)})}getBackgroundElement(){return b(this,null,function*(){return this.backgroundContentEl||(yield new Promise(t=>rt(this.el,t))),Promise.resolve(this.backgroundContentEl)})}scrollToTop(t=0){return this.scrollToPoint(void 0,0,t)}scrollToBottom(t=0){return b(this,null,function*(){let n=yield this.getScrollElement(),r=n.scrollHeight-n.clientHeight;return this.scrollToPoint(void 0,r,t)})}scrollByPoint(t,n,r){return b(this,null,function*(){let o=yield this.getScrollElement();return this.scrollToPoint(t+o.scrollLeft,n+o.scrollTop,r)})}scrollToPoint(t,n,r=0){return b(this,null,function*(){let o=yield this.getScrollElement();if(r<32){n!=null&&(o.scrollTop=n),t!=null&&(o.scrollLeft=t);return}let i,s=0,a=new Promise(d=>i=d),c=o.scrollTop,l=o.scrollLeft,u=n!=null?n-c:0,f=t!=null?t-l:0,p=d=>{let m=Math.min(1,(d-s)/r)-1,g=Math.pow(m,3)+1;u!==0&&(o.scrollTop=Math.floor(g*u+c)),f!==0&&(o.scrollLeft=Math.floor(g*f+l)),g<1?requestAnimationFrame(p):i()};return requestAnimationFrame(d=>{s=d,p(d)}),a})}onScrollStart(){this.isScrolling=!0,this.ionScrollStart.emit({isScrolling:!0}),this.watchDog&&clearInterval(this.watchDog),this.watchDog=setInterval(()=>{this.lastScroll<Date.now()-120&&this.onScrollEnd()},100)}onScrollEnd(){this.watchDog&&clearInterval(this.watchDog),this.watchDog=null,this.isScrolling&&(this.isScrolling=!1,this.ionScrollEnd.emit({isScrolling:!1}))}render(){let{fixedSlotPlacement:t,inheritedAttributes:n,isMainContent:r,scrollX:o,scrollY:i,el:s}=this,a=lf(s)?"rtl":"ltr",c=J(this),l=this.shouldForceOverscroll(),u=c==="ios";return this.resize(),T(xe,Object.assign({key:"f2a24aa66dbf5c76f9d4b06f708eb73cadc239df",role:r?"main":void 0,class:Ur(this.color,{[c]:!0,"content-sizing":$r("ion-popover",this.el),overscroll:l,[`content-${a}`]:!0}),style:{"--offset-top":`${this.cTop}px`,"--offset-bottom":`${this.cBottom}px`}},n),T("div",{key:"6480ca7648b278abb36477b3838bccbcd4995e2a",ref:f=>this.backgroundContentEl=f,id:"background-content",part:"background"}),t==="before"?T("slot",{name:"fixed"}):null,T("div",{key:"29a23b663f5f0215bb000820c01e1814c0d55985",class:{"inner-scroll":!0,"scroll-x":o,"scroll-y":i,overscroll:(o||i)&&l},ref:f=>this.scrollEl=f,onScroll:this.scrollEvents?f=>this.onScroll(f):void 0,part:"scroll"},T("slot",{key:"0fe1bd05609a4b88ae2ce9addf5d5dc5dc1806f0"})),u?T("div",{class:"transition-effect"},T("div",{class:"transition-cover"}),T("div",{class:"transition-shadow"})):null,t==="after"?T("slot",{name:"fixed"}):null)}get el(){return this}static get style(){return uS}},[1,"ion-content",{color:[513],fullscreen:[4],fixedSlotPlacement:[1,"fixed-slot-placement"],forceOverscroll:[1028,"force-overscroll"],scrollX:[4,"scroll-x"],scrollY:[4,"scroll-y"],scrollEvents:[4,"scroll-events"],getScrollElement:[64],getBackgroundElement:[64],scrollToTop:[64],scrollToBottom:[64],scrollByPoint:[64],scrollToPoint:[64]},[[9,"resize","onResize"]]]),dS=e=>{var t;return e.parentElement?e.parentElement:!((t=e.parentNode)===null||t===void 0)&&t.host?e.parentNode.host:null},fS=e=>{let t=e.closest("ion-tabs");if(t)return t;let n=e.closest("ion-app, ion-page, .ion-page, page-inner, .popover-content");return n||dS(e)},pS=(e,t,n,r)=>{let o=e.currentX,i=e.currentY,s=e.currentTime,a=t.scrollLeft,c=t.scrollTop,l=n-s;if(r&&(e.startTime=n,e.startX=a,e.startY=c,e.velocityX=e.velocityY=0),e.currentTime=n,e.currentX=e.scrollLeft=a,e.currentY=e.scrollTop=c,e.deltaX=a-e.startX,e.deltaY=c-e.startY,l>0&&l<100){let u=(a-o)/l,f=(c-i)/l;e.velocityX=u*.7+e.velocityX*.3,e.velocityY=f*.7+e.velocityY*.3}};function ty(){if(typeof customElements>"u")return;["ion-content"].forEach(t=>{switch(t){case"ion-content":customElements.get(t)||customElements.define(t,ey);break}})}var ny=ty;var hS="all 0.2s ease-in-out",ry=e=>{let t=document.querySelector(`${e}.ion-cloned-element`);if(t!==null)return t;let n=document.createElement(e);return n.classList.add("ion-cloned-element"),n.style.setProperty("display","none"),document.body.appendChild(n),n},oy=e=>{if(!e)return;let t=e.querySelectorAll("ion-toolbar");return{el:e,toolbars:Array.from(t).map(n=>{let r=n.querySelector("ion-title");return{el:n,background:n.shadowRoot.querySelector(".toolbar-background"),ionTitleEl:r,innerTitleEl:r?r.shadowRoot.querySelector(".toolbar-title"):null,ionButtonsEl:Array.from(n.querySelectorAll("ion-buttons"))}})}},mS=(e,t,n)=>{qn(()=>{let r=e.scrollTop,o=Jt(1,1+-r/500,1.1);n.querySelector("ion-refresher.refresher-native")===null&&xt(()=>{yS(t.toolbars,o)})})},zd=(e,t)=>{e.collapse!=="fade"&&(t===void 0?e.style.removeProperty("--opacity-scale"):e.style.setProperty("--opacity-scale",t.toString()))},gS=(e,t,n)=>{if(!e[0].isIntersecting)return;let r=e[0].intersectionRatio>.9||n<=0?0:(1-e[0].intersectionRatio)*100/75;zd(t.el,r===1?void 0:r)},vS=(e,t,n,r)=>{xt(()=>{let o=r.scrollTop;gS(e,t,o);let i=e[0],s=i.intersectionRect,a=s.width*s.height,c=i.rootBounds.width*i.rootBounds.height,l=a===0&&c===0,u=Math.abs(s.left-i.boundingClientRect.left),f=Math.abs(s.right-i.boundingClientRect.right),p=a>0&&(u>=5||f>=5);l||p||(i.isIntersecting?(Ci(t,!1),Ci(n)):(s.x===0&&s.y===0||s.width!==0&&s.height!==0)&&o>0&&(Ci(t),Ci(n,!1),zd(t.el)))})},Ci=(e,t=!0)=>{let n=e.el,o=e.toolbars.map(i=>i.ionTitleEl);t?(n.classList.remove("header-collapse-condense-inactive"),o.forEach(i=>{i&&i.removeAttribute("aria-hidden")})):(n.classList.add("header-collapse-condense-inactive"),o.forEach(i=>{i&&i.setAttribute("aria-hidden","true")}))},yS=(e=[],t=1,n=!1)=>{e.forEach(r=>{let o=r.ionTitleEl,i=r.innerTitleEl;!o||o.size!=="large"||(i.style.transition=n?hS:"",i.style.transform=`scale3d(${t}, ${t}, 1)`)})},iy=(e,t,n)=>{qn(()=>{let r=e.scrollTop,o=t.clientHeight,i=n?n.clientHeight:0;if(n!==null&&r<i){t.style.setProperty("--opacity-scale","0"),e.style.setProperty("clip-path",`inset(${o}px 0px 0px 0px)`);return}let s=r-i,c=Jt(0,s/10,1);xt(()=>{e.style.removeProperty("clip-path"),t.style.setProperty("--opacity-scale",c.toString())})})},DS="ion-header{display:block;position:relative;-ms-flex-order:-1;order:-1;width:100%;z-index:10}ion-header ion-toolbar:first-of-type{padding-top:var(--ion-safe-area-top, 0)}.header-ios ion-toolbar:last-of-type{--border-width:0 0 0.55px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.header-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}.header-translucent-ios ion-toolbar{--opacity:.8}.header-collapse-condense-inactive .header-background{-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px)}}.header-ios.ion-no-border ion-toolbar:last-of-type{--border-width:0}.header-collapse-fade ion-toolbar{--opacity-scale:inherit}.header-collapse-condense{z-index:9}.header-collapse-condense ion-toolbar{position:-webkit-sticky;position:sticky;top:0}.header-collapse-condense ion-toolbar:first-of-type{padding-top:0px;z-index:1}.header-collapse-condense ion-toolbar{--background:var(--ion-background-color, #fff);z-index:0}.header-collapse-condense ion-toolbar:last-of-type{--border-width:0px}.header-collapse-condense ion-toolbar ion-searchbar{padding-top:0px;padding-bottom:13px}.header-collapse-main{--opacity-scale:1}.header-collapse-main ion-toolbar{--opacity-scale:inherit}.header-collapse-main ion-toolbar.in-toolbar ion-title,.header-collapse-main ion-toolbar.in-toolbar ion-buttons{-webkit-transition:all 0.2s ease-in-out;transition:all 0.2s ease-in-out}.header-collapse-condense-inactive:not(.header-collapse-condense) ion-toolbar.in-toolbar ion-title,.header-collapse-condense-inactive:not(.header-collapse-condense) ion-toolbar.in-toolbar ion-buttons.buttons-collapse{opacity:0;pointer-events:none}.header-collapse-condense-inactive.header-collapse-condense ion-toolbar.in-toolbar ion-title,.header-collapse-condense-inactive.header-collapse-condense ion-toolbar.in-toolbar ion-buttons.buttons-collapse{visibility:hidden}ion-header.header-ios:not(.header-collapse-main):has(~ion-content ion-header.header-ios[collapse=condense],~ion-content ion-header.header-ios.header-collapse-condense){opacity:0}",CS="ion-header{display:block;position:relative;-ms-flex-order:-1;order:-1;width:100%;z-index:10}ion-header ion-toolbar:first-of-type{padding-top:var(--ion-safe-area-top, 0)}.header-md{-webkit-box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12)}.header-collapse-condense{display:none}.header-md.ion-no-border{-webkit-box-shadow:none;box-shadow:none}",sy=Ce(class extends De{constructor(){super(),this.__registerHost(),this.inheritedAttributes={},this.translucent=!1,this.setupFadeHeader=(t,n)=>b(this,null,function*(){let r=this.scrollEl=yield gc(t);this.contentScrollCallback=()=>{iy(this.scrollEl,this.el,n)},r.addEventListener("scroll",this.contentScrollCallback),iy(this.scrollEl,this.el,n)})}componentWillLoad(){this.inheritedAttributes=wi(this.el)}componentDidLoad(){this.checkCollapsibleHeader()}componentDidUpdate(){this.checkCollapsibleHeader()}disconnectedCallback(){this.destroyCollapsibleHeader()}checkCollapsibleHeader(){return b(this,null,function*(){if(J(this)!=="ios")return;let{collapse:n}=this,r=n==="condense",o=n==="fade";if(this.destroyCollapsibleHeader(),r){let i=this.el.closest("ion-app,ion-page,.ion-page,page-inner"),s=i?oo(i):null;xt(()=>{let a=ry("ion-title");a.size="large",ry("ion-back-button")}),yield this.setupCondenseHeader(s,i)}else if(o){let i=this.el.closest("ion-app,ion-page,.ion-page,page-inner"),s=i?oo(i):null;if(!s){io(this.el);return}let a=s.querySelector('ion-header[collapse="condense"]');yield this.setupFadeHeader(s,a)}})}destroyCollapsibleHeader(){this.intersectionObserver&&(this.intersectionObserver.disconnect(),this.intersectionObserver=void 0),this.scrollEl&&this.contentScrollCallback&&(this.scrollEl.removeEventListener("scroll",this.contentScrollCallback),this.contentScrollCallback=void 0),this.collapsibleMainHeader&&(this.collapsibleMainHeader.classList.remove("header-collapse-main"),this.collapsibleMainHeader=void 0)}setupCondenseHeader(t,n){return b(this,null,function*(){if(!t||!n){io(this.el);return}if(typeof IntersectionObserver>"u")return;this.scrollEl=yield gc(t);let r=n.querySelectorAll("ion-header");if(this.collapsibleMainHeader=Array.from(r).find(a=>a.collapse!=="condense"),!this.collapsibleMainHeader)return;let o=oy(this.collapsibleMainHeader),i=oy(this.el);if(!o||!i)return;Ci(o,!1),zd(o.el,0);let s=a=>{vS(a,o,i,this.scrollEl)};this.intersectionObserver=new IntersectionObserver(s,{root:t,threshold:[.25,.3,.4,.5,.6,.7,.8,.9,1]}),this.intersectionObserver.observe(i.toolbars[i.toolbars.length-1].el),this.contentScrollCallback=()=>{mS(this.scrollEl,i,t)},this.scrollEl.addEventListener("scroll",this.contentScrollCallback),xt(()=>{this.collapsibleMainHeader!==void 0&&this.collapsibleMainHeader.classList.add("header-collapse-main")})})}render(){let{translucent:t,inheritedAttributes:n}=this,r=J(this),o=this.collapse||"none",i=$r("ion-menu",this.el)?"none":"banner";return T(xe,Object.assign({key:"b6cc27f0b08afc9fcc889683525da765d80ba672",role:i,class:{[r]:!0,[`header-${r}`]:!0,"header-translucent":this.translucent,[`header-collapse-${o}`]:!0,[`header-translucent-${r}`]:this.translucent}},n),r==="ios"&&t&&T("div",{key:"395766d4dcee3398bc91960db21f922095292f14",class:"header-background"}),T("slot",{key:"09a67ece27b258ff1248805d43d92a49b2c6859a"}))}get el(){return this}static get style(){return{ios:DS,md:CS}}},[36,"ion-header",{collapse:[1],translucent:[4]}]);function ay(){if(typeof customElements>"u")return;["ion-header"].forEach(t=>{switch(t){case"ion-header":customElements.get(t)||customElements.define(t,sy);break}})}var cy=ay;var bS=":host{--color:initial;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}.toolbar-title{display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;pointer-events:auto}:host(.title-small) .toolbar-title{white-space:normal}:host{top:0;-webkit-padding-start:90px;padding-inline-start:90px;-webkit-padding-end:90px;padding-inline-end:90px;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);position:absolute;width:100%;height:100%;-webkit-transform:translateZ(0);transform:translateZ(0);font-size:min(1.0625rem, 20.4px);font-weight:600;text-align:center;-webkit-box-sizing:border-box;box-sizing:border-box;pointer-events:none}:host{inset-inline-start:0}:host(.title-small){-webkit-padding-start:9px;padding-inline-start:9px;-webkit-padding-end:9px;padding-inline-end:9px;padding-top:6px;padding-bottom:16px;position:relative;font-size:min(0.8125rem, 23.4px);font-weight:normal}:host(.title-large){-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:2px;padding-bottom:4px;-webkit-transform-origin:left center;transform-origin:left center;position:static;-ms-flex-align:end;align-items:flex-end;min-width:100%;font-size:min(2.125rem, 61.2px);font-weight:700;text-align:start}:host(.title-large.title-rtl){-webkit-transform-origin:right center;transform-origin:right center}:host(.title-large.ion-cloned-element){--color:var(--ion-text-color, #000);font-family:var(--ion-font-family)}:host(.title-large) .toolbar-title{-webkit-transform-origin:inherit;transform-origin:inherit;width:auto}:host-context([dir=rtl]):host(.title-large) .toolbar-title,:host-context([dir=rtl]).title-large .toolbar-title{-webkit-transform-origin:calc(100% - inherit);transform-origin:calc(100% - inherit)}@supports selector(:dir(rtl)){:host(.title-large:dir(rtl)) .toolbar-title{-webkit-transform-origin:calc(100% - inherit);transform-origin:calc(100% - inherit)}}",IS=":host{--color:initial;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}.toolbar-title{display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;pointer-events:auto}:host(.title-small) .toolbar-title{white-space:normal}:host{-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:20px;padding-inline-end:20px;padding-top:0;padding-bottom:0;font-size:1.25rem;font-weight:500;letter-spacing:0.0125em}:host(.title-small){width:100%;height:100%;font-size:0.9375rem;font-weight:normal}",ly=Ce(class extends De{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionStyle=H(this,"ionStyle",7)}sizeChanged(){this.emitStyle()}connectedCallback(){this.emitStyle()}emitStyle(){let t=this.getSize();this.ionStyle.emit({[`title-${t}`]:!0})}getSize(){return this.size!==void 0?this.size:"default"}render(){let t=J(this),n=this.getSize();return T(xe,{key:"e599c0bf1b0817df3fa8360bdcd6d787f751c371",class:Ur(this.color,{[t]:!0,[`title-${n}`]:!0,"title-rtl":document.dir==="rtl"})},T("div",{key:"6e7eee9047d6759876bb31d7305b76efc7c4338c",class:"toolbar-title"},T("slot",{key:"bf790eb4c83dd0af4f2fd1f85ab4af5819f46ff4"})))}get el(){return this}static get watchers(){return{size:["sizeChanged"]}}static get style(){return{ios:bS,md:IS}}},[33,"ion-title",{color:[513],size:[1]},void 0,{size:["sizeChanged"]}]);function uy(){if(typeof customElements>"u")return;["ion-title"].forEach(t=>{switch(t){case"ion-title":customElements.get(t)||customElements.define(t,ly);break}})}var ES=":host{--border-width:0;--border-style:solid;--opacity:1;--opacity-scale:1;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;width:100%;padding-right:var(--ion-safe-area-right);padding-left:var(--ion-safe-area-left);color:var(--color);font-family:var(--ion-font-family, inherit);contain:content;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-contrast)}:host(.ion-color) .toolbar-background{background:var(--ion-color-base)}.toolbar-container{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:var(--min-height);contain:content;overflow:hidden;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}.toolbar-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;opacity:calc(var(--opacity) * var(--opacity-scale));z-index:-1;pointer-events:none}::slotted(ion-progress-bar){left:0;right:0;bottom:0;position:absolute}:host{--background:var(--ion-toolbar-background, var(--ion-color-step-50, var(--ion-background-color-step-50, #f7f7f7)));--color:var(--ion-toolbar-color, var(--ion-text-color, #000));--border-color:var(--ion-toolbar-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.2)))));--padding-top:3px;--padding-bottom:3px;--padding-start:4px;--padding-end:4px;--min-height:44px}.toolbar-content{-ms-flex:1;flex:1;-ms-flex-order:4;order:4;min-width:0}:host(.toolbar-segment) .toolbar-content{display:-ms-inline-flexbox;display:inline-flex}:host(.toolbar-searchbar) .toolbar-container{padding-top:0;padding-bottom:0}:host(.toolbar-searchbar) ::slotted(*){-ms-flex-item-align:start;align-self:start}:host(.toolbar-searchbar) ::slotted(ion-chip){margin-top:3px}::slotted(ion-buttons){min-height:38px}::slotted([slot=start]){-ms-flex-order:2;order:2}::slotted([slot=secondary]){-ms-flex-order:3;order:3}::slotted([slot=primary]){-ms-flex-order:5;order:5;text-align:end}::slotted([slot=end]){-ms-flex-order:6;order:6;text-align:end}:host(.toolbar-title-large) .toolbar-container{-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:start;align-items:flex-start}:host(.toolbar-title-large) .toolbar-content ion-title{-ms-flex:1;flex:1;-ms-flex-order:8;order:8;min-width:100%}",wS=":host{--border-width:0;--border-style:solid;--opacity:1;--opacity-scale:1;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;width:100%;padding-right:var(--ion-safe-area-right);padding-left:var(--ion-safe-area-left);color:var(--color);font-family:var(--ion-font-family, inherit);contain:content;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-contrast)}:host(.ion-color) .toolbar-background{background:var(--ion-color-base)}.toolbar-container{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:var(--min-height);contain:content;overflow:hidden;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}.toolbar-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;opacity:calc(var(--opacity) * var(--opacity-scale));z-index:-1;pointer-events:none}::slotted(ion-progress-bar){left:0;right:0;bottom:0;position:absolute}:host{--background:var(--ion-toolbar-background, var(--ion-background-color, #fff));--color:var(--ion-toolbar-color, var(--ion-text-color, #424242));--border-color:var(--ion-toolbar-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, #c1c4cd))));--padding-top:0;--padding-bottom:0;--padding-start:0;--padding-end:0;--min-height:56px}.toolbar-content{-ms-flex:1;flex:1;-ms-flex-order:3;order:3;min-width:0;max-width:100%}::slotted(.buttons-first-slot){-webkit-margin-start:4px;margin-inline-start:4px}::slotted(.buttons-last-slot){-webkit-margin-end:4px;margin-inline-end:4px}::slotted([slot=start]){-ms-flex-order:2;order:2}::slotted([slot=secondary]){-ms-flex-order:4;order:4}::slotted([slot=primary]){-ms-flex-order:5;order:5;text-align:end}::slotted([slot=end]){-ms-flex-order:6;order:6;text-align:end}",dy=Ce(class extends De{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.childrenStyles=new Map}componentWillLoad(){let t=Array.from(this.el.querySelectorAll("ion-buttons")),n=t.find(i=>i.slot==="start");n&&n.classList.add("buttons-first-slot");let r=t.reverse(),o=r.find(i=>i.slot==="end")||r.find(i=>i.slot==="primary")||r.find(i=>i.slot==="secondary");o&&o.classList.add("buttons-last-slot")}childrenStyle(t){t.stopPropagation();let n=t.target.tagName,r=t.detail,o={},i=this.childrenStyles.get(n)||{},s=!1;Object.keys(r).forEach(a=>{let c=`toolbar-${a}`,l=r[a];l!==i[c]&&(s=!0),l&&(o[c]=!0)}),s&&(this.childrenStyles.set(n,o),to(this))}render(){let t=J(this),n={};return this.childrenStyles.forEach(r=>{Object.assign(n,r)}),T(xe,{key:"f6c4f669a6a61c5eac4cbb5ea0aa97c48ae5bd46",class:Object.assign(Object.assign({},n),Ur(this.color,{[t]:!0,"in-toolbar":$r("ion-toolbar",this.el)}))},T("div",{key:"9c81742ffa02de9ba7417025b077d05e67305074",class:"toolbar-background",part:"background"}),T("div",{key:"5fc96d166fa47894a062e41541a9beee38078a36",class:"toolbar-container",part:"container"},T("slot",{key:"b62c0d9d59a70176bdbf769aec6090d7a166853b",name:"start"}),T("slot",{key:"d01d3cc2c50e5aaa49c345b209fe8dbdf3d48131",name:"secondary"}),T("div",{key:"3aaa3a2810aedd38c37eb616158ec7b9638528fc",class:"toolbar-content",part:"content"},T("slot",{key:"357246690f8d5e1cc3ca369611d4845a79edf610"})),T("slot",{key:"06ed3cca4f7ebff4a54cd877dad3cc925ccf9f75",name:"primary"}),T("slot",{key:"e453d43d14a26b0d72f41e1b81a554bab8ece811",name:"end"})))}get el(){return this}static get style(){return{ios:ES,md:wS}}},[33,"ion-toolbar",{color:[513]},[[0,"ionStyle","childrenStyle"]]]);function fy(){if(typeof customElements>"u")return;["ion-toolbar"].forEach(t=>{switch(t){case"ion-toolbar":customElements.get(t)||customElements.define(t,dy);break}})}var py=uy;var hy=fy;var _S=["outletContent"],Kr=["*"];var s8=(()=>{let e=class rc extends Rv{parentOutlet;outletContent;constructor(n,r,o,i,s,a,c,l){super(n,r,o,i,s,a,c,l),this.parentOutlet=l}static \u0275fac=function(r){return new(r||rc)($t("name"),$t("tabs"),j(ut),j(we),j(tt),j(q),j(Le),j(rc,12))};static \u0275cmp=It({type:rc,selectors:[["ion-router-outlet"]],viewQuery:function(r,o){if(r&1&&aa(_S,7,lt),r&2){let i;Vo(i=Bo())&&(o.outletContent=i.first)}},features:[Fo],ngContentSelectors:Kr,decls:3,vars:0,consts:[["outletContent",""]],template:function(r,o){r&1&&(hn(),ia(0,null,0),mn(2),sa())},encapsulation:2})};return e=En([_v({defineCustomElementFn:Ov})],e),e})();var SS=(e,t)=>{let n=e.prototype;t.forEach(r=>{Object.defineProperty(n,r,{get(){return this.el[r]},set(o){this.z.runOutsideAngular(()=>this.el[r]=o)},configurable:!0})})},TS=(e,t)=>{let n=e.prototype;t.forEach(r=>{n[r]=function(){let o=arguments;return this.z.runOutsideAngular(()=>this.el[r].apply(this.el,o))}})},MS=(e,t,n)=>{n.forEach(r=>e[r]=cr(t,r))};function bi(e){return function(n){let{defineCustomElementFn:r,inputs:o,methods:i}=e;return r!==void 0&&r(),o&&SS(n,o),i&&TS(n,i),n}}var a8=(()=>{let e=class Gd{z;el;constructor(n,r,o){this.z=o,n.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Gd)(j(et),j(we),j(q))};static \u0275cmp=It({type:Gd,selectors:[["ion-app"]],ngContentSelectors:Kr,decls:1,vars:0,template:function(r,o){r&1&&(hn(),mn(0))},encapsulation:2,changeDetection:0})};return e=En([bi({defineCustomElementFn:Jv,methods:["setFocus"]})],e),e})();var c8=(()=>{let e=class Wd{z;el;constructor(n,r,o){this.z=o,n.detach(),this.el=r.nativeElement,MS(this,this.el,["ionScrollStart","ionScroll","ionScrollEnd"])}static \u0275fac=function(r){return new(r||Wd)(j(et),j(we),j(q))};static \u0275cmp=It({type:Wd,selectors:[["ion-content"]],inputs:{color:"color",fixedSlotPlacement:"fixedSlotPlacement",forceOverscroll:"forceOverscroll",fullscreen:"fullscreen",scrollEvents:"scrollEvents",scrollX:"scrollX",scrollY:"scrollY"},ngContentSelectors:Kr,decls:1,vars:0,template:function(r,o){r&1&&(hn(),mn(0))},encapsulation:2,changeDetection:0})};return e=En([bi({defineCustomElementFn:ny,inputs:["color","fixedSlotPlacement","forceOverscroll","fullscreen","scrollEvents","scrollX","scrollY"],methods:["getScrollElement","scrollToTop","scrollToBottom","scrollByPoint","scrollToPoint"]})],e),e})();var l8=(()=>{let e=class qd{z;el;constructor(n,r,o){this.z=o,n.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||qd)(j(et),j(we),j(q))};static \u0275cmp=It({type:qd,selectors:[["ion-header"]],inputs:{collapse:"collapse",mode:"mode",translucent:"translucent"},ngContentSelectors:Kr,decls:1,vars:0,template:function(r,o){r&1&&(hn(),mn(0))},encapsulation:2,changeDetection:0})};return e=En([bi({defineCustomElementFn:cy,inputs:["collapse","mode","translucent"]})],e),e})();var u8=(()=>{let e=class Zd{z;el;constructor(n,r,o){this.z=o,n.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Zd)(j(et),j(we),j(q))};static \u0275cmp=It({type:Zd,selectors:[["ion-title"]],inputs:{color:"color",size:"size"},ngContentSelectors:Kr,decls:1,vars:0,template:function(r,o){r&1&&(hn(),mn(0))},encapsulation:2,changeDetection:0})};return e=En([bi({defineCustomElementFn:py,inputs:["color","size"]})],e),e})();var d8=(()=>{let e=class Yd{z;el;constructor(n,r,o){this.z=o,n.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Yd)(j(et),j(we),j(q))};static \u0275cmp=It({type:Yd,selectors:[["ion-toolbar"]],inputs:{color:"color",mode:"mode"},ngContentSelectors:Kr,decls:1,vars:0,template:function(r,o){r&1&&(hn(),mn(0))},encapsulation:2,changeDetection:0})};return e=En([bi({defineCustomElementFn:hy,inputs:["color","mode"]})],e),e})();var xS=(()=>{class e extends yi{angularDelegate=h(Xa);injector=h(Ee);environmentInjector=h(ee);constructor(){super(Nd),zv()}create(n){return super.create(U(y({},n),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"modal")}))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=C({token:e,factory:e.\u0275fac})}return e})(),Qd=class extends yi{angularDelegate=h(Xa);injector=h(Ee);environmentInjector=h(ee);constructor(){super(kd),Xv()}create(t){return super.create(U(y({},t),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"popover")}))}},f8=(e={})=>fr([{provide:Ka,useValue:e},{provide:ea,useFactory:AS,multi:!0,deps:[Ka,ae]},kv(),Xa,xS,Qd]),AS=(e,t)=>()=>{t.documentElement.classList.add("ion-ce"),Ad(e)};export{zh as a,It as b,Bu as c,na as d,ra as e,oa as f,kb as g,Yu as h,AI as i,Yg as j,hw as k,gw as l,Iw as m,OH as n,Vd as o,s8 as p,a8 as q,c8 as r,l8 as s,u8 as t,d8 as u,f8 as v};
