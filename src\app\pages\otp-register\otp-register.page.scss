.register-container {
  max-width: 500px;
  margin: 0 auto;
  padding: 20px;
}

.header-section {
  text-align: center;
  margin-bottom: 30px;
  
  .logo-container {
    margin-bottom: 20px;
    
    .logo-icon {
      font-size: 64px;
      color: var(--ion-color-primary);
    }
  }
  
  h1 {
    font-size: 28px;
    font-weight: 600;
    color: var(--ion-color-dark);
    margin-bottom: 8px;
  }
  
  .subtitle {
    font-size: 16px;
    color: var(--ion-color-medium);
    margin: 0;
    line-height: 1.4;
  }
}

.form-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  
  ion-card-content {
    padding: 24px;
  }
}

.form-section {
  margin-bottom: 24px;
  
  h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--ion-color-dark);
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--ion-color-light-shade);
  }
}

.form-item {
  margin-bottom: 16px;
  --border-radius: 8px;
  --background: var(--ion-color-light);
  
  ion-label {
    font-weight: 500;
    color: var(--ion-color-dark);
  }
  
  ion-input, ion-select {
    --padding-start: 12px;
    --padding-end: 12px;
    font-size: 16px;
  }
  
  &.ion-invalid {
    --border-color: var(--ion-color-danger);
  }
}

.password-toggle {
  --color: var(--ion-color-medium);
  --padding-start: 8px;
  --padding-end: 8px;
  
  ion-icon {
    font-size: 20px;
  }
}

.checkbox-item {
  --background: transparent;
  --border-color: transparent;
  --padding-start: 0;
  --inner-padding-end: 0;
  
  ion-checkbox {
    margin-right: 12px;
    --size: 20px;
    --border-radius: 4px;
  }
  
  .checkbox-label {
    font-size: 14px;
    line-height: 1.4;
    
    .terms-link {
      color: var(--ion-color-primary);
      text-decoration: none;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
}

.error-message {
  color: var(--ion-color-danger);
  font-size: 14px;
  margin-top: 8px;
  margin-bottom: 16px;
  padding-left: 4px;
}

.success-message {
  color: var(--ion-color-success);
  font-size: 14px;
  margin-top: 8px;
  margin-bottom: 16px;
  padding-left: 4px;
}

.register-button, .verify-button {
  margin-top: 20px;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  --border-radius: 8px;
  
  ion-spinner {
    margin-right: 8px;
  }
}

.otp-timer {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 16px 0;
  color: var(--ion-color-medium);
  font-size: 14px;
  
  ion-icon {
    margin-right: 8px;
    font-size: 16px;
  }
}

.resend-section {
  text-align: center;
  margin: 16px 0;
  
  ion-text {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
  }
  
  .resend-button {
    --color: var(--ion-color-primary);
    font-weight: 500;
    
    ion-icon {
      margin-right: 4px;
    }
  }
}

.login-link {
  text-align: center;
  margin-top: 30px;
  
  ion-text p {
    margin: 0;
    font-size: 14px;
    
    .link {
      color: var(--ion-color-primary);
      text-decoration: none;
      font-weight: 500;
      cursor: pointer;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
}

// Responsive design
@media (max-width: 480px) {
  .register-container {
    padding: 16px;
  }
  
  .header-section {
    margin-bottom: 24px;
    
    .logo-container .logo-icon {
      font-size: 56px;
    }
    
    h1 {
      font-size: 24px;
    }
    
    .subtitle {
      font-size: 14px;
    }
  }
  
  .form-card ion-card-content {
    padding: 20px;
  }
  
  .form-section h3 {
    font-size: 16px;
  }
}

// Aadhaar Upload Styles
.upload-section {
  margin-top: 24px;

  h4 {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 18px;
    font-weight: 600;
    color: var(--ion-color-dark);
    margin-bottom: 8px;

    .section-icon {
      font-size: 20px;
      color: var(--ion-color-primary);
    }
  }

  .upload-description {
    font-size: 14px;
    color: var(--ion-color-medium);
    margin-bottom: 16px;
    line-height: 1.4;
  }
}

.file-upload-container {
  .upload-area {
    border: 2px dashed var(--ion-color-medium);
    border-radius: 12px;
    padding: 24px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: rgba(var(--ion-color-primary-rgb), 0.02);

    &:hover {
      border-color: var(--ion-color-primary);
      background: rgba(var(--ion-color-primary-rgb), 0.05);
      transform: translateY(-2px);
    }

    &.drag-over {
      border-color: var(--ion-color-primary);
      background: rgba(var(--ion-color-primary-rgb), 0.1);
      transform: scale(1.02);
    }

    &.has-file {
      border-color: var(--ion-color-success);
      background: rgba(var(--ion-color-success-rgb), 0.05);
    }
  }

  .upload-placeholder {
    .upload-icon {
      font-size: 48px;
      color: var(--ion-color-medium);
      margin-bottom: 16px;
    }

    .upload-text {
      font-size: 16px;
      color: var(--ion-color-dark);
      margin-bottom: 8px;

      strong {
        color: var(--ion-color-primary);
      }
    }

    .upload-hint {
      font-size: 14px;
      color: var(--ion-color-medium);
      margin: 0;
    }
  }

  .file-preview {
    display: flex;
    align-items: center;
    gap: 16px;
    text-align: left;

    .preview-image {
      width: 80px;
      height: 80px;
      object-fit: cover;
      border-radius: 8px;
      border: 2px solid var(--ion-color-light);
    }

    .file-info {
      flex: 1;

      .file-name {
        font-weight: 600;
        color: var(--ion-color-dark);
        margin: 0 0 4px 0;
        font-size: 14px;
      }

      .file-size {
        color: var(--ion-color-medium);
        margin: 0;
        font-size: 12px;
      }
    }

    .remove-file-btn {
      --color: var(--ion-color-danger);

      ion-icon {
        font-size: 20px;
      }
    }
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .form-card {
    --background: var(--ion-color-dark);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  .form-item {
    --background: var(--ion-color-dark-shade);
  }

  .form-section h3 {
    border-bottom-color: var(--ion-color-dark-tint);
  }

  .upload-section h4 {
    color: var(--ion-color-light);
  }

  .file-upload-container .upload-area {
    border-color: var(--ion-color-dark-tint);
    background: rgba(var(--ion-color-dark-tint-rgb), 0.1);
  }
}
