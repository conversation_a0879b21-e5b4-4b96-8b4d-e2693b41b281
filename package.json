{"name": "driversetuApp", "version": "0.0.1", "author": "Ionic Framework", "homepage": "https://ionicframework.com/", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "build:prod": "ng build --configuration production", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint", "cap:sync": "npx cap sync", "cap:android": "npx cap open android", "cap:ios": "npx cap open ios", "build:mobile": "node build-mobile.js", "build:android": "node build-mobile.js --android", "build:ios": "node build-mobile.js --ios", "serve:backend": "cd backend && node server-production.js", "dev": "concurrently \"npm run serve:backend\" \"ionic serve\"", "deploy:prep": "npm run build:prod && npm run cap:sync"}, "private": true, "dependencies": {"@angular/animations": "^20.0.0", "@angular/common": "^20.0.5", "@angular/compiler": "^20.0.0", "@angular/core": "^20.0.0", "@angular/forms": "^20.0.5", "@angular/platform-browser": "^20.0.0", "@angular/platform-browser-dynamic": "^20.0.0", "@angular/router": "^20.0.5", "@capacitor/android": "^7.4.0", "@capacitor/app": "7.0.1", "@capacitor/core": "^7.4.0", "@capacitor/haptics": "7.0.1", "@capacitor/ios": "^7.4.0", "@capacitor/keyboard": "7.0.1", "@capacitor/push-notifications": "^7.0.1", "@capacitor/status-bar": "7.0.1", "@ionic/angular": "^8.0.0", "axios": "^1.10.0", "ionicons": "^7.0.0", "rxjs": "~7.8.0", "socket.io-client": "^4.8.1", "sweetalert2": "^11.22.1", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^20.0.0", "@angular-eslint/builder": "^20.0.0", "@angular-eslint/eslint-plugin": "^20.0.0", "@angular-eslint/eslint-plugin-template": "^20.0.0", "@angular-eslint/schematics": "^20.0.0", "@angular-eslint/template-parser": "^20.0.0", "@angular/cli": "^20.0.0", "@angular/compiler-cli": "^20.0.0", "@angular/language-service": "^20.0.0", "@capacitor/cli": "^7.4.0", "@ionic/angular-toolkit": "^12.0.0", "@types/jasmine": "~5.1.0", "@typescript-eslint/eslint-plugin": "^8.18.0", "@typescript-eslint/parser": "^8.18.0", "concurrently": "^9.2.0", "eslint": "^9.16.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsdoc": "^48.2.1", "eslint-plugin-prefer-arrow": "1.2.2", "jasmine-core": "~5.1.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.8.0"}, "description": "An Ionic project"}