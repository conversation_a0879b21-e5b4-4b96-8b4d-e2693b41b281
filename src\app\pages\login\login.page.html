<ion-header>
  <ion-toolbar class="gradient-toolbar">
    <ion-title class="animated-title">DriverSetu Login</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content class="login-content">
  <div class="login-container">
    <ion-card class="login-card">
      <ion-card-header>
        <ion-card-title class="login-title">Welcome Back!</ion-card-title>
        <p class="login-subtitle">Sign in to your account</p>
      </ion-card-header>

      <ion-card-content>
        <form [formGroup]="loginForm" (ngSubmit)="login()">
          <ion-item class="form-item">
            <ion-label position="floating">Email Address</ion-label>
            <ion-input formControlName="email" type="email" placeholder="Enter your email"></ion-input>
          </ion-item>

          <ion-item class="form-item">
            <ion-label position="floating">Password</ion-label>
            <ion-input formControlName="password" type="password" placeholder="Enter your password"></ion-input>
          </ion-item>

          <ion-button
            expand="full"
            type="submit"
            [disabled]="loginForm.invalid"
            class="login-button">
            <span>Login</span>
          </ion-button>
        </form>

        <div class="forgot-password-link">
          <a routerLink="/forgot-password" class="forgot-link-text">
            <ion-icon name="key-outline"></ion-icon>
            Forgot Password?
          </a>
        </div>

        <div class="signup-link">
          <p>Don't have an account?
            <a routerLink="/otp-register" class="signup-link-text">Register with OTP</a>
          </p>
        </div>

        <!-- Alternative Login Options -->
        <div class="alternative-login-section">
          <div class="divider">
            <span>OR</span>
          </div>

          <!-- OTP Login -->
          <ion-button
            expand="block"
            fill="outline"
            routerLink="/otp-login"
            class="otp-login-btn">
            <ion-icon name="phone-portrait-outline" slot="start"></ion-icon>
            Login with OTP
          </ion-button>

          <!-- Driver Login -->
          <ion-button
            expand="block"
            fill="outline"
            routerLink="/driver-login"
            class="driver-login-btn">
            <ion-icon name="car-sport-outline" slot="start"></ion-icon>
            Driver Login (Password)
          </ion-button>
        </div>
      </ion-card-content>
    </ion-card>
  </div>
</ion-content>
