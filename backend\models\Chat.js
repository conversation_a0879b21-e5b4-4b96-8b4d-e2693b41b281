const mongoose = require('mongoose');

const messageSchema = new mongoose.Schema({
  sender: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  content: {
    type: String,
    required: [true, 'Message content is required'],
    trim: true,
    maxlength: [1000, 'Message cannot exceed 1000 characters']
  },
  messageType: {
    type: String,
    enum: ['text', 'image', 'file', 'location'],
    default: 'text'
  },
  attachment: {
    filename: String,
    originalName: String,
    path: String,
    size: Number
  },
  isRead: {
    type: Boolean,
    default: false
  },
  readAt: Date
}, {
  timestamps: true
});

const chatSchema = new mongoose.Schema({
  participants: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }],
  chatType: {
    type: String,
    enum: ['direct', 'group'],
    default: 'direct'
  },
  title: {
    type: String,
    trim: true
  },
  messages: [messageSchema],
  lastMessage: {
    type: messageSchema,
    default: null
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Index for better query performance
chatSchema.index({ participants: 1 });
chatSchema.index({ 'messages.createdAt': -1 });
chatSchema.index({ updatedAt: -1 });

// Update lastMessage when a new message is added
chatSchema.pre('save', function(next) {
  if (this.messages && this.messages.length > 0) {
    this.lastMessage = this.messages[this.messages.length - 1];
  }
  next();
});

// Static method to find or create chat between users
chatSchema.statics.findOrCreateChat = async function(user1Id, user2Id) {
  let chat = await this.findOne({
    participants: { $all: [user1Id, user2Id] },
    chatType: 'direct'
  }).populate('participants', 'name email role profileImage');
  
  if (!chat) {
    chat = await this.create({
      participants: [user1Id, user2Id],
      chatType: 'direct'
    });
    chat = await this.findById(chat._id).populate('participants', 'name email role profileImage');
  }
  
  return chat;
};

// Method to add message to chat
chatSchema.methods.addMessage = function(senderId, content, messageType = 'text', attachment = null) {
  const message = {
    sender: senderId,
    content,
    messageType,
    attachment
  };
  
  this.messages.push(message);
  this.lastMessage = message;
  return this.save();
};

// Method to mark messages as read
chatSchema.methods.markMessagesAsRead = function(userId) {
  this.messages.forEach(message => {
    if (message.sender.toString() !== userId.toString() && !message.isRead) {
      message.isRead = true;
      message.readAt = new Date();
    }
  });
  return this.save();
};

// Method to get unread message count for a user
chatSchema.methods.getUnreadCount = function(userId) {
  return this.messages.filter(message => 
    message.sender.toString() !== userId.toString() && !message.isRead
  ).length;
};

module.exports = mongoose.model('Chat', chatSchema);
