<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button (click)="goBack()"></ion-back-button>
    </ion-buttons>
    <ion-title>{{ step === 1 ? 'Phone Login' : 'Verify OTP' }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="ion-padding">
  <div class="login-container">
    
    <!-- Header Section -->
    <div class="header-section">
      <div class="logo-container">
        <ion-icon name="phone-portrait-outline" class="logo-icon"></ion-icon>
      </div>
      <h1>{{ step === 1 ? 'Login with Phone' : 'Enter OTP' }}</h1>
      <p class="subtitle">
        {{ step === 1 ? 'Enter your phone number to receive OTP' : 'We sent a 6-digit code to ' + phoneNumber }}
      </p>
    </div>

    <!-- Step 1: Phone Number Input -->
    <div *ngIf="step === 1">
      <form [formGroup]="phoneForm" (ngSubmit)="sendOtp()">
        <ion-card class="form-card">
          <ion-card-content>
            
            <ion-item class="form-item">
              <ion-label position="stacked">Phone Number</ion-label>
              <ion-input
                type="tel"
                formControlName="phone"
                placeholder="Enter 10-digit phone number"
                maxlength="10"
                [class.ion-invalid]="phoneForm.get('phone')?.invalid && phoneForm.get('phone')?.touched"
              ></ion-input>
            </ion-item>
            
            <div class="error-message" *ngIf="getFieldError(phoneForm, 'phone')">
              {{ getFieldError(phoneForm, 'phone') }}
            </div>

            <div class="error-message" *ngIf="errorMessage">
              {{ errorMessage }}
            </div>

            <div class="success-message" *ngIf="successMessage">
              {{ successMessage }}
            </div>

            <ion-button 
              expand="block" 
              type="submit" 
              [disabled]="isLoading"
              class="login-button"
            >
              <ion-spinner *ngIf="isLoading" name="crescent"></ion-spinner>
              <span *ngIf="!isLoading">Send OTP</span>
            </ion-button>

          </ion-card-content>
        </ion-card>
      </form>
    </div>

    <!-- Step 2: OTP Verification -->
    <div *ngIf="step === 2">
      <form [formGroup]="otpForm" (ngSubmit)="verifyOtp()">
        <ion-card class="form-card">
          <ion-card-content>
            
            <ion-item class="form-item">
              <ion-label position="stacked">Enter OTP</ion-label>
              <ion-input
                type="number"
                formControlName="otp"
                placeholder="Enter 6-digit OTP"
                maxlength="6"
                [class.ion-invalid]="otpForm.get('otp')?.invalid && otpForm.get('otp')?.touched"
              ></ion-input>
            </ion-item>
            
            <div class="error-message" *ngIf="getFieldError(otpForm, 'otp')">
              {{ getFieldError(otpForm, 'otp') }}
            </div>

            <div class="error-message" *ngIf="errorMessage">
              {{ errorMessage }}
            </div>

            <!-- OTP Timer -->
            <div class="otp-timer" *ngIf="otpTimer > 0">
              <ion-icon name="time-outline"></ion-icon>
              <span>Resend OTP in {{ getTimerDisplay() }}</span>
            </div>

            <!-- Resend OTP Button -->
            <div class="resend-section" *ngIf="canResendOtp">
              <ion-text color="medium">Didn't receive OTP?</ion-text>
              <ion-button 
                fill="clear" 
                color="primary" 
                (click)="resendOtp()"
                class="resend-button"
              >
                <ion-icon name="refresh-outline" slot="start"></ion-icon>
                Resend OTP
              </ion-button>
            </div>

            <ion-button 
              expand="block" 
              type="submit" 
              [disabled]="isOtpLoading"
              class="verify-button"
            >
              <ion-spinner *ngIf="isOtpLoading" name="crescent"></ion-spinner>
              <span *ngIf="!isOtpLoading">Verify & Login</span>
            </ion-button>

          </ion-card-content>
        </ion-card>
      </form>
    </div>

    <!-- Alternative Login Options -->
    <div class="alternative-login">
      <ion-text color="medium">
        <p>Or login with password</p>
      </ion-text>
      <ion-button 
        fill="outline" 
        expand="block" 
        (click)="goToPasswordLogin()"
        class="alt-login-button"
      >
        Login with Password
      </ion-button>
    </div>

  </div>
</ion-content>
