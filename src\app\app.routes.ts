import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: 'home',
    loadComponent: () => import('./home/<USER>').then((m) => m.HomePage),
  },
  { path: '', redirectTo: 'splash', pathMatch: 'full' },
  {
    path: 'splash',
    loadComponent: () => import('./pages/splash/splash.page').then( m => m.SplashPage)
  },
  {
    path: 'login',
    loadComponent: () => import('./pages/login/login.page').then( m => m.LoginPage)
  },
  {
    path: 'driver-login',
    loadComponent: () => import('./pages/driver-login/driver-login.page').then( m => m.DriverLoginPage)
  },
  {
    path: 'signup',
    loadComponent: () => import('./pages/signup/signup.page').then( m => m.SignupPage)
  },
  {
    path: 'forgot-password',
    loadComponent: () => import('./pages/forgot-password/forgot-password.page').then( m => m.ForgotPasswordPage)
  },
  {
    path: 'driver-dashboard',
    loadComponent: () => import('./pages/driver-dashboard/driver-dashboard.page').then( m => m.DriverDashboardPage)
  },
  {
    path: 'admin-dashboard',
    loadComponent: () => import('./pages/admin-dashboard/admin-dashboard.page').then( m => m.AdminDashboardPage)
  },
  {
    path: 'subscription',
    loadComponent: () => import('./pages/subscription/subscription.page').then( m => m.SubscriptionPage)
  },
  {
    path: 'admin-panel',
    loadComponent: () => import('./pages/admin-panel/admin-panel.page').then( m => m.AdminPanelPage)
  },
  {
    path: 'otp-login',
    loadComponent: () => import('./pages/otp-login/otp-login.page').then( m => m.OtpLoginPage)
  },
  {
    path: 'otp-register',
    loadComponent: () => import('./pages/otp-register/otp-register.page').then( m => m.OtpRegisterPage)
  },
];
