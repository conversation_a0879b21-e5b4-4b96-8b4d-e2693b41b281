<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button (click)="goBack()"></ion-back-button>
    </ion-buttons>
    <ion-title>{{ step === 1 ? 'Register' : 'Verify OTP' }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="ion-padding">
  <div class="register-container">
    
    <!-- Header Section -->
    <div class="header-section">
      <div class="logo-container">
        <ion-icon name="person-add-outline" class="logo-icon"></ion-icon>
      </div>
      <h1>{{ step === 1 ? 'Create Account' : 'Verify Phone' }}</h1>
      <p class="subtitle">
        {{ step === 1 ? 'Fill in your details to get started' : 'We sent a 6-digit code to ' + phoneNumber }}
      </p>
    </div>

    <!-- Step 1: Registration Form -->
    <div *ngIf="step === 1">
      <form [formGroup]="registrationForm" (ngSubmit)="sendOtp()">
        <ion-card class="form-card">
          <ion-card-content>
            
            <!-- Personal Information -->
            <div class="form-section">
              <h3>Personal Information</h3>
              
              <ion-item class="form-item">
                <ion-label position="stacked">Full Name *</ion-label>
                <ion-input
                  type="text"
                  formControlName="name"
                  placeholder="Enter your full name"
                  [class.ion-invalid]="registrationForm.get('name')?.invalid && registrationForm.get('name')?.touched"
                ></ion-input>
              </ion-item>
              <div class="error-message" *ngIf="getFieldError(registrationForm, 'name')">
                {{ getFieldError(registrationForm, 'name') }}
              </div>

              <ion-item class="form-item">
                <ion-label position="stacked">Email *</ion-label>
                <ion-input
                  type="email"
                  formControlName="email"
                  placeholder="Enter your email"
                  [class.ion-invalid]="registrationForm.get('email')?.invalid && registrationForm.get('email')?.touched"
                ></ion-input>
              </ion-item>
              <div class="error-message" *ngIf="getFieldError(registrationForm, 'email')">
                {{ getFieldError(registrationForm, 'email') }}
              </div>

              <ion-item class="form-item">
                <ion-label position="stacked">Phone Number *</ion-label>
                <ion-input
                  type="tel"
                  formControlName="phone"
                  placeholder="Enter 10-digit phone number"
                  maxlength="10"
                  [class.ion-invalid]="registrationForm.get('phone')?.invalid && registrationForm.get('phone')?.touched"
                ></ion-input>
              </ion-item>
              <div class="error-message" *ngIf="getFieldError(registrationForm, 'phone')">
                {{ getFieldError(registrationForm, 'phone') }}
              </div>
            </div>

            <!-- Password Section -->
            <div class="form-section">
              <h3>Security</h3>
              
              <ion-item class="form-item">
                <ion-label position="stacked">Password *</ion-label>
                <ion-input
                  [type]="showPassword ? 'text' : 'password'"
                  formControlName="password"
                  placeholder="Enter password (min 6 characters)"
                  [class.ion-invalid]="registrationForm.get('password')?.invalid && registrationForm.get('password')?.touched"
                ></ion-input>
                <ion-button 
                  slot="end" 
                  fill="clear" 
                  (click)="togglePassword()"
                  class="password-toggle"
                >
                  <ion-icon [name]="showPassword ? 'eye-off-outline' : 'eye-outline'"></ion-icon>
                </ion-button>
              </ion-item>
              <div class="error-message" *ngIf="getFieldError(registrationForm, 'password')">
                {{ getFieldError(registrationForm, 'password') }}
              </div>

              <ion-item class="form-item">
                <ion-label position="stacked">Confirm Password *</ion-label>
                <ion-input
                  [type]="showConfirmPassword ? 'text' : 'password'"
                  formControlName="confirmPassword"
                  placeholder="Confirm your password"
                  [class.ion-invalid]="registrationForm.get('confirmPassword')?.invalid && registrationForm.get('confirmPassword')?.touched"
                ></ion-input>
                <ion-button 
                  slot="end" 
                  fill="clear" 
                  (click)="toggleConfirmPassword()"
                  class="password-toggle"
                >
                  <ion-icon [name]="showConfirmPassword ? 'eye-off-outline' : 'eye-outline'"></ion-icon>
                </ion-button>
              </ion-item>
              <div class="error-message" *ngIf="getFieldError(registrationForm, 'confirmPassword')">
                {{ getFieldError(registrationForm, 'confirmPassword') }}
              </div>
            </div>

            <!-- Role Selection -->
            <div class="form-section">
              <h3>Account Type</h3>
              
              <ion-item class="form-item">
                <ion-label position="stacked">Role *</ion-label>
                <ion-select 
                  formControlName="role" 
                  placeholder="Select your role"
                  (ionChange)="onRoleChange()"
                >
                  <ion-select-option value="driver">Driver</ion-select-option>
                  <ion-select-option value="admin">Admin</ion-select-option>
                </ion-select>
              </ion-item>
            </div>

            <!-- Driver-specific fields -->
            <div class="form-section" *ngIf="registrationForm.get('role')?.value === 'driver'">
              <h3>Driver Information</h3>
              
              <ion-item class="form-item">
                <ion-label position="stacked">License Number *</ion-label>
                <ion-input
                  type="text"
                  formControlName="licenseNumber"
                  placeholder="Enter your license number"
                  [class.ion-invalid]="registrationForm.get('licenseNumber')?.invalid && registrationForm.get('licenseNumber')?.touched"
                ></ion-input>
              </ion-item>
              <div class="error-message" *ngIf="getFieldError(registrationForm, 'licenseNumber')">
                {{ getFieldError(registrationForm, 'licenseNumber') }}
              </div>

              <ion-item class="form-item">
                <ion-label position="stacked">Vehicle Details *</ion-label>
                <ion-input
                  type="text"
                  formControlName="vehicleDetails"
                  placeholder="e.g., Honda City, MH12AB1234"
                  [class.ion-invalid]="registrationForm.get('vehicleDetails')?.invalid && registrationForm.get('vehicleDetails')?.touched"
                ></ion-input>
              </ion-item>
              <div class="error-message" *ngIf="getFieldError(registrationForm, 'vehicleDetails')">
                {{ getFieldError(registrationForm, 'vehicleDetails') }}
              </div>

              <!-- Aadhaar Upload Section -->
              <div class="upload-section">
                <h4>
                  <ion-icon name="card-outline" class="section-icon"></ion-icon>
                  Aadhaar Card Upload *
                </h4>
                <p class="upload-description">Please upload a clear photo of your Aadhaar card for verification</p>

                <div class="file-upload-container">
                  <input
                    type="file"
                    #aadhaarFileInput
                    accept="image/*"
                    (change)="onAadhaarFileSelected($event)"
                    style="display: none;"
                  >

                  <div class="upload-area"
                       [class.has-file]="selectedAadhaarFile"
                       [class.drag-over]="isDragOver"
                       (click)="aadhaarFileInput.click()"
                       (dragover)="onDragOver($event)"
                       (dragleave)="onDragLeave($event)"
                       (drop)="onDrop($event)">

                    <div *ngIf="!selectedAadhaarFile" class="upload-placeholder">
                      <ion-icon name="cloud-upload-outline" class="upload-icon"></ion-icon>
                      <p class="upload-text">
                        <strong>Click to upload</strong> or drag and drop
                      </p>
                      <p class="upload-hint">PNG, JPG up to 5MB</p>
                    </div>

                    <div *ngIf="selectedAadhaarFile" class="file-preview">
                      <img [src]="aadhaarPreviewUrl" alt="Aadhaar preview" class="preview-image">
                      <div class="file-info">
                        <p class="file-name">{{ selectedAadhaarFile.name }}</p>
                        <p class="file-size">{{ formatFileSize(selectedAadhaarFile.size) }}</p>
                      </div>
                      <ion-button
                        fill="clear"
                        color="danger"
                        (click)="removeAadhaarFile($event)"
                        class="remove-file-btn"
                      >
                        <ion-icon name="trash-outline"></ion-icon>
                      </ion-button>
                    </div>
                  </div>

                  <div class="error-message" *ngIf="aadhaarUploadError">
                    {{ aadhaarUploadError }}
                  </div>
                </div>
              </div>
            </div>

            <!-- Admin-specific fields -->
            <div class="form-section" *ngIf="registrationForm.get('role')?.value === 'admin'">
              <h3>Company Information</h3>
              
              <ion-item class="form-item">
                <ion-label position="stacked">Company Name *</ion-label>
                <ion-input
                  type="text"
                  formControlName="companyName"
                  placeholder="Enter your company name"
                  [class.ion-invalid]="registrationForm.get('companyName')?.invalid && registrationForm.get('companyName')?.touched"
                ></ion-input>
              </ion-item>
              <div class="error-message" *ngIf="getFieldError(registrationForm, 'companyName')">
                {{ getFieldError(registrationForm, 'companyName') }}
              </div>
            </div>

            <!-- Address -->
            <div class="form-section">
              <ion-item class="form-item">
                <ion-label position="stacked">Address</ion-label>
                <ion-input
                  type="text"
                  formControlName="address"
                  placeholder="Enter your address"
                ></ion-input>
              </ion-item>
            </div>

            <!-- Terms and Conditions -->
            <div class="form-section">
              <ion-item class="checkbox-item">
                <ion-checkbox 
                  formControlName="agreeToTerms"
                  [class.ion-invalid]="registrationForm.get('agreeToTerms')?.invalid && registrationForm.get('agreeToTerms')?.touched"
                ></ion-checkbox>
                <ion-label class="checkbox-label">
                  I agree to the <a href="#" class="terms-link">Terms and Conditions</a>
                </ion-label>
              </ion-item>
              <div class="error-message" *ngIf="getFieldError(registrationForm, 'agreeToTerms')">
                {{ getFieldError(registrationForm, 'agreeToTerms') }}
              </div>
            </div>

            <div class="error-message" *ngIf="errorMessage">
              {{ errorMessage }}
            </div>

            <div class="success-message" *ngIf="successMessage">
              {{ successMessage }}
            </div>

            <ion-button 
              expand="block" 
              type="submit" 
              [disabled]="isLoading"
              class="register-button"
            >
              <ion-spinner *ngIf="isLoading" name="crescent"></ion-spinner>
              <span *ngIf="!isLoading">Send OTP</span>
            </ion-button>

          </ion-card-content>
        </ion-card>
      </form>
    </div>

    <!-- Step 2: OTP Verification -->
    <div *ngIf="step === 2">
      <form [formGroup]="otpForm" (ngSubmit)="verifyOtpAndRegister()">
        <ion-card class="form-card">
          <ion-card-content>
            
            <ion-item class="form-item">
              <ion-label position="stacked">Enter OTP</ion-label>
              <ion-input
                type="number"
                formControlName="otp"
                placeholder="Enter 6-digit OTP"
                maxlength="6"
                [class.ion-invalid]="otpForm.get('otp')?.invalid && otpForm.get('otp')?.touched"
              ></ion-input>
            </ion-item>
            
            <div class="error-message" *ngIf="getFieldError(otpForm, 'otp')">
              {{ getFieldError(otpForm, 'otp') }}
            </div>

            <div class="error-message" *ngIf="errorMessage">
              {{ errorMessage }}
            </div>

            <!-- OTP Timer -->
            <div class="otp-timer" *ngIf="otpTimer > 0">
              <ion-icon name="time-outline"></ion-icon>
              <span>Resend OTP in {{ getTimerDisplay() }}</span>
            </div>

            <!-- Resend OTP Button -->
            <div class="resend-section" *ngIf="canResendOtp">
              <ion-text color="medium">Didn't receive OTP?</ion-text>
              <ion-button 
                fill="clear" 
                color="primary" 
                (click)="resendOtp()"
                class="resend-button"
              >
                <ion-icon name="refresh-outline" slot="start"></ion-icon>
                Resend OTP
              </ion-button>
            </div>

            <ion-button 
              expand="block" 
              type="submit" 
              [disabled]="isOtpLoading"
              class="verify-button"
            >
              <ion-spinner *ngIf="isOtpLoading" name="crescent"></ion-spinner>
              <span *ngIf="!isOtpLoading">Verify & Register</span>
            </ion-button>

          </ion-card-content>
        </ion-card>
      </form>
    </div>

    <!-- Login Link -->
    <div class="login-link">
      <ion-text color="medium">
        <p>Already have an account? <a (click)="goToLogin()" class="link">Login here</a></p>
      </ion-text>
    </div>

  </div>
</ion-content>
