const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Name is required'],
    trim: true,
    maxlength: [50, 'Name cannot exceed 50 characters']
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [6, 'Password must be at least 6 characters']
  },
  role: {
    type: String,
    enum: ['driver', 'admin'],
    default: 'driver'
  },
  phone: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    match: [/^[0-9]{10}$/, 'Please enter a valid 10-digit phone number']
  },
  profileImage: {
    type: String,
    default: null
  },
  isActive: {
    type: Boolean,
    default: true
  },
  // Admin specific fields
  companyName: {
    type: String,
    required: function() { return this.role === 'admin'; }
  },
  gstNumber: {
    type: String,
    trim: true
  },
  // Driver specific fields
  aadhaarNumber: {
    type: String,
    required: function() { return this.role === 'driver'; }
  },
  aadhaarPhoto: {
    type: String, // Cloudinary URL
    required: function() { return this.role === 'driver'; }
  },
  driverPhoto: {
    type: String, // Cloudinary URL
    required: function() { return this.role === 'driver'; }
  },
  adminId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: function() { return this.role === 'driver'; }
  },
  companyId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: function() { return this.role === 'driver'; }
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended'],
    default: 'active'
  },
  // Subscription fields
  subscriptionStatus: {
    type: String,
    enum: ['trial', 'active', 'expired', 'cancelled'],
    default: 'trial'
  },
  trialStartDate: {
    type: Date,
    default: Date.now
  },
  trialEndDate: {
    type: Date,
    default: function() {
      return new Date(Date.now() + 3 * 24 * 60 * 60 * 1000); // 3 days from now
    }
  },
  subscriptionStartDate: {
    type: Date
  },
  subscriptionEndDate: {
    type: Date
  },
  razorpayCustomerId: {
    type: String
  },
  razorpaySubscriptionId: {
    type: String
  },
  licenseNumber: {
    type: String,
    trim: true,
    required: function() {
      return this.role === 'driver';
    }
  },
  vehicleDetails: {
    vehicleNumber: {
      type: String,
      trim: true,
      uppercase: true
    },
    vehicleType: {
      type: String,
      enum: ['car', 'truck', 'bike', 'auto', 'bus', 'other']
    },
    vehicleModel: String,
    fuelType: {
      type: String,
      enum: ['petrol', 'diesel', 'cng', 'electric']
    }
  },
  address: {
    street: String,
    city: String,
    state: String,
    pincode: String,
    country: {
      type: String,
      default: 'India'
    }
  },
  bankDetails: {
    accountNumber: String,
    ifscCode: String,
    bankName: String,
    accountHolderName: String
  },
  fcmToken: {
    type: String,
    default: null
  },
  lastLogin: {
    type: Date,
    default: Date.now
  },
  resetPasswordToken: String,
  resetPasswordExpire: Date
}, {
  timestamps: true
});

// Index for better query performance
userSchema.index({ email: 1 });
userSchema.index({ role: 1 });
userSchema.index({ isActive: 1 });

// Hash password before saving
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) {
    return next();
  }
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Compare password method
userSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

// Check if trial is expired
userSchema.methods.isTrialExpired = function() {
  return new Date() > this.trialEndDate;
};

// Check if subscription is active
userSchema.methods.hasActiveSubscription = function() {
  if (this.subscriptionStatus === 'active' && this.subscriptionEndDate) {
    return new Date() < this.subscriptionEndDate;
  }
  return false;
};

// Get trial days remaining
userSchema.methods.getTrialDaysRemaining = function() {
  if (this.isTrialExpired()) return 0;
  const diffTime = this.trialEndDate - new Date();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

// Get user without sensitive data
userSchema.methods.toJSON = function() {
  const user = this.toObject();
  delete user.password;
  delete user.resetPasswordToken;
  delete user.resetPasswordExpire;
  delete user.razorpayCustomerId;
  return user;
};

module.exports = mongoose.model('User', userSchema);
