import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import {
  IonContent,
  IonHeader,
  IonTitle,
  IonToolbar,
  IonButton,
  IonButtons,
  IonIcon,
  IonCard,
  IonCardContent,
  IonItem,
  IonLabel,
  IonInput,
  IonSpinner,
  IonBackButton,
  IonText,
  IonSelect,
  IonSelectOption,
  IonCheckbox
} from '@ionic/angular/standalone';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { addIcons } from 'ionicons';
import { personAddOutline, phonePortraitOutline, timeOutline, refreshOutline, eyeOutline, eyeOffOutline } from 'ionicons/icons';

@Component({
  selector: 'app-otp-register',
  templateUrl: './otp-register.page.html',
  styleUrls: ['./otp-register.page.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    IonContent,
    IonHeader,
    IonTitle,
    IonToolbar,
    IonButton,
    IonButtons,
    IonIcon,
    IonCard,
    IonCardContent,
    IonItem,
    IonLabel,
    IonInput,
    IonSpinner,
    IonBackButton,
    IonText,
    IonSelect,
    IonSelectOption,
    IonCheckbox
  ]
})
export class OtpRegisterPage implements OnInit {
  registrationForm: FormGroup;
  otpForm: FormGroup;
  
  step = 1; // 1: Registration details, 2: OTP verification
  isLoading = false;
  isOtpLoading = false;
  errorMessage = '';
  successMessage = '';
  
  phoneNumber = '';
  otpTimer = 0;
  otpTimerInterval: any;
  canResendOtp = false;
  showPassword = false;
  showConfirmPassword = false;
  
  private readonly API_BASE = 'http://localhost:3001/api';

  constructor(
    private formBuilder: FormBuilder,
    private http: HttpClient,
    private router: Router
  ) {
    addIcons({ 
      personAddOutline, 
      phonePortraitOutline, 
      timeOutline, 
      refreshOutline,
      eyeOutline,
      eyeOffOutline
    });
    
    this.registrationForm = this.formBuilder.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', [Validators.required]],
      role: ['driver', [Validators.required]],
      licenseNumber: [''],
      vehicleDetails: [''],
      address: [''],
      companyName: [''],
      agreeToTerms: [false, [Validators.requiredTrue]]
    }, { validators: this.passwordMatchValidator });
    
    this.otpForm = this.formBuilder.group({
      otp: ['', [Validators.required, Validators.pattern(/^[0-9]{6}$/)]]
    });
  }

  ngOnInit() {}

  ngOnDestroy() {
    if (this.otpTimerInterval) {
      clearInterval(this.otpTimerInterval);
    }
  }

  passwordMatchValidator(form: FormGroup) {
    const password = form.get('password');
    const confirmPassword = form.get('confirmPassword');
    
    if (password && confirmPassword && password.value !== confirmPassword.value) {
      confirmPassword.setErrors({ passwordMismatch: true });
    } else if (confirmPassword?.errors?.['passwordMismatch']) {
      delete confirmPassword.errors['passwordMismatch'];
      if (Object.keys(confirmPassword.errors).length === 0) {
        confirmPassword.setErrors(null);
      }
    }
    return null;
  }

  getFieldError(form: FormGroup, fieldName: string): string {
    const field = form.get(fieldName);
    if (field && field.invalid && field.touched) {
      if (field.errors?.['required']) {
        return `${this.getFieldDisplayName(fieldName)} is required`;
      }
      if (field.errors?.['email']) {
        return 'Please enter a valid email address';
      }
      if (field.errors?.['pattern']) {
        if (fieldName === 'phone') {
          return 'Please enter a valid 10-digit phone number';
        }
        if (fieldName === 'otp') {
          return 'Please enter a valid 6-digit OTP';
        }
      }
      if (field.errors?.['minlength']) {
        return `${this.getFieldDisplayName(fieldName)} must be at least ${field.errors?.['minlength'].requiredLength} characters`;
      }
      if (field.errors?.['passwordMismatch']) {
        return 'Passwords do not match';
      }
      if (field.errors?.['requiredTrue']) {
        return 'You must agree to the terms and conditions';
      }
    }
    return '';
  }

  private getFieldDisplayName(fieldName: string): string {
    const displayNames: { [key: string]: string } = {
      name: 'Name',
      email: 'Email',
      phone: 'Phone number',
      password: 'Password',
      confirmPassword: 'Confirm password',
      role: 'Role',
      licenseNumber: 'License number',
      vehicleDetails: 'Vehicle details',
      address: 'Address',
      companyName: 'Company name',
      otp: 'OTP'
    };
    return displayNames[fieldName] || fieldName;
  }

  togglePassword() {
    this.showPassword = !this.showPassword;
  }

  toggleConfirmPassword() {
    this.showConfirmPassword = !this.showConfirmPassword;
  }

  onRoleChange() {
    const role = this.registrationForm.get('role')?.value;
    const licenseControl = this.registrationForm.get('licenseNumber');
    const vehicleControl = this.registrationForm.get('vehicleDetails');
    const companyControl = this.registrationForm.get('companyName');
    
    if (role === 'driver') {
      licenseControl?.setValidators([Validators.required]);
      vehicleControl?.setValidators([Validators.required]);
      companyControl?.clearValidators();
    } else {
      licenseControl?.clearValidators();
      vehicleControl?.clearValidators();
      companyControl?.setValidators([Validators.required]);
    }
    
    licenseControl?.updateValueAndValidity();
    vehicleControl?.updateValueAndValidity();
    companyControl?.updateValueAndValidity();
  }

  sendOtp() {
    if (this.registrationForm.invalid) {
      this.markFormGroupTouched(this.registrationForm);
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';
    
    const phone = this.registrationForm.value.phone;
    this.phoneNumber = phone;

    console.log('Sending OTP for registration to:', phone);

    this.http.post(`${this.API_BASE}/otp/send`, {
      phone: phone,
      purpose: 'registration'
    }).subscribe({
      next: (response: any) => {
        console.log('OTP send response:', response);
        if (response.success) {
          this.step = 2;
          this.successMessage = 'OTP sent successfully to your mobile number';
          this.startOtpTimer();
        } else {
          this.errorMessage = response.message || 'Failed to send OTP';
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Send OTP error:', error);
        this.errorMessage = error.error?.message || 'Failed to send OTP. Please try again.';
        this.isLoading = false;
      }
    });
  }

  verifyOtpAndRegister() {
    if (this.otpForm.invalid) {
      this.markFormGroupTouched(this.otpForm);
      return;
    }

    this.isOtpLoading = true;
    this.errorMessage = '';

    const otpData = {
      phone: this.phoneNumber,
      otp: this.otpForm.value.otp,
      purpose: 'registration'
    };

    console.log('Verifying OTP:', otpData);

    this.http.post(`${this.API_BASE}/otp/verify`, otpData).subscribe({
      next: (response: any) => {
        console.log('OTP verify response:', response);
        if (response.success) {
          this.clearOtpTimer();
          // OTP verified, now register the user
          this.registerUser();
        } else {
          this.errorMessage = response.message || 'Invalid OTP';
          this.isOtpLoading = false;
        }
      },
      error: (error) => {
        console.error('Verify OTP error:', error);
        this.errorMessage = error.error?.message || 'OTP verification failed. Please try again.';
        this.isOtpLoading = false;
      }
    });
  }

  private registerUser() {
    const formData = this.registrationForm.value;
    delete formData.confirmPassword;
    delete formData.agreeToTerms;

    console.log('Registering user:', formData);

    this.http.post(`${this.API_BASE}/auth/register`, formData).subscribe({
      next: (response: any) => {
        console.log('Registration response:', response);
        if (response.success) {
          // Store token and user data
          localStorage.setItem('token', response.data.token);
          localStorage.setItem('user', JSON.stringify(response.data.user));
          
          // Navigate based on user role
          if (response.data.user.role === 'admin') {
            this.router.navigate(['/admin-dashboard']);
          } else {
            this.router.navigate(['/driver-dashboard']);
          }
        } else {
          this.errorMessage = response.message || 'Registration failed';
        }
        this.isOtpLoading = false;
      },
      error: (error) => {
        console.error('Registration error:', error);
        this.errorMessage = error.error?.message || 'Registration failed. Please try again.';
        this.isOtpLoading = false;
      }
    });
  }

  resendOtp() {
    if (!this.canResendOtp) return;
    
    this.clearOtpTimer();
    this.otpForm.reset();
    this.sendOtp();
  }

  private startOtpTimer() {
    this.otpTimer = 300; // 5 minutes
    this.canResendOtp = false;
    
    this.otpTimerInterval = setInterval(() => {
      this.otpTimer--;
      if (this.otpTimer <= 0) {
        this.clearOtpTimer();
        this.canResendOtp = true;
      }
    }, 1000);
  }

  private clearOtpTimer() {
    if (this.otpTimerInterval) {
      clearInterval(this.otpTimerInterval);
      this.otpTimerInterval = null;
    }
    this.otpTimer = 0;
  }

  getTimerDisplay(): string {
    const minutes = Math.floor(this.otpTimer / 60);
    const seconds = this.otpTimer % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  private markFormGroupTouched(formGroup: FormGroup) {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }

  goBack() {
    if (this.step === 2) {
      this.step = 1;
      this.clearOtpTimer();
      this.otpForm.reset();
      this.errorMessage = '';
      this.successMessage = '';
    } else {
      this.router.navigate(['/login']);
    }
  }

  goToLogin() {
    this.router.navigate(['/login']);
  }
}
