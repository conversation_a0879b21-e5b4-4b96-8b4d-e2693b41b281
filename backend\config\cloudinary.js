const cloudinary = require('cloudinary').v2;
const { CloudinaryStorage } = require('multer-storage-cloudinary');
const multer = require('multer');

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME || 'demo',
  api_key: process.env.CLOUDINARY_API_KEY || 'demo',
  api_secret: process.env.CLOUDINARY_API_SECRET || 'demo'
});

// Storage configuration for different file types
const createCloudinaryStorage = (folder, allowedFormats = ['jpg', 'jpeg', 'png', 'pdf']) => {
  return new CloudinaryStorage({
    cloudinary: cloudinary,
    params: {
      folder: `driversetuapp/${folder}`,
      allowed_formats: allowedFormats,
      transformation: [
        { width: 1000, height: 1000, crop: 'limit', quality: 'auto' }
      ]
    }
  });
};

// Storage configurations
const aadhaarStorage = createCloudinaryStorage('aadhaar_photos', ['jpg', 'jpeg', 'png']);
const driverPhotoStorage = createCloudinaryStorage('driver_photos', ['jpg', 'jpeg', 'png']);
const billStorage = createCloudinaryStorage('bill_images', ['jpg', 'jpeg', 'png', 'pdf']);

// Multer configurations
const uploadAadhaar = multer({
  storage: aadhaarStorage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed for Aadhaar photos'), false);
    }
  }
});

const uploadDriverPhoto = multer({
  storage: driverPhotoStorage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed for driver photos'), false);
    }
  }
});

const uploadBillImages = multer({
  storage: billStorage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/') || file.mimetype === 'application/pdf') {
      cb(null, true);
    } else {
      cb(new Error('Only image files and PDFs are allowed for bills'), false);
    }
  }
});

// Helper function to delete files from Cloudinary
const deleteFromCloudinary = async (publicId) => {
  try {
    const result = await cloudinary.uploader.destroy(publicId);
    return result;
  } catch (error) {
    console.error('Error deleting from Cloudinary:', error);
    throw error;
  }
};

// Fallback local storage for development
const localStorage = multer({
  dest: 'uploads/',
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  }
});

// Check if Cloudinary is configured
const isCloudinaryConfigured = () => {
  return process.env.CLOUDINARY_CLOUD_NAME && 
         process.env.CLOUDINARY_API_KEY && 
         process.env.CLOUDINARY_API_SECRET;
};

module.exports = {
  cloudinary,
  uploadAadhaar: isCloudinaryConfigured() ? uploadAadhaar : localStorage,
  uploadDriverPhoto: isCloudinaryConfigured() ? uploadDriverPhoto : localStorage,
  uploadBillImages: isCloudinaryConfigured() ? uploadBillImages : localStorage,
  deleteFromCloudinary,
  isCloudinaryConfigured
};
