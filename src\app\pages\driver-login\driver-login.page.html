<ion-header [translucent]="true">
  <ion-toolbar class="gradient-toolbar">
    <ion-title class="animated-title">
      <ion-icon name="car-sport" class="title-icon"></ion-icon>
      Driver Login
    </ion-title>
    <ion-buttons slot="start">
      <ion-button (click)="goBack()" fill="clear" class="back-btn">
        <ion-icon name="arrow-back-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="login-content">
  <div class="login-container">
    <!-- Welcome Section -->
    <div class="welcome-section">
      <div class="logo-container">
        <ion-icon name="car-sport" class="logo-icon"></ion-icon>
        <h1 class="app-title">DriverSetu</h1>
        <p class="welcome-text">Driver Portal</p>
      </div>
    </div>

    <!-- Login Form -->
    <div class="form-section">
      <ion-card class="login-card">
        <ion-card-content>
          <h2 class="form-title">Welcome Back!</h2>
          <p class="form-subtitle">Sign in to your driver account</p>

          <form [formGroup]="driverLoginForm" (ngSubmit)="login()">
            <!-- Phone Number -->
            <div class="input-group">
              <ion-item class="custom-item">
                <ion-label position="stacked">Phone Number</ion-label>
                <ion-input
                  formControlName="phone"
                  type="tel"
                  placeholder="Enter your phone number"
                  class="custom-input">
                </ion-input>
              </ion-item>
              <div class="error-message" *ngIf="getFieldError('phone')">
                {{ getFieldError('phone') }}
              </div>
            </div>

            <!-- Password -->
            <div class="input-group">
              <ion-item class="custom-item">
                <ion-label position="stacked">Password</ion-label>
                <ion-input
                  formControlName="password"
                  [type]="showPassword ? 'text' : 'password'"
                  placeholder="Enter your password"
                  class="custom-input">
                </ion-input>
                <ion-button
                  fill="clear"
                  slot="end"
                  (click)="togglePassword()"
                  class="password-toggle">
                  <ion-icon [name]="showPassword ? 'eye-off-outline' : 'eye-outline'"></ion-icon>
                </ion-button>
              </ion-item>
              <div class="error-message" *ngIf="getFieldError('password')">
                {{ getFieldError('password') }}
              </div>
            </div>

            <!-- Login Button -->
            <ion-button
              expand="block"
              type="submit"
              class="login-btn"
              [disabled]="driverLoginForm.invalid || isLoading">
              <ion-spinner name="crescent" *ngIf="isLoading"></ion-spinner>
              <span *ngIf="!isLoading">
                <ion-icon name="log-in-outline" slot="start"></ion-icon>
                Sign In
              </span>
            </ion-button>

            <!-- Error Message -->
            <div class="error-alert" *ngIf="errorMessage">
              <ion-icon name="alert-circle-outline"></ion-icon>
              {{ errorMessage }}
            </div>
          </form>

          <!-- Forgot Password -->
          <div class="forgot-section">
            <ion-button fill="clear" (click)="goToForgotPassword()" class="forgot-btn">
              Forgot Password?
            </ion-button>
          </div>

          <!-- Alternative Login Options -->
          <div class="alternative-options">
            <div class="divider">
              <span>OR</span>
            </div>

            <ion-button fill="outline" expand="block" (click)="goToOtpLogin()" class="otp-login-btn">
              <ion-icon name="phone-portrait-outline" slot="start"></ion-icon>
              Login with OTP
            </ion-button>
          </div>

          <!-- Admin Login Link -->
          <div class="admin-link">
            <p>Are you an admin?</p>
            <ion-button fill="clear" (click)="goToAdminLogin()" class="admin-btn">
              <ion-icon name="business-outline" slot="start"></ion-icon>
              Admin Login
            </ion-button>
          </div>
        </ion-card-content>
      </ion-card>
    </div>
  </div>
</ion-content>
