// Enhanced Animations
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.stats-container {
  padding: 20px;
  position: relative;
}

.stat-card {
  margin: 12px 0;
  text-align: center;
  border-radius: 20px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  animation: fadeInUp 0.6s ease-out;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(102, 126, 234, 0.1),
      transparent
    );
    animation: shimmer 3s infinite;
  }

  &:hover {
    transform: translateY(-8px);
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.15),
      0 0 0 1px rgba(255, 255, 255, 0.3);
    animation: float 2s ease-in-out infinite;
  }
}

.stat-card ion-card-content {
  padding: 24px;
  position: relative;
  z-index: 1;
}

.stat-card h3 {
  font-size: 2.5rem;
  font-weight: 800;
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: pulse 2s ease-in-out infinite;
}

.stat-card p {
  margin: 8px 0 0 0;
  color: #666;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.filter-buttons {
  padding: 16px;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.trip-actions {
  margin-top: 12px;
  display: flex;
  gap: 8px;
}

ion-segment {
  margin: 16px;
}

ion-searchbar {
  padding: 0 16px;
}

ion-list {
  padding: 0 16px;
}

ion-item {
  --border-radius: 8px;
  --background: var(--ion-color-light);
  margin-bottom: 12px;
}

ion-chip {
  font-size: 0.8rem;
}

// Tab header styling
.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: linear-gradient(135deg, var(--ion-color-primary), var(--ion-color-secondary));
  color: white;
  margin: 16px;
  border-radius: 12px;

  h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
  }

  ion-button {
    --color: white;
    --border-color: white;
  }
}

// Subscription card styling
.subscription-card {
  margin: 16px;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;

  ion-card-header {
    padding-bottom: 0;
  }

  ion-card-title {
    color: white;
    font-size: 1.3rem;
    font-weight: 600;
  }

  .subscription-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
    margin: 16px 0;
  }

  .sub-detail {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    backdrop-filter: blur(10px);

    ion-icon {
      font-size: 1.5rem;
      color: rgba(255, 255, 255, 0.9);
    }

    h4 {
      margin: 0;
      font-size: 0.9rem;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.9);
    }

    p {
      margin: 4px 0 0 0;
      font-size: 1.1rem;
      font-weight: 700;
      color: white;
    }
  }

  .upgrade-btn {
    margin-top: 16px;
    --color: white;
    --border-color: white;
    --background-hover: rgba(255, 255, 255, 0.1);
  }
}

// Driver list styling
ion-avatar {
  width: 60px;
  height: 60px;
  border: 3px solid var(--ion-color-primary);

  img {
    border-radius: 50%;
    object-fit: cover;
  }
}

// Enhanced item styling
ion-item {
  --border-radius: 12px;
  --background: white;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
  }
}

// List header styling
ion-list-header {
  background: var(--ion-color-light);
  border-radius: 8px;
  margin: 16px 0 8px 0;
  font-weight: 600;
  color: var(--ion-color-primary);
}

// Enhanced toolbar
ion-toolbar {
  --background: linear-gradient(135deg, var(--ion-color-primary), var(--ion-color-secondary));
  --color: white;

  ion-title {
    font-weight: 600;
    font-size: 1.3rem;
  }

  ion-button {
    --color: white;
  }
}

// Segment styling
ion-segment {
  background: var(--ion-color-light);
  border-radius: 12px;
  padding: 4px;

  ion-segment-button {
    --color: var(--ion-color-medium);
    --color-checked: var(--ion-color-primary);
    --indicator-color: var(--ion-color-primary);
    border-radius: 8px;
    font-weight: 600;
  }
}

@media (max-width: 768px) {
  .filter-buttons {
    justify-content: center;
  }

  .trip-actions {
    flex-direction: column;
  }

  .trip-actions ion-button {
    margin: 2px 0;
  }

  .tab-header {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .subscription-info {
    grid-template-columns: 1fr;
  }
}