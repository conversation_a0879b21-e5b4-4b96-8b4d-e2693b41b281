const mongoose = require('mongoose');

const tripSchema = new mongoose.Schema({
  driver: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Driver is required']
  },
  date: {
    type: Date,
    required: [true, 'Trip date is required'],
    default: Date.now
  },
  fromLocation: {
    type: String,
    required: [true, 'From location is required'],
    trim: true
  },
  toLocation: {
    type: String,
    required: [true, 'To location is required'],
    trim: true
  },
  vehicle: {
    type: String,
    required: [true, 'Vehicle number is required'],
    trim: true,
    uppercase: true
  },
  km: {
    type: Number,
    required: [true, 'Distance in KM is required'],
    min: [0, 'Distance cannot be negative']
  },
  amount: {
    type: Number,
    required: [true, 'Amount received is required'],
    min: [0, 'Amount cannot be negative']
  },
  expenses: {
    fuel: {
      type: Number,
      default: 0,
      min: [0, 'Fuel expense cannot be negative']
    },
    food: {
      type: Number,
      default: 0,
      min: [0, 'Food expense cannot be negative']
    },
    other: {
      type: Number,
      default: 0,
      min: [0, 'Other expense cannot be negative']
    }
  },
  remarks: {
    type: String,
    trim: true,
    maxlength: [500, 'Remarks cannot exceed 500 characters']
  },
  billImages: [{
    filename: String,
    originalName: String,
    path: String, // Local path for backward compatibility
    cloudinaryUrl: String, // Cloudinary URL
    cloudinaryPublicId: String, // For deletion
    size: Number,
    uploadDate: {
      type: Date,
      default: Date.now
    }
  }],
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending'
  },
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  approvedAt: Date,
  rejectionReason: String,
  coordinates: {
    start: {
      latitude: Number,
      longitude: Number
    },
    end: {
      latitude: Number,
      longitude: Number
    }
  },
  route: [{
    latitude: Number,
    longitude: Number,
    timestamp: Date
  }],
  totalExpense: {
    type: Number,
    default: 0
  },
  profit: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

// Calculate total expense and profit before saving
tripSchema.pre('save', function(next) {
  this.totalExpense = (this.expenses.fuel || 0) + (this.expenses.food || 0) + (this.expenses.other || 0);
  this.profit = this.amount - this.totalExpense;
  next();
});

// Indexes for better query performance
tripSchema.index({ driver: 1, date: -1 });
tripSchema.index({ status: 1 });
tripSchema.index({ date: -1 });
tripSchema.index({ vehicle: 1 });

// Virtual for formatted date
tripSchema.virtual('formattedDate').get(function() {
  return this.date.toLocaleDateString('en-IN');
});

// Static method to get trip statistics
tripSchema.statics.getDriverStats = function(driverId, startDate, endDate) {
  return this.aggregate([
    {
      $match: {
        driver: mongoose.Types.ObjectId(driverId),
        date: {
          $gte: startDate,
          $lte: endDate
        }
      }
    },
    {
      $group: {
        _id: null,
        totalTrips: { $sum: 1 },
        totalKm: { $sum: '$km' },
        totalAmount: { $sum: '$amount' },
        totalExpenses: { $sum: '$totalExpense' },
        totalProfit: { $sum: '$profit' },
        avgKmPerTrip: { $avg: '$km' },
        avgAmountPerTrip: { $avg: '$amount' }
      }
    }
  ]);
};

module.exports = mongoose.model('Trip', tripSchema);
