import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import {
  IonContent,
  IonHeader,
  IonTitle,
  IonToolbar,
  IonButton,
  IonButtons,
  IonIcon,
  IonCard,
  IonCardContent,
  IonItem,
  IonLabel,
  IonInput,
  IonSpinner,
  IonBackButton,
  IonText,
  IonGrid,
  IonRow,
  IonCol
} from '@ionic/angular/standalone';
import { HttpClient } from '@angular/common/http';
import { Router, ActivatedRoute } from '@angular/router';
import { addIcons } from 'ionicons';
import { phonePortraitOutline, timeOutline, refreshOutline } from 'ionicons/icons';

@Component({
  selector: 'app-otp-login',
  templateUrl: './otp-login.page.html',
  styleUrls: ['./otp-login.page.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    IonContent,
    IonHeader,
    IonTitle,
    IonToolbar,
    IonButton,
    IonButtons,
    IonIcon,
    IonCard,
    IonCardContent,
    IonItem,
    IonLabel,
    IonInput,
    IonSpinner,
    IonBackButton,
    IonText,
    IonGrid,
    IonRow,
    IonCol
  ]
})
export class OtpLoginPage implements OnInit {
  phoneForm: FormGroup;
  otpForm: FormGroup;
  
  step = 1; // 1: Phone input, 2: OTP verification
  isLoading = false;
  isOtpLoading = false;
  errorMessage = '';
  successMessage = '';
  
  phoneNumber = '';
  otpTimer = 0;
  otpTimerInterval: any;
  canResendOtp = false;
  
  private readonly API_BASE = 'http://localhost:3001/api';

  constructor(
    private formBuilder: FormBuilder,
    private http: HttpClient,
    private router: Router,
    private route: ActivatedRoute
  ) {
    addIcons({ phonePortraitOutline, timeOutline, refreshOutline });
    
    this.phoneForm = this.formBuilder.group({
      phone: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]]
    });
    
    this.otpForm = this.formBuilder.group({
      otp: ['', [Validators.required, Validators.pattern(/^[0-9]{6}$/)]]
    });
  }

  ngOnInit() {
    // Check if phone number was passed from previous page
    this.route.queryParams.subscribe(params => {
      if (params['phone']) {
        this.phoneForm.patchValue({ phone: params['phone'] });
      }
    });
  }

  ngOnDestroy() {
    if (this.otpTimerInterval) {
      clearInterval(this.otpTimerInterval);
    }
  }

  getFieldError(form: FormGroup, fieldName: string): string {
    const field = form.get(fieldName);
    if (field && field.invalid && field.touched) {
      if (field.errors?.['required']) {
        return `${this.getFieldDisplayName(fieldName)} is required`;
      }
      if (field.errors?.['pattern']) {
        if (fieldName === 'phone') {
          return 'Please enter a valid 10-digit phone number';
        }
        if (fieldName === 'otp') {
          return 'Please enter a valid 6-digit OTP';
        }
      }
    }
    return '';
  }

  private getFieldDisplayName(fieldName: string): string {
    const displayNames: { [key: string]: string } = {
      phone: 'Phone number',
      otp: 'OTP'
    };
    return displayNames[fieldName] || fieldName;
  }

  sendOtp() {
    if (this.phoneForm.invalid) {
      this.markFormGroupTouched(this.phoneForm);
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';
    
    const phone = this.phoneForm.value.phone;
    this.phoneNumber = phone;

    console.log('Sending OTP to:', phone);

    this.http.post(`${this.API_BASE}/otp/send`, {
      phone: phone,
      purpose: 'login'
    }).subscribe({
      next: (response: any) => {
        console.log('OTP send response:', response);
        if (response.success) {
          this.step = 2;
          this.successMessage = 'OTP sent successfully to your mobile number';
          this.startOtpTimer();
        } else {
          this.errorMessage = response.message || 'Failed to send OTP';
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Send OTP error:', error);
        this.errorMessage = error.error?.message || 'Failed to send OTP. Please try again.';
        this.isLoading = false;
      }
    });
  }

  verifyOtp() {
    if (this.otpForm.invalid) {
      this.markFormGroupTouched(this.otpForm);
      return;
    }

    this.isOtpLoading = true;
    this.errorMessage = '';

    const otpData = {
      phone: this.phoneNumber,
      otp: this.otpForm.value.otp,
      purpose: 'login'
    };

    console.log('Verifying OTP:', otpData);

    this.http.post(`${this.API_BASE}/otp/verify`, otpData).subscribe({
      next: (response: any) => {
        console.log('OTP verify response:', response);
        if (response.success) {
          this.clearOtpTimer();
          // OTP verified, now login the user
          this.loginWithPhone();
        } else {
          this.errorMessage = response.message || 'Invalid OTP';
        }
        this.isOtpLoading = false;
      },
      error: (error) => {
        console.error('Verify OTP error:', error);
        this.errorMessage = error.error?.message || 'OTP verification failed. Please try again.';
        this.isOtpLoading = false;
      }
    });
  }

  private loginWithPhone() {
    // Login user with phone number (since OTP is verified)
    this.http.post(`${this.API_BASE}/auth/login`, {
      phone: this.phoneNumber,
      otpVerified: true
    }).subscribe({
      next: (response: any) => {
        console.log('Login response:', response);
        if (response.success) {
          // Store token and user data
          localStorage.setItem('token', response.data.token);
          localStorage.setItem('user', JSON.stringify(response.data.user));
          
          // Navigate based on user role
          if (response.data.user.role === 'admin') {
            this.router.navigate(['/admin-dashboard']);
          } else {
            this.router.navigate(['/driver-dashboard']);
          }
        } else {
          this.errorMessage = response.message || 'Login failed';
        }
      },
      error: (error) => {
        console.error('Login error:', error);
        this.errorMessage = error.error?.message || 'Login failed. Please try again.';
      }
    });
  }

  resendOtp() {
    if (!this.canResendOtp) return;
    
    this.clearOtpTimer();
    this.otpForm.reset();
    this.sendOtp();
  }

  private startOtpTimer() {
    this.otpTimer = 300; // 5 minutes
    this.canResendOtp = false;
    
    this.otpTimerInterval = setInterval(() => {
      this.otpTimer--;
      if (this.otpTimer <= 0) {
        this.clearOtpTimer();
        this.canResendOtp = true;
      }
    }, 1000);
  }

  private clearOtpTimer() {
    if (this.otpTimerInterval) {
      clearInterval(this.otpTimerInterval);
      this.otpTimerInterval = null;
    }
    this.otpTimer = 0;
  }

  getTimerDisplay(): string {
    const minutes = Math.floor(this.otpTimer / 60);
    const seconds = this.otpTimer % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  private markFormGroupTouched(formGroup: FormGroup) {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }

  goBack() {
    if (this.step === 2) {
      this.step = 1;
      this.clearOtpTimer();
      this.otpForm.reset();
      this.errorMessage = '';
      this.successMessage = '';
    } else {
      this.router.navigate(['/login']);
    }
  }

  goToPasswordLogin() {
    this.router.navigate(['/driver-login']);
  }
}
