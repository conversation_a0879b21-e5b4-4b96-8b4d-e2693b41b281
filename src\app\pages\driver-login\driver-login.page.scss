// Gradient Toolbar
.gradient-toolbar {
  --background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --color: white;
}

.animated-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  animation: slideInDown 0.6s ease-out;
}

.title-icon {
  font-size: 24px;
  animation: pulse 2s infinite;
}

.back-btn {
  --color: white;
  --background-hover: rgba(255,255,255,0.1);
}

// Login Content
.login-content {
  --background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
}

.login-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 20px;
}

// Welcome Section
.welcome-section {
  text-align: center;
  padding: 40px 0;
  animation: fadeInUp 0.8s ease-out;
}

.logo-container {
  .logo-icon {
    font-size: 80px;
    color: #667eea;
    margin-bottom: 16px;
    animation: bounce 2s infinite;
    filter: drop-shadow(0 4px 8px rgba(102, 126, 234, 0.3));
  }

  .app-title {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0 0 8px 0;
    animation: slideInLeft 0.8s ease-out 0.2s both;
  }

  .welcome-text {
    font-size: 1.1rem;
    color: #666;
    margin: 0;
    animation: slideInRight 0.8s ease-out 0.4s both;
  }
}

// Form Section
.form-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeInUp 0.8s ease-out 0.6s both;
}

.login-card {
  width: 100%;
  max-width: 400px;
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0,0,0,0.1);
  background: white;
  overflow: hidden;
  animation: scaleIn 0.6s ease-out 0.8s both;
}

.form-title {
  text-align: center;
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.form-subtitle {
  text-align: center;
  color: #666;
  margin: 0 0 32px 0;
  font-size: 0.95rem;
}

// Input Groups
.input-group {
  margin-bottom: 24px;
}

.custom-item {
  --background: #f8f9fa;
  --border-radius: 12px;
  --padding-start: 16px;
  --padding-end: 16px;
  --min-height: 56px;
  margin-bottom: 8px;
  transition: all 0.3s ease;

  &.item-has-focus {
    --background: white;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
  }

  ion-label {
    font-weight: 500;
    color: #667eea;
    font-size: 0.9rem;
  }

  .custom-input {
    --color: #333;
    font-size: 1rem;
  }
}

.password-toggle {
  --color: #667eea;
  --background-hover: rgba(102, 126, 234, 0.1);
}

.error-message {
  color: #e74c3c;
  font-size: 0.8rem;
  margin-top: 4px;
  margin-left: 16px;
  animation: shake 0.5s ease-in-out;
}

// Login Button
.login-btn {
  margin: 32px 0 24px 0;
  --background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --border-radius: 12px;
  --padding-top: 16px;
  --padding-bottom: 16px;
  font-weight: 600;
  font-size: 1rem;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
  transition: transform 0.2s ease;

  &:hover:not([disabled]) {
    transform: translateY(-2px);
  }

  &[disabled] {
    opacity: 0.6;
  }

  ion-spinner {
    margin-right: 8px;
  }
}

// Error Alert
.error-alert {
  background: rgba(231, 76, 60, 0.1);
  border: 1px solid rgba(231, 76, 60, 0.2);
  border-radius: 8px;
  padding: 12px;
  margin-top: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #e74c3c;
  font-size: 0.9rem;
  animation: slideInUp 0.3s ease-out;

  ion-icon {
    font-size: 18px;
  }
}

// Forgot Section
.forgot-section {
  text-align: center;
  margin: 16px 0;
}

.forgot-btn {
  --color: #667eea;
  font-size: 0.9rem;
  text-decoration: underline;
}

// Alternative Options
.alternative-options {
  margin-top: 20px;

  .divider {
    text-align: center;
    margin: 16px 0;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      height: 1px;
      background: #e0e0e0;
    }

    span {
      background: white;
      padding: 0 16px;
      color: #999;
      font-size: 0.9rem;
      font-weight: 500;
    }
  }
}

.otp-login-btn {
  --border-color: #28a745;
  --color: #28a745;
  --border-radius: 12px;
  --padding-top: 14px;
  --padding-bottom: 14px;
  font-weight: 500;
  margin-top: 12px;
  transition: all 0.3s ease;

  &:hover {
    --background: rgba(40, 167, 69, 0.1);
    transform: translateY(-2px);
  }
}

// Admin Link
.admin-link {
  text-align: center;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #eee;

  p {
    margin: 0 0 8px 0;
    color: #666;
    font-size: 0.9rem;
  }
}

.admin-btn {
  --color: #667eea;
  --background-hover: rgba(102, 126, 234, 0.1);
  font-weight: 500;
}

// Animations
@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}
