var u=(e,t)=>{e.componentOnReady?e.componentOnReady().then(a=>t(a)):s(()=>t(e))},c=e=>e.componentOnReady!==void 0,r=(e,t=[])=>{let a={};return t.forEach(n=>{e.hasAttribute(n)&&(e.getAttribute(n)!==null&&(a[n]=e.getAttribute(n)),e.removeAttribute(n))}),a},i=["role","aria-activedescendant","aria-atomic","aria-autocomplete","aria-braillelabel","aria-brailleroledescription","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colindextext","aria-colspan","aria-controls","aria-current","aria-describedby","aria-description","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowindextext","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext"],d=(e,t)=>r(e,i),l=(e,t,a,n)=>e.addEventListener(t,a,n),f=(e,t,a,n)=>e.removeEventListener(t,a,n),m=(e,t=e)=>e.shadowRoot||t,s=e=>typeof __zone_symbol__requestAnimationFrame=="function"?__zone_symbol__requestAnimationFrame(e):typeof requestAnimationFrame=="function"?requestAnimationFrame(e):setTimeout(e);var b=e=>{if(e.focus(),e.classList.contains("ion-focusable")){let t=e.closest("ion-app");t&&t.setFocus([e])}};var h=(e,t,a)=>Math.max(e,Math.min(t,a));var p=e=>{if(e){let t=e.changedTouches;if(t&&t.length>0){let a=t[0];return{x:a.clientX,y:a.clientY}}if(e.pageX!==void 0)return{x:e.pageX,y:e.pageY}}return{x:0,y:0}};var y=(e,t)=>{if(e??(e={}),t??(t={}),e===t)return!0;let a=Object.keys(e);if(a.length!==Object.keys(t).length)return!1;for(let n of a)if(!(n in t)||e[n]!==t[n])return!1;return!0};export{u as a,c as b,r as c,d,l as e,f,m as g,s as h,b as i,h as j,p as k,y as l};
