const Razorpay = require('razorpay');

// Initialize Razorpay instance
const razorpay = new Razorpay({
  key_id: process.env.RAZORPAY_KEY_ID || 'demo_key',
  key_secret: process.env.RAZORPAY_KEY_SECRET || 'demo_secret'
});

// Subscription plans configuration
const SUBSCRIPTION_PLANS = {
  monthly: {
    id: process.env.RAZORPAY_MONTHLY_PLAN_ID || 'plan_monthly_demo',
    amount: 99900, // ₹999 per month in paise
    currency: 'INR',
    interval: 1,
    period: 'monthly',
    name: 'DriverSetu Monthly Plan',
    description: 'Monthly subscription for up to 20 drivers'
  },
  yearly: {
    id: process.env.RAZORPAY_YEARLY_PLAN_ID || 'plan_yearly_demo',
    amount: 999900, // ₹9999 per year in paise (2 months free)
    currency: 'INR',
    interval: 1,
    period: 'yearly',
    name: 'DriverSetu Yearly Plan',
    description: 'Yearly subscription for up to 20 drivers (2 months free)'
  }
};

// Create subscription
const createSubscription = async (planId, customerId, notes = {}) => {
  try {
    const subscription = await razorpay.subscriptions.create({
      plan_id: planId,
      customer_id: customerId,
      total_count: 12, // For yearly plans
      quantity: 1,
      notes: {
        company: notes.companyName || '',
        admin_email: notes.adminEmail || '',
        ...notes
      }
    });
    
    return subscription;
  } catch (error) {
    console.error('Error creating subscription:', error);
    throw error;
  }
};

// Create customer
const createCustomer = async (customerData) => {
  try {
    const customer = await razorpay.customers.create({
      name: customerData.name,
      email: customerData.email,
      contact: customerData.phone,
      notes: {
        company: customerData.companyName || '',
        role: 'admin'
      }
    });
    
    return customer;
  } catch (error) {
    console.error('Error creating customer:', error);
    throw error;
  }
};

// Verify payment signature
const verifyPaymentSignature = (orderId, paymentId, signature) => {
  const crypto = require('crypto');
  const expectedSignature = crypto
    .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET || 'demo_secret')
    .update(orderId + '|' + paymentId)
    .digest('hex');
    
  return expectedSignature === signature;
};

// Create order for one-time payments
const createOrder = async (amount, currency = 'INR', notes = {}) => {
  try {
    const order = await razorpay.orders.create({
      amount: amount, // Amount in paise
      currency: currency,
      notes: notes
    });
    
    return order;
  } catch (error) {
    console.error('Error creating order:', error);
    throw error;
  }
};

// Cancel subscription
const cancelSubscription = async (subscriptionId) => {
  try {
    const subscription = await razorpay.subscriptions.cancel(subscriptionId);
    return subscription;
  } catch (error) {
    console.error('Error cancelling subscription:', error);
    throw error;
  }
};

// Get subscription details
const getSubscription = async (subscriptionId) => {
  try {
    const subscription = await razorpay.subscriptions.fetch(subscriptionId);
    return subscription;
  } catch (error) {
    console.error('Error fetching subscription:', error);
    throw error;
  }
};

// Check if Razorpay is configured
const isRazorpayConfigured = () => {
  return process.env.RAZORPAY_KEY_ID && 
         process.env.RAZORPAY_KEY_SECRET;
};

module.exports = {
  razorpay,
  SUBSCRIPTION_PLANS,
  createSubscription,
  createCustomer,
  createOrder,
  cancelSubscription,
  getSubscription,
  verifyPaymentSignature,
  isRazorpayConfigured
};
